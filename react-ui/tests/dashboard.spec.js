import { test, expect } from '@playwright/test';

test.describe('DyFlow React Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/test-static.html');
  });

  test('應該顯示加載動畫然後顯示Dashboard', async ({ page }) => {
    // 檢查加載動畫
    await expect(page.locator('text=DyFlow React UI')).toBeVisible();
    await expect(page.locator('text=正在初始化現代化界面...')).toBeVisible();
    
    // 等待加載完成，應該看到Dashboard
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=24/7 自動化流動性挖礦策略系統')).toBeVisible();
  });

  test('應該顯示系統狀態卡片', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 檢查系統狀態卡片
    await expect(page.locator('text=BSC 連接')).toBeVisible();
    await expect(page.locator('text=Solana 連接')).toBeVisible();
    await expect(page.locator('text=總池子數')).toBeVisible();
    await expect(page.locator('text=總 TVL')).toBeVisible();
  });

  test('應該顯示流動性池子表格', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 檢查池子表格
    await expect(page.locator('text=流動性池子')).toBeVisible();
    await expect(page.locator('text=實時監控的高收益LP池子')).toBeVisible();
    
    // 檢查表格標題
    await expect(page.locator('text=交易對')).toBeVisible();
    await expect(page.locator('text=網絡')).toBeVisible();
    await expect(page.locator('text=TVL')).toBeVisible();
    await expect(page.locator('text=APR')).toBeVisible();
    await expect(page.locator('text=24h 手續費')).toBeVisible();
    await expect(page.locator('text=風險等級')).toBeVisible();
    await expect(page.locator('text=操作')).toBeVisible();
  });

  test('應該有響應式設計', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 測試桌面視圖
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible();
    
    // 測試平板視圖
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible();
    
    // 測試手機視圖
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible();
  });

  test('應該有動畫效果', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 檢查是否有Framer Motion的動畫類
    const cards = page.locator('[class*="motion"]');
    await expect(cards.first()).toBeVisible();
  });

  test('查看按鈕應該可以點擊', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 等待一下讓數據加載
    await page.waitForTimeout(2000);
    
    // 查找查看按鈕
    const viewButtons = page.locator('button:has-text("查看")');
    const buttonCount = await viewButtons.count();
    
    if (buttonCount > 0) {
      // 如果有查看按鈕，測試第一個
      await expect(viewButtons.first()).toBeVisible();
      await expect(viewButtons.first()).toBeEnabled();
    }
  });

  test('應該顯示正確的Badge顏色', async ({ page }) => {
    // 等待Dashboard加載
    await expect(page.locator('text=DyFlow Dashboard')).toBeVisible({ timeout: 10000 });
    
    // 等待數據加載
    await page.waitForTimeout(2000);
    
    // 檢查是否有Badge元素
    const badges = page.locator('[class*="badge"]');
    if (await badges.count() > 0) {
      await expect(badges.first()).toBeVisible();
    }
  });
});
