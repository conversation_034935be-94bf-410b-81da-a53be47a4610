/**
 * 交易模式切換組件
 * 頂欄交易模式控制
 */

import React from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Play, 
  Pause, 
  LogOut,
  Settings
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'
import { setTradingMode } from '../../lib/wsHub'

export const ToggleMode: React.FC = () => {
  const tradingMode = useAgnoStore(state => state.tradingMode)
  const connectionStatus = useAgnoStore(state => state.connectionStatus)

  const handleModeChange = (mode: 'paused' | 'exit_only' | 'active') => {
    setTradingMode(mode)
  }

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case 'active': return <Play className="w-4 h-4" />
      case 'exit_only': return <LogOut className="w-4 h-4" />
      case 'paused': return <Pause className="w-4 h-4" />
      default: return <Settings className="w-4 h-4" />
    }
  }

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500 hover:bg-emerald-600'
      case 'exit_only': return 'bg-amber-500 hover:bg-amber-600'
      case 'paused': return 'bg-gray-500 hover:bg-gray-600'
      default: return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  const getModeText = (mode: string) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  const getConnectionColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-emerald-500'
      case 'connecting': return 'bg-amber-500 animate-pulse'
      case 'disconnected': return 'bg-gray-500'
      case 'error': return 'bg-rose-500'
      case 'failed': return 'bg-rose-600'
      default: return 'bg-gray-500'
    }
  }

  const getConnectionText = (status: string) => {
    switch (status) {
      case 'connected': return '已連接'
      case 'connecting': return '連接中'
      case 'disconnected': return '已斷線'
      case 'error': return '連接錯誤'
      case 'failed': return '連接失敗'
      default: return '未知'
    }
  }

  const isDisabled = connectionStatus !== 'connected'

  return (
    <div className="flex items-center space-x-4">
      {/* 連接狀態 */}
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${getConnectionColor(connectionStatus)}`}></div>
        <span className="text-sm text-gray-600">
          {getConnectionText(connectionStatus)}
        </span>
      </div>

      {/* 當前模式顯示 */}
      <Badge className={`${getModeColor(tradingMode)} text-white`}>
        {getModeIcon(tradingMode)}
        <span className="ml-1">{getModeText(tradingMode)}</span>
      </Badge>

      {/* 模式切換按鈕 */}
      <div className="flex items-center space-x-1">
        <Button
          size="sm"
          variant={tradingMode === 'paused' ? 'default' : 'outline'}
          onClick={() => handleModeChange('paused')}
          disabled={isDisabled}
          className="text-xs px-2 py-1"
        >
          <Pause className="w-3 h-3 mr-1" />
          暫停
        </Button>
        
        <Button
          size="sm"
          variant={tradingMode === 'exit_only' ? 'default' : 'outline'}
          onClick={() => handleModeChange('exit_only')}
          disabled={isDisabled}
          className="text-xs px-2 py-1"
        >
          <LogOut className="w-3 h-3 mr-1" />
          僅退出
        </Button>
        
        <Button
          size="sm"
          variant={tradingMode === 'active' ? 'default' : 'outline'}
          onClick={() => handleModeChange('active')}
          disabled={isDisabled}
          className="text-xs px-2 py-1"
        >
          <Play className="w-3 h-3 mr-1" />
          主動交易
        </Button>
      </div>

      {/* 模式說明 */}
      <div className="text-xs text-gray-500 max-w-xs">
        {tradingMode === 'active' && '系統將主動掃描並建立新的 LP 持倉'}
        {tradingMode === 'exit_only' && '系統僅處理現有持倉的退出，不建立新持倉'}
        {tradingMode === 'paused' && '系統暫停所有交易活動'}
      </div>
    </div>
  )
}
