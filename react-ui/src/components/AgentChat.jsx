import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { 
  MessageCircle, 
  Send, 
  Bot, 
  User, 
  Loader,
  Minimize2,
  Maximize2,
  X
} from 'lucide-react'

const AgentChat = ({ isAgentActive = false }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'agent',
      content: '您好！我是 DyFlow AI Agent。系統初始化完成後，我將為您提供智能投資管理服務。',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !isAgentActive) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      const response = await fetch('http://localhost:8001/api/agent/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          timestamp: new Date().toISOString()
        })
      })

      if (response.ok) {
        const result = await response.json()
        const agentMessage = {
          id: Date.now() + 1,
          type: 'agent',
          content: result.response || '收到您的指令，正在處理中...',
          timestamp: new Date()
        }
        setMessages(prev => [...prev, agentMessage])
      } else {
        throw new Error('Agent 響應失敗')
      }
    } catch (error) {
      console.error('發送消息失敗:', error)
      const errorMessage = {
        id: Date.now() + 1,
        type: 'agent',
        content: '抱歉，我暫時無法處理您的請求。請稍後再試。',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const quickCommands = [
    '分析當前市場機會',
    '查看投資組合狀態',
    '關閉所有LP倉位',
    '尋找高收益池子',
    '風險評估報告'
  ]

  if (isMinimized) {
    return (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full w-12 h-12 bg-purple-600 hover:bg-purple-700 shadow-lg"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 right-4 w-96 z-50"
    >
      <Card className="shadow-xl border-purple-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-purple-500" />
              <CardTitle className="text-lg">AI Agent 對話</CardTitle>
              {isAgentActive && (
                <Badge className="bg-green-100 text-green-800 text-xs">
                  在線
                </Badge>
              )}
            </div>
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(true)}
                className="h-6 w-6 p-0"
              >
                <Minimize2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <CardDescription>
            與 AI Agent 對話，發送投資指令和查詢
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 消息區域 */}
          <div className="h-64 overflow-y-auto space-y-3 p-2 bg-gray-50 rounded-lg">
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[80%] rounded-lg p-3 ${
                    message.type === 'user' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-white border border-gray-200'
                  }`}>
                    <div className="flex items-start space-x-2">
                      {message.type === 'agent' && (
                        <Bot className="h-4 w-4 mt-0.5 text-purple-500 flex-shrink-0" />
                      )}
                      <div className="flex-1">
                        <p className="text-sm">{message.content}</p>
                        <p className={`text-xs mt-1 ${
                          message.type === 'user' ? 'text-purple-200' : 'text-gray-500'
                        }`}>
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex justify-start"
              >
                <div className="bg-white border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4 text-purple-500" />
                    <Loader className="h-4 w-4 animate-spin text-purple-500" />
                    <span className="text-sm text-gray-500">Agent 正在思考...</span>
                  </div>
                </div>
              </motion.div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* 快速指令 */}
          {!isAgentActive && (
            <div className="space-y-2">
              <p className="text-xs text-gray-500">快速指令 (需要先啟動 Agent):</p>
              <div className="flex flex-wrap gap-1">
                {quickCommands.slice(0, 3).map((command, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    disabled={true}
                    className="text-xs h-6 px-2"
                  >
                    {command}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {isAgentActive && (
            <div className="space-y-2">
              <p className="text-xs text-gray-500">快速指令:</p>
              <div className="flex flex-wrap gap-1">
                {quickCommands.slice(0, 3).map((command, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setInputMessage(command)}
                    className="text-xs h-6 px-2"
                  >
                    {command}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* 輸入區域 */}
          <div className="flex space-x-2">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={isAgentActive ? "輸入指令或問題..." : "請先啟動 Agent..."}
              disabled={!isAgentActive || isLoading}
              className="flex-1 resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:bg-gray-100"
              rows={2}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || !isAgentActive || isLoading}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default AgentChat
