import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { motion } from 'framer-motion'
import { 
  Brain, Bot, Loader, CheckCircle, Square, Settings, 
  AlertTriangle, Play, Pause 
} from 'lucide-react'

const WorkflowControl = ({ workflowStatus, onWorkflowStart, onWorkflowReset }) => {
  const [isStarting, setIsStarting] = useState(false)
  const [workflowStep, setWorkflowStep] = useState('idle') // idle, scanning, configuring, executing, monitoring
  const [availablePools, setAvailablePools] = useState([])
  const [recommendedPools, setRecommendedPools] = useState([])
  const [selectedPools, setSelectedPools] = useState([])
  const [currentAgent, setCurrentAgent] = useState(null)

  const startWorkflow = async () => {
    setIsStarting(true)
    setWorkflowStep('scanning')
    setCurrentAgent('MarketIntelAgent')
    
    try {
      console.log('🚀 啟動 LP 投資工作流程...')
      
      // Step 1: MarketIntelAgent 掃描池子
      console.log('📊 MarketIntelAgent 正在掃描可用池子...')
      const poolsResponse = await fetch('http://localhost:8001/api/pools')
      const poolsData = await poolsResponse.json()
      setAvailablePools(poolsData.pools || [])
      
      // 模擬掃描時間
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Step 2: PortfolioManagerAgent 分析推薦
      setWorkflowStep('configuring')
      setCurrentAgent('PortfolioManagerAgent')
      console.log('🎯 PortfolioManagerAgent 正在分析投資組合配置...')
      
      // 篩選高收益、低風險的池子
      const recommended = (poolsData.pools || []).filter(pool => 
        pool.apr > 100 && pool.tvl_usd > 5000000
      )
      setRecommendedPools(recommended)
      
      // 模擬分析時間
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      console.log('✅ 工作流程準備完成，等待用戶確認投資配置')
      
    } catch (error) {
      console.error('❌ 工作流程啟動失敗:', error)
      setWorkflowStep('idle')
      setCurrentAgent(null)
    } finally {
      setIsStarting(false)
    }
  }

  const executeInvestment = async () => {
    setWorkflowStep('executing')
    setCurrentAgent('ExecutionAgent')
    
    try {
      console.log('💰 ExecutionAgent 正在執行 LP 投資...')
      console.log('選中的池子:', selectedPools)
      
      // 模擬執行投資
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Step 3: 進入風控監控
      setWorkflowStep('monitoring')
      setCurrentAgent('RiskSentinelAgent')
      console.log('🛡️ RiskSentinelAgent 開始風控監控...')
      
      console.log('✅ LP 投資執行完成，進入持續監控模式')
      
    } catch (error) {
      console.error('❌ 投資執行失敗:', error)
      setWorkflowStep('configuring')
    }
  }

  const resetWorkflow = () => {
    setWorkflowStep('idle')
    setCurrentAgent(null)
    setAvailablePools([])
    setRecommendedPools([])
    setSelectedPools([])
  }

  const handlePoolSelection = (pool, selected) => {
    if (selected) {
      setSelectedPools([...selectedPools, pool])
    } else {
      setSelectedPools(selectedPools.filter(p => p.id !== pool.id))
    }
  }

  const getWorkflowStatusBadge = () => {
    switch (workflowStep) {
      case 'idle':
        return (
          <Badge variant="outline">
            <Square className="h-3 w-3 mr-1" />
            等待啟動
          </Badge>
        )
      case 'scanning':
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <Loader className="h-3 w-3 mr-1 animate-spin" />
            掃描池子中
          </Badge>
        )
      case 'configuring':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Settings className="h-3 w-3 mr-1" />
            等待配置確認
          </Badge>
        )
      case 'executing':
        return (
          <Badge className="bg-purple-100 text-purple-800">
            <Loader className="h-3 w-3 mr-1 animate-spin" />
            執行投資中
          </Badge>
        )
      case 'monitoring':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            風控監控中
          </Badge>
        )
      default:
        return <Badge variant="outline">未知狀態</Badge>
    }
  }

  const getCurrentAgentInfo = () => {
    switch (workflowStep) {
      case 'scanning':
        return { name: 'MarketIntelAgent', action: '正在掃描可用的 LP 池子...' }
      case 'configuring':
        return { name: 'PortfolioManagerAgent', action: '分析投資組合配置，等待用戶確認' }
      case 'executing':
        return { name: 'ExecutionAgent', action: '正在執行 LP 投資交易...' }
      case 'monitoring':
        return { name: 'RiskSentinelAgent', action: '持續監控風險指標和 IL 變化' }
      default:
        return { name: '無', action: '點擊啟動開始 LP 投資流程' }
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-purple-500" />
          <span>AI Agent 工作流程</span>
        </CardTitle>
        <CardDescription>
          啟動 Agents 開始 LP 投資流程
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 當前工作流程狀態 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium">當前狀態</span>
            {getWorkflowStatusBadge()}
          </div>
          
          {/* 當前 Agent 信息 */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Bot className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">當前 Agent: {getCurrentAgentInfo().name}</span>
            </div>
            <p className="text-xs text-gray-600">{getCurrentAgentInfo().action}</p>
          </div>
        </div>

        {/* 啟動按鈕 */}
        {workflowStep === 'idle' && (
          <div className="mb-6">
            <Button
              onClick={startWorkflow}
              disabled={isStarting}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              {isStarting ? (
                <Loader className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Bot className="h-4 w-4 mr-2" />
              )}
              {isStarting ? '啟動中...' : '啟動 LP 投資流程'}
            </Button>
          </div>
        )}

        {/* 池子配置界面 */}
        {workflowStep === 'configuring' && recommendedPools.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-3">推薦的 LP 池子配置</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {recommendedPools.map(pool => (
                <div key={pool.id} className="flex items-center space-x-2 p-2 border rounded">
                  <input
                    type="checkbox"
                    checked={selectedPools.some(p => p.id === pool.id)}
                    onChange={(e) => handlePoolSelection(pool, e.target.checked)}
                    className="rounded"
                  />
                  <div className="flex-1">
                    <div className="text-xs font-medium">{pool.pair}</div>
                    <div className="text-xs text-gray-500">
                      {pool.chain.toUpperCase()} • APR: {pool.apr.toFixed(1)}% • TVL: ${(pool.tvl_usd/1000000).toFixed(1)}M
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {selectedPools.length > 0 && (
              <Button
                onClick={executeInvestment}
                className="w-full mt-3 bg-green-600 hover:bg-green-700"
              >
                確認投資 ({selectedPools.length} 個池子)
              </Button>
            )}
          </div>
        )}

        {/* 重置按鈕 */}
        {workflowStep !== 'idle' && workflowStep !== 'executing' && (
          <div className="flex justify-center">
            <Button
              onClick={resetWorkflow}
              variant="outline"
              size="sm"
            >
              重置流程
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default WorkflowControl
