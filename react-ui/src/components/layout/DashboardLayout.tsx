/**
 * DyFlow v3.3 Dashboard 佈局
 * 新版資訊架構：解決 5 個痛點的重新佈局
 */

import React, { useEffect } from 'react'
import { ToggleMode } from '../common/ToggleMode'
import { SystemOverview } from '../kpi/SystemOverview'
import { HotPoolTable } from '../pools/HotPoolTable'
import { PositionList } from '../lp/PositionList'
import { FlowTimeline } from '../agents/FlowTimeline'
import { RiskPanel } from '../risk/RiskPanel'
import { InfraStatus } from '../health/InfraStatus'
import { useAgnoStore } from '../../store/useAgno'

export const DashboardLayout: React.FC = () => {
  const connectionStatus = useAgnoStore(state => state.connectionStatus)

  // 初始化 WebSocket 連接在 wsHub 中自動處理
  useEffect(() => {
    // WebSocket 連接已在 wsHub 中自動建立
    console.log('🎯 DashboardLayout mounted, WebSocket connection status:', connectionStatus)
  }, [connectionStatus])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂欄：連線狀態 / TradingMode Toggle */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-800">DyFlow v3.3</h1>
            <span className="text-sm text-gray-500">24/7 自動化多 Agent LP 策略系統</span>
          </div>
          
          <ToggleMode />
        </div>
      </header>

      {/* 主要內容區 - DyFlow v3.3 12-col Grid 佈局 */}
      <main className="p-6">
        <div className="grid grid-cols-12 gap-4">
          {/* SystemOverview (col-4 row-2) */}
          <section className="col-span-12 lg:col-span-4 row-span-2">
            <SystemOverview />
          </section>

          {/* 右側空白區域或額外信息 */}
          <div className="col-span-12 lg:col-span-8 row-span-2">
            {/* 可以放置額外的 KPI 或快速操作 */}
          </div>

          {/* HotPools (col-4 row-3) */}
          <section className="col-span-12 lg:col-span-4 row-span-3">
            <HotPoolTable />
          </section>

          {/* AgentTimeline (col-4 row-8) */}
          <section className="col-span-12 lg:col-span-4 row-span-8">
            <FlowTimeline />
          </section>

          {/* Positions (col-4 row-3) */}
          <section className="col-span-12 lg:col-span-4 row-span-3">
            <PositionList />
          </section>

          {/* RiskPanel (col-4 row-3) */}
          <section className="col-span-12 lg:col-span-4 row-span-3">
            <RiskPanel />
          </section>

          {/* InfraStatus (col-4 row-3) */}
          <section className="col-span-12 lg:col-span-4 row-span-3">
            <InfraStatus />
          </section>
        </div>
      </main>

      {/* 連接狀態指示器 */}
      {connectionStatus !== 'connected' && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`rounded-lg px-4 py-2 text-white text-sm shadow-lg ${
            connectionStatus === 'connecting' ? 'bg-amber-500' :
            connectionStatus === 'disconnected' ? 'bg-gray-500' :
            connectionStatus === 'error' ? 'bg-rose-500' :
            connectionStatus === 'failed' ? 'bg-rose-600' : 'bg-gray-500'
          }`}>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connecting' ? 'bg-white animate-pulse' : 'bg-white'
              }`}></div>
              <span>
                {connectionStatus === 'connecting' ? '正在連接...' :
                 connectionStatus === 'disconnected' ? '連接已斷開' :
                 connectionStatus === 'error' ? '連接錯誤' :
                 connectionStatus === 'failed' ? '連接失敗' : '連接異常'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
