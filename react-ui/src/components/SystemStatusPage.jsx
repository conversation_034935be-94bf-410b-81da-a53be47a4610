import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import {
  Activity, AlertTriangle, TrendingUp, Shield,
  Wallet, Database, Wifi, Bot, DollarSign,
  Target, BarChart3, Clock, Square
} from 'lucide-react'

const SystemStatusPage = () => {
  const [agentStatus, setAgentStatus] = useState({})
  const [lpPositions, setLpPositions] = useState([])
  const [riskMetrics, setRiskMetrics] = useState({})
  const [systemHealth, setSystemHealth] = useState({})
  const [realtimeData, setRealtimeData] = useState({})
  const [portfolioStrategy, setPortfolioStrategy] = useState({})
  const [systemRunning, setSystemRunning] = useState(false)

  useEffect(() => {
    // 模擬 PortfolioManagerAgent 策略和持倉狀態
    const mockPortfolioStrategy = {
      originalPlan: {
        title: "DyFlow v3.3 自動化 LP 策略",
        objective: "通過多鏈 LP 挖礦實現穩定收益，同時控制無常損失風險",
        riskManagement: {
          ilFuse: "-8%",
          varLimit: "4%",
          maxDrawdown: "-10%",
          dailyHarvest: "UTC 02:00"
        },
        allocationPrinciple: "50% 低風險穩定幣對 + 30% 中風險主流幣對 + 20% 高風險機會池"
      },
      currentPositions: [
        {
          pair: "SOL/USDC",
          chain: "Solana",
          strategy: "SPOT_BALANCED",
          strategyDesc: "平衡現貨策略 - 均勻分佈流動性",
          allocation: 50000,
          currentValue: 52300,
          performance: "+4.6%",
          riskLevel: "中等",
          status: "執行中"
        },
        {
          pair: "BNB/USDT",
          chain: "BSC",
          strategy: "CURVE_BALANCED",
          strategyDesc: "曲線平衡策略 - 集中流動性於當前價格",
          allocation: 30000,
          currentValue: 31200,
          performance: "+4.0%",
          riskLevel: "低",
          status: "執行中"
        },
        {
          pair: "CAKE/BNB",
          chain: "BSC",
          strategy: "BID_ASK_BALANCED",
          strategyDesc: "買賣平衡策略 - 雙邊掛單優化",
          allocation: 20000,
          currentValue: 19400,
          performance: "-3.0%",
          riskLevel: "高",
          status: "風險警告"
        }
      ],
      executionSummary: {
        totalAllocated: 100000,
        currentValue: 102900,
        totalReturn: "+2.9%",
        strategiesActive: 3,
        riskStatus: "正常"
      }
    }

    // 簡化的 Agent 狀態 - 只顯示關鍵信息
    const mockAgentStatus = {
      PortfolioManagerAgent: {
        status: 'active',
        message: '正在分析市場機會，準備調整投資組合配置',
        action: '與用戶對話中'
      },
      ExecutionAgent: {
        status: 'standby',
        message: '等待 PortfolioManager 的執行指令',
        action: '待命'
      },
      StrategyAgent: {
        status: 'configuring',
        message: '為 SOL/USDC 池配置 SPOT_BALANCED 策略',
        action: '策略配置'
      },
      RiskSentinelAgent: {
        status: 'monitoring',
        message: '所有持倉風險正常，IL < 5%',
        action: '風險監控'
      }
    }

    // 模擬當前 LP 持倉
    const mockLpPositions = [
      {
        id: 1,
        pair: 'SOL/USDC',
        chain: 'Solana',
        invested: 50000,
        currentValue: 52300,
        pnl: 2300,
        pnlPercent: 4.6,
        apr: 156.8,
        il: -1.2,
        status: 'active'
      },
      {
        id: 2,
        pair: 'BNB/USDT', 
        chain: 'BSC',
        invested: 30000,
        currentValue: 31200,
        pnl: 1200,
        pnlPercent: 4.0,
        apr: 45.2,
        il: -0.8,
        status: 'active'
      },
      {
        id: 3,
        pair: 'CAKE/BNB',
        chain: 'BSC', 
        invested: 20000,
        currentValue: 19400,
        pnl: -600,
        pnlPercent: -3.0,
        apr: 78.5,
        il: -4.2,
        status: 'warning'
      }
    ]

    // 模擬風險指標
    const mockRiskMetrics = {
      totalIL: -2.1,
      var95: 3.2,
      maxDrawdown: -5.8,
      riskScore: 7.2,
      healthScore: 92
    }

    // 模擬系統健康狀態
    const mockSystemHealth = {
      apis: { meteora: true, pancakeswap: true, coingecko: true },
      wallet: { connected: true, balance: 125000 },
      database: { connected: true, latency: 45 },
      network: { bsc: true, solana: true }
    }

    // 模擬實時收益數據
    const mockRealtimeData = {
      todayPnl: 2900,
      totalPnl: 15600,
      todayFees: 450,
      totalFees: 3200,
      totalInvested: 100000,
      totalValue: 102900
    }

    setAgentStatus(mockAgentStatus)
    setLpPositions(mockLpPositions)
    setRiskMetrics(mockRiskMetrics)
    setSystemHealth(mockSystemHealth)
    setRealtimeData(mockRealtimeData)

    // 新增 Portfolio 策略狀態
    setPortfolioStrategy(mockPortfolioStrategy)

    // 每 3 秒更新一次數據
    const interval = setInterval(() => {
      // 更新 Agent 狀態
      setAgentStatus(prev => {
        const updated = { ...prev }
        Object.keys(updated).forEach(agent => {
          if (updated[agent].status === 'active') {
            updated[agent].progress = Math.min(100, updated[agent].progress + Math.random() * 10)
            updated[agent].lastUpdate = `${Math.floor(Math.random() * 10)} 秒前`
          }
        })
        return updated
      })
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const getAgentStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800">活躍</Badge>
      case 'monitoring':
        return <Badge className="bg-green-100 text-green-800">監控中</Badge>
      case 'standby':
        return <Badge className="bg-gray-100 text-gray-800">待命</Badge>
      case 'configuring':
        return <Badge className="bg-purple-100 text-purple-800">配置中</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }



  const getPositionStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
      case 'danger':
        return <Badge className="bg-red-100 text-red-800">風險</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const startWorkflow = async () => {
    try {
      setSystemRunning(true)
      // 調用真實的 Agno Workflow API
      const response = await fetch('http://localhost:8001/api/workflow/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (response.ok) {
        setAgentStatus(prev => ({
          ...prev,
          PortfolioManagerAgent: {
            ...prev.PortfolioManagerAgent,
            status: 'active',
            message: 'AI 系統已啟動，開始分析市場和配置投資組合',
            action: '全自動運行中'
          }
        }))
      }
    } catch (error) {
      console.error('啟動 Workflow 失敗:', error)
      setSystemRunning(false)
    }
  }

  const stopWorkflow = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/workflow/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      setSystemRunning(false)
      setAgentStatus(prev => ({
        ...prev,
        PortfolioManagerAgent: {
          ...prev.PortfolioManagerAgent,
          status: 'standby',
          message: '系統已停止，等待用戶指令',
          action: '手動模式'
        }
      }))
    } catch (error) {
      console.error('停止 Workflow 失敗:', error)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左側 - PortfolioManagerAgent 核心控制 */}
      <div className="space-y-6">
        {/* 統一的系統控制 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-purple-500" />
              <span>DyFlow AI 投資管理系統</span>
            </CardTitle>
            <CardDescription>
              PortfolioManagerAgent 主導的全自動 LP 投資管理
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 系統狀態和控制 */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="font-medium text-lg">系統狀態</div>
                  <div className="text-sm text-gray-600">
                    {systemRunning ? '🤖 AI 全自動運行中' : '👤 手動模式'}
                  </div>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  systemRunning
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {systemRunning ? 'ACTIVE' : 'STANDBY'}
                </div>
              </div>

              <div className="flex space-x-3">
                {!systemRunning ? (
                  <button
                    onClick={startWorkflow}
                    className="flex-1 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2 font-medium"
                  >
                    <Bot className="h-5 w-5" />
                    <span>啟動 AI 管理</span>
                  </button>
                ) : (
                  <button
                    onClick={stopWorkflow}
                    className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center justify-center space-x-2 font-medium"
                  >
                    <Square className="h-5 w-5" />
                    <span>停止自動化</span>
                  </button>
                )}
                <button className="px-4 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>重置</span>
                </button>
              </div>
            </div>

            {/* PortfolioManagerAgent 狀態 */}
            <div className="p-3 border rounded-lg bg-blue-50">
              <div className="flex items-center space-x-2 mb-2">
                <Bot className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">PortfolioManagerAgent</span>
                {getAgentStatusBadge(agentStatus.PortfolioManagerAgent?.status)}
              </div>
              <p className="text-sm text-blue-700">
                {agentStatus.PortfolioManagerAgent?.message}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* PortfolioManagerAgent 策略規劃 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <span>PortfolioManager 策略規劃</span>
            </CardTitle>
            <CardDescription>
              原始計劃、當前持倉與策略執行狀況
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 原始策略計劃 */}
            <div className="border rounded-lg p-3 bg-blue-50">
              <div className="font-medium text-blue-800 mb-2">
                {portfolioStrategy.originalPlan?.title}
              </div>
              <div className="text-sm text-blue-700 mb-3">
                {portfolioStrategy.originalPlan?.objective}
              </div>

              {/* 風險管理計劃 */}
              <div className="mb-3">
                <div className="text-xs font-medium text-blue-800 mb-1">風險管理計劃</div>
                <div className="grid grid-cols-2 gap-2 text-xs text-blue-700">
                  <div>IL 熔斷: {portfolioStrategy.originalPlan?.riskManagement.ilFuse}</div>
                  <div>VaR 限制: {portfolioStrategy.originalPlan?.riskManagement.varLimit}</div>
                  <div>最大回撤: {portfolioStrategy.originalPlan?.riskManagement.maxDrawdown}</div>
                  <div>收割時間: {portfolioStrategy.originalPlan?.riskManagement.dailyHarvest}</div>
                </div>
              </div>

              {/* 資金分配原則 */}
              <div className="text-xs text-blue-700">
                <span className="font-medium">分配原則: </span>
                {portfolioStrategy.originalPlan?.allocationPrinciple}
              </div>
            </div>

            {/* 當前持倉與策略 */}
            <div>
              <h4 className="font-medium mb-2 flex items-center">
                <BarChart3 className="h-4 w-4 mr-1" />
                當前持倉與 LP 策略
              </h4>
              <div className="space-y-3">
                {portfolioStrategy.currentPositions?.map((position, index) => (
                  <div key={index} className="border rounded-lg p-3 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{position.pair}</span>
                          <Badge variant="outline" className="text-xs">{position.chain}</Badge>
                          <Badge className={`text-xs ${
                            position.status === '執行中' ? 'bg-green-100 text-green-800' :
                            position.status === '風險警告' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {position.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          <span className="font-medium">{position.strategy}</span> - {position.strategyDesc}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${
                          position.performance.startsWith('+') ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {position.performance}
                        </div>
                        <div className="text-xs text-gray-500">
                          風險: {position.riskLevel}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>投入: ${position.allocation.toLocaleString()}</span>
                      <span>當前: ${position.currentValue.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 執行總結 */}
            <div className="border rounded-lg p-3 bg-green-50">
              <div className="font-medium text-green-800 mb-2">策略執行總結</div>
              <div className="grid grid-cols-2 gap-2 text-sm text-green-700">
                <div>總投入: ${portfolioStrategy.executionSummary?.totalAllocated.toLocaleString()}</div>
                <div>當前價值: ${portfolioStrategy.executionSummary?.currentValue.toLocaleString()}</div>
                <div>總收益: {portfolioStrategy.executionSummary?.totalReturn}</div>
                <div>活躍策略: {portfolioStrategy.executionSummary?.strategiesActive} 個</div>
              </div>
              <div className="mt-2 text-xs text-green-600">
                風險狀態: {portfolioStrategy.executionSummary?.riskStatus}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 其他 Agent 狀態 - 簡化顯示 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-500" />
              <span>協作 Agent 狀態</span>
            </CardTitle>
            <CardDescription>
              其他 Agent 配合 PortfolioManager 工作
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {Object.entries(agentStatus)
              .filter(([name]) => name !== 'PortfolioManagerAgent')
              .map(([agentName, data]) => (
              <div key={agentName} className="flex items-center justify-between p-2 border rounded text-sm">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{agentName.replace('Agent', '')}</span>
                  {getAgentStatusBadge(data.status)}
                </div>
                <div className="text-xs text-gray-600">{data.action}</div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 系統健康監控 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-500" />
              <span>系統健康監控</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* API 連接狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Wifi className="h-4 w-4 mr-1" />
                API 連接狀態
              </h4>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.meteora ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">Meteora</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.pancakeswap ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">PancakeSwap</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${systemHealth.apis?.coingecko ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-xs">CoinGecko</span>
                </div>
              </div>
            </div>

            {/* 錢包狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Wallet className="h-4 w-4 mr-1" />
                錢包狀態
              </h4>
              <div className="flex justify-between text-sm">
                <span>連接狀態:</span>
                <span className={systemHealth.wallet?.connected ? 'text-green-600' : 'text-red-600'}>
                  {systemHealth.wallet?.connected ? '已連接' : '未連接'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>餘額:</span>
                <span>${systemHealth.wallet?.balance?.toLocaleString()}</span>
              </div>
            </div>

            {/* 數據庫狀態 */}
            <div>
              <h4 className="text-sm font-medium mb-2 flex items-center">
                <Database className="h-4 w-4 mr-1" />
                數據庫狀態
              </h4>
              <div className="flex justify-between text-sm">
                <span>連接:</span>
                <span className={systemHealth.database?.connected ? 'text-green-600' : 'text-red-600'}>
                  {systemHealth.database?.connected ? '正常' : '異常'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>延遲:</span>
                <span>{systemHealth.database?.latency}ms</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右側 - 關鍵業務信息 */}
      <div className="space-y-6">
        {/* 實時收益概覽 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span>實時收益概覽</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  +${realtimeData.todayPnl?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">今日收益</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  +${realtimeData.totalPnl?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">總收益</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold">
                  ${realtimeData.todayFees?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">今日手續費</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold">
                  ${realtimeData.totalFees?.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">總手續費</div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span>總投入:</span>
                <span>${realtimeData.totalInvested?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm font-medium">
                <span>當前價值:</span>
                <span>${realtimeData.totalValue?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>總收益率:</span>
                <span className="text-green-600">
                  +{((realtimeData.totalValue - realtimeData.totalInvested) / realtimeData.totalInvested * 100).toFixed(2)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 當前 LP 持倉 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-500" />
              <span>當前 LP 持倉</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {lpPositions.map(position => (
                <div key={position.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{position.pair}</span>
                        <Badge variant="outline" className="text-xs">{position.chain}</Badge>
                        {getPositionStatusBadge(position.status)}
                      </div>
                      <div className="text-sm text-gray-500">
                        APR: {position.apr}% | IL: {position.il}%
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${position.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {position.pnl >= 0 ? '+' : ''}${position.pnl.toLocaleString()}
                      </div>
                      <div className={`text-sm ${position.pnlPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent}%
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>投入: ${position.invested.toLocaleString()}</span>
                    <span>當前: ${position.currentValue.toLocaleString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 風險監控 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-orange-500" />
              <span>風險監控</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-500">總 IL</div>
                <div className={`text-lg font-semibold ${riskMetrics.totalIL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {riskMetrics.totalIL}%
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">VaR 95%</div>
                <div className="text-lg font-semibold">{riskMetrics.var95}%</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">最大回撤</div>
                <div className="text-lg font-semibold text-red-600">{riskMetrics.maxDrawdown}%</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">風險評分</div>
                <div className="text-lg font-semibold">{riskMetrics.riskScore}/10</div>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>系統健康度</span>
                <span>{riskMetrics.healthScore}%</span>
              </div>
              <Progress value={riskMetrics.healthScore} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SystemStatusPage
