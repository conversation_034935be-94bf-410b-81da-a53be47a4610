import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Clock,
  AlertTriangle,
  ExternalLink,
  Target
} from 'lucide-react'
import { useAgnoStore } from '@/store/useAgno'

const BSCPositionList: React.FC = () => {
  const { positions } = useAgnoStore()

  // 過濾 BSC 持倉
  const bscPositions = positions.filter(pos => pos.chain === 'bsc')

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1e6) return `$${(num / 1e6).toFixed(decimals)}M`
    if (num >= 1e3) return `$${(num / 1e3).toFixed(decimals)}K`
    return `$${num.toFixed(decimals)}`
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'exiting': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-blue-100 text-blue-800'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return 'bg-blue-100 text-blue-800'
      case 'CURVE_BALANCED': return 'bg-green-100 text-green-800'
      case 'BID_ASK_BALANCED': return 'bg-purple-100 text-purple-800'
      case 'SPOT_IMBALANCED_DAMM': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getILProgressColor = (ilPct: number) => {
    if (ilPct >= -2) return 'bg-green-500'
    if (ilPct >= -5) return 'bg-yellow-500'
    if (ilPct >= -8) return 'bg-orange-500'
    return 'bg-red-500'
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold">BSC LP 持倉</h3>
          <Badge variant="outline">{bscPositions.length} 個持倉</Badge>
        </div>
        
        {bscPositions.length > 0 && (
          <div className="text-sm text-gray-600">
            總流動性: {formatNumber(bscPositions.reduce((sum, pos) => sum + pos.liquidityUsd, 0))}
          </div>
        )}
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {bscPositions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暫無 BSC LP 持倉</p>
            <p className="text-sm">建立第一個 LP 持倉開始賺取手續費</p>
          </div>
        ) : (
          bscPositions.map((position) => (
            <Card key={position.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* 頭部信息 */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-lg">{position.pool}</h4>
                      <Badge className="bg-yellow-100 text-yellow-800">BSC</Badge>
                      <Badge 
                        variant="outline" 
                        className={getStatusColor(position.status)}
                      >
                        {position.status === 'active' ? '活躍' : 
                         position.status === 'exiting' ? '退出中' : '已關閉'}
                      </Badge>
                      {position.exitAsset && (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          退出資產: {position.exitAsset}
                        </Badge>
                      )}
                    </div>
                    
                    <Button size="sm" variant="outline">
                      退出
                    </Button>
                  </div>

                  {/* 策略和基本信息 */}
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="outline" 
                      className={getStrategyColor(position.strategy)}
                    >
                      {position.strategy}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {position.strategy === 'CURVE_BALANCED' ? '曲線分布' :
                       position.strategy === 'SPOT_BALANCED' ? '均衡分布' :
                       position.strategy === 'BID_ASK_BALANCED' ? '買賣分布' : '單邊流動性'}
                    </span>
                  </div>

                  {/* 財務指標 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">流動性</span>
                        <span className="font-medium">{formatNumber(position.liquidityUsd)}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">PnL</span>
                        <div className="flex items-center space-x-1">
                          {position.pnlPct >= 0 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span className={`font-medium ${position.pnlPct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {position.pnlPct >= 0 ? '+' : ''}{position.pnlPct.toFixed(2)}%
                          </span>
                          <span className="text-sm text-gray-500">
                            ({formatNumber(position.pnlUsd)})
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">總 APR</span>
                        <span className="font-medium text-green-600">{position.apr.toFixed(1)}%</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Fee APR</span>
                        <span className="font-medium">{position.feeApr.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  {/* 無常損失進度條 */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">無常損失</span>
                      <span className="font-medium">{position.ilPct.toFixed(2)}%</span>
                    </div>
                    
                    <div className="relative">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${getILProgressColor(position.ilPct)}`}
                          style={{ 
                            width: `${Math.min(Math.abs(position.ilPct) / 10 * 100, 100)}%` 
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>-2%</span>
                        <span>-5%</span>
                        <span className="text-red-500">-8%</span>
                        <span>-10%</span>
                      </div>
                    </div>
                  </div>

                  {/* 時間信息 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <div>
                        <div className="text-sm text-gray-600">持倉時間</div>
                        <div className="font-medium">{formatTime(position.holdingTime)}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      <div>
                        <div className="text-sm text-gray-600">預計退出</div>
                        <div className="font-medium">{formatTime(position.countdown)}</div>
                      </div>
                    </div>
                  </div>

                  {/* 24h 手續費 */}
                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center space-x-1">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-gray-600">24h 手續費:</span>
                    </div>
                    <span className="font-medium text-green-600">{formatNumber(position.fees24hUsd)}</span>
                  </div>

                  {/* 操作按鈕 */}
                  <div className="flex items-center justify-between pt-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(`https://pancakeswap.finance/v3/pools/${position.id}`, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      查看池子詳情
                    </Button>
                    
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        收割手續費
                      </Button>
                      <Button size="sm" variant="destructive">
                        緊急退出
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}

export default BSCPositionList
