/**
 * LP 持倉列表
 * 管理所有 LP 持倉，支持篩選和排序
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Filter, 
  SortAsc, 
  SortDesc, 
  AlertTriangle,
  TrendingUp,
  Activity
} from 'lucide-react'
import { PositionCard } from './PositionCard'
import { useAgnoStore } from '../../store/useAgno'
import { emergencyExit } from '../../lib/wsHub'

type SortField = 'apr' | 'pnl' | 'liquidity' | 'il' | 'countdown'
type SortDirection = 'asc' | 'desc'
type FilterStatus = 'all' | 'active' | 'exiting' | 'closed'

export const PositionList: React.FC = () => {
  const positions = useAgnoStore(state => state.positions)
  const [sortField, setSortField] = useState<SortField>('apr')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('all')

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const handleExitPosition = (positionId: string) => {
    // 這裡可以發送單個持倉退出命令
    console.log('Exit position:', positionId)
    // 暫時使用緊急退出作為示例
    emergencyExit()
  }

  const handleEmergencyExitAll = () => {
    emergencyExit()
  }

  // 篩選和排序
  const filteredAndSortedPositions = positions
    .filter(position => {
      if (filterStatus === 'all') return true
      return position.status === filterStatus
    })
    .sort((a, b) => {
      let aValue: number, bValue: number
      
      switch (sortField) {
        case 'apr':
          aValue = a.apr
          bValue = b.apr
          break
        case 'pnl':
          aValue = a.pnlPct
          bValue = b.pnlPct
          break
        case 'liquidity':
          aValue = a.liquidityUsd
          bValue = b.liquidityUsd
          break
        case 'il':
          aValue = a.ilPct
          bValue = b.ilPct
          break
        case 'countdown':
          aValue = a.countdown
          bValue = b.countdown
          break
        default:
          return 0
      }
      
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    })

  const activePositions = positions.filter(p => p.status === 'active')
  const exitingPositions = positions.filter(p => p.status === 'exiting')
  const closedPositions = positions.filter(p => p.status === 'closed')

  const totalLiquidity = activePositions.reduce((sum, p) => sum + p.liquidityUsd, 0)
  const totalPnL = activePositions.reduce((sum, p) => sum + p.pnlUsd, 0)
  const avgAPR = activePositions.length > 0 
    ? activePositions.reduce((sum, p) => sum + p.apr, 0) / activePositions.length 
    : 0

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" />
            <span>LP 持倉管理</span>
          </CardTitle>
          
          {activePositions.length > 0 && (
            <Button
              size="sm"
              variant="destructive"
              onClick={handleEmergencyExitAll}
              className="text-xs"
            >
              <AlertTriangle className="w-3 h-3 mr-1" />
              緊急退出全部
            </Button>
          )}
        </div>
        
        {/* 持倉統計 */}
        <div className="grid grid-cols-3 gap-3 mt-3">
          <div className="bg-blue-50 rounded-lg p-2">
            <div className="text-xs text-blue-600">總流動性</div>
            <div className="text-sm font-bold text-blue-800">
              {formatCurrency(totalLiquidity)}
            </div>
          </div>
          <div className={`rounded-lg p-2 ${totalPnL >= 0 ? 'bg-emerald-50' : 'bg-rose-50'}`}>
            <div className={`text-xs ${totalPnL >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
              總 PnL
            </div>
            <div className={`text-sm font-bold ${totalPnL >= 0 ? 'text-emerald-800' : 'text-rose-800'}`}>
              {formatCurrency(totalPnL)}
            </div>
          </div>
          <div className="bg-amber-50 rounded-lg p-2">
            <div className="text-xs text-amber-600">平均 APR</div>
            <div className="text-sm font-bold text-amber-800">
              {avgAPR.toFixed(1)}%
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 篩選和排序控制 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <div className="flex space-x-1">
              {(['all', 'active', 'exiting', 'closed'] as FilterStatus[]).map(status => (
                <Button
                  key={status}
                  size="sm"
                  variant={filterStatus === status ? "default" : "ghost"}
                  onClick={() => setFilterStatus(status)}
                  className="text-xs px-2 py-1"
                >
                  {status === 'all' ? '全部' : 
                   status === 'active' ? '活躍' :
                   status === 'exiting' ? '退出中' : '已關閉'}
                  {status !== 'all' && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {status === 'active' ? activePositions.length :
                       status === 'exiting' ? exitingPositions.length :
                       closedPositions.length}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            {(['apr', 'pnl', 'liquidity', 'il'] as SortField[]).map(field => (
              <Button
                key={field}
                size="sm"
                variant={sortField === field ? "default" : "ghost"}
                onClick={() => handleSort(field)}
                className="text-xs px-2 py-1"
              >
                {field === 'apr' ? 'APR' :
                 field === 'pnl' ? 'PnL' :
                 field === 'liquidity' ? '流動性' : 'IL'}
                {sortField === field && (
                  sortDirection === 'asc' ? 
                    <SortAsc className="w-3 h-3 ml-1" /> : 
                    <SortDesc className="w-3 h-3 ml-1" />
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* 持倉卡片列表 */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredAndSortedPositions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Activity className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">
                {filterStatus === 'all' ? '暫無持倉' : `暫無${filterStatus === 'active' ? '活躍' : filterStatus === 'exiting' ? '退出中' : '已關閉'}持倉`}
              </p>
            </div>
          ) : (
            filteredAndSortedPositions.map(position => (
              <PositionCard
                key={position.id}
                position={position}
                onExit={handleExitPosition}
              />
            ))
          )}
        </div>

        {/* 快速操作 */}
        {activePositions.length > 0 && (
          <div className="pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                {activePositions.length} 個活躍持倉
              </span>
              <div className="flex space-x-2">
                <Button size="sm" variant="outline" className="text-xs">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  批量收割
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
