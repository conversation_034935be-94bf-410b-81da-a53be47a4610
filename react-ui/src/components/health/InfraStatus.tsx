/**
 * 基礎設施健康監控
 * 解決痛點⑤：健康監控藏最下面
 * 移到視窗可視範圍，綠→黃→紅即時變色
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { 
  Wifi, 
  Database, 
  Globe, 
  Server,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

export const InfraStatus: React.FC = () => {
  const infraHealth = useAgnoStore(state => state.infraHealth)

  // 計算系統健康狀態
  const healthServices = Object.values(infraHealth)
  const connectedCount = healthServices.filter(s => s.status === 'connected').length
  const totalCount = healthServices.length
  const healthPct = (connectedCount / totalCount) * 100

  let status: 'healthy' | 'warning' | 'critical'
  if (healthPct >= 80) status = 'healthy'
  else if (healthPct >= 60) status = 'warning'
  else status = 'critical'

  const systemHealth = {
    status,
    healthPct,
    connectedCount,
    totalCount
  }

  const getStatusIcon = (status: string, latency: number) => {
    if (status === 'connected') {
      if (latency < 100) return <CheckCircle className="w-4 h-4 text-emerald-500" />
      if (latency < 500) return <Clock className="w-4 h-4 text-amber-500" />
      return <AlertCircle className="w-4 h-4 text-orange-500" />
    }
    return <AlertCircle className="w-4 h-4 text-rose-500" />
  }

  const getStatusColor = (status: string, latency: number) => {
    if (status === 'connected') {
      if (latency < 100) return 'text-emerald-600 bg-emerald-50 border-emerald-200'
      if (latency < 500) return 'text-amber-600 bg-amber-50 border-amber-200'
      return 'text-orange-600 bg-orange-50 border-orange-200'
    }
    return 'text-rose-600 bg-rose-50 border-rose-200'
  }

  const getStatusText = (status: string, latency: number) => {
    if (status === 'connected') {
      if (latency < 100) return '正常'
      if (latency < 500) return '緩慢'
      return '延遲'
    }
    return '離線'
  }

  const getLatencyText = (latency: number) => {
    if (latency === 0) return 'N/A'
    return `${latency}ms`
  }

  const getHealthColor = (healthPct: number) => {
    if (healthPct >= 80) return 'text-emerald-600 bg-emerald-50'
    if (healthPct >= 60) return 'text-amber-600 bg-amber-50'
    return 'text-rose-600 bg-rose-50'
  }

  const getHealthText = (status: string) => {
    switch (status) {
      case 'healthy': return '健康'
      case 'warning': return '警告'
      case 'critical': return '嚴重'
      default: return '未知'
    }
  }

  const services = [
    {
      id: 'rpcBsc',
      name: 'BSC RPC',
      icon: Server,
      data: infraHealth.rpcBsc,
      description: 'BSC 區塊鏈節點'
    },
    {
      id: 'rpcSolana',
      name: 'Solana RPC',
      icon: Server,
      data: infraHealth.rpcSolana,
      description: 'Solana 區塊鏈節點'
    },
    {
      id: 'subgraphPancake',
      name: 'PancakeSwap',
      icon: Globe,
      data: infraHealth.subgraphPancake,
      description: 'PancakeSwap Subgraph'
    },
    {
      id: 'apiMeteora',
      name: 'Meteora API',
      icon: Wifi,
      data: infraHealth.apiMeteora,
      description: 'Meteora DLMM API'
    },
    {
      id: 'dbSupabase',
      name: 'Supabase DB',
      icon: Database,
      data: infraHealth.dbSupabase,
      description: 'Supabase 資料庫'
    }
  ]

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" />
            <span>基礎設施健康</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge className={`${getHealthColor(systemHealth.healthPct)}`}>
              {getHealthText(systemHealth.status)}
            </Badge>
            <span className="text-sm text-gray-500">
              {systemHealth.connectedCount}/{systemHealth.totalCount}
            </span>
          </div>
        </div>
        
        {/* 整體健康度進度條 */}
        <div className="mt-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-gray-500">整體健康度</span>
            <span className="text-xs font-medium text-gray-700">
              {systemHealth.healthPct.toFixed(0)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                systemHealth.healthPct >= 80 ? 'bg-emerald-500' :
                systemHealth.healthPct >= 60 ? 'bg-amber-500' : 'bg-rose-500'
              }`}
              style={{ width: `${systemHealth.healthPct}%` }}
            ></div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {services.map(service => {
          const IconComponent = service.icon
          
          return (
            <div 
              key={service.id}
              className={`rounded-lg border p-3 transition-all duration-200 ${getStatusColor(service.data.status, service.data.latency)}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-white">
                    <IconComponent className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{service.name}</div>
                    <div className="text-xs text-gray-600">{service.description}</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="text-right">
                    <div className="text-xs font-medium">
                      {getStatusText(service.data.status, service.data.latency)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {getLatencyText(service.data.latency)}
                    </div>
                  </div>
                  {getStatusIcon(service.data.status, service.data.latency)}
                </div>
              </div>
              
              {/* 延遲進度條 */}
              {service.data.status === 'connected' && service.data.latency > 0 && (
                <div className="mt-2">
                  <div className="w-full bg-white bg-opacity-50 rounded-full h-1">
                    <div 
                      className={`h-1 rounded-full transition-all duration-300 ${
                        service.data.latency < 100 ? 'bg-emerald-400' :
                        service.data.latency < 500 ? 'bg-amber-400' : 'bg-rose-400'
                      }`}
                      style={{ width: `${Math.min((service.data.latency / 1000) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          )
        })}
        
        {/* 連接統計 */}
        <div className="pt-3 border-t border-gray-100">
          <div className="grid grid-cols-3 gap-3 text-center">
            <div>
              <div className="text-lg font-bold text-emerald-600">
                {services.filter(s => s.data.status === 'connected' && s.data.latency < 100).length}
              </div>
              <div className="text-xs text-gray-500">正常</div>
            </div>
            <div>
              <div className="text-lg font-bold text-amber-600">
                {services.filter(s => s.data.status === 'connected' && s.data.latency >= 100).length}
              </div>
              <div className="text-xs text-gray-500">緩慢</div>
            </div>
            <div>
              <div className="text-lg font-bold text-rose-600">
                {services.filter(s => s.data.status !== 'connected').length}
              </div>
              <div className="text-xs text-gray-500">離線</div>
            </div>
          </div>
        </div>
        
        {/* 健康警告 */}
        {systemHealth.status !== 'healthy' && (
          <div className={`rounded-lg p-3 border ${
            systemHealth.status === 'critical' ? 'border-rose-200 bg-rose-50' : 'border-amber-200 bg-amber-50'
          }`}>
            <div className="flex items-start space-x-2">
              <AlertCircle className={`w-4 h-4 mt-0.5 ${
                systemHealth.status === 'critical' ? 'text-rose-500' : 'text-amber-500'
              }`} />
              <div>
                <div className={`text-sm font-medium ${
                  systemHealth.status === 'critical' ? 'text-rose-800' : 'text-amber-800'
                }`}>
                  基礎設施警告
                </div>
                <div className={`text-xs ${
                  systemHealth.status === 'critical' ? 'text-rose-600' : 'text-amber-600'
                }`}>
                  {systemHealth.status === 'critical' 
                    ? '多個服務離線，系統功能可能受影響' 
                    : '部分服務異常，請檢查網絡連接'}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
