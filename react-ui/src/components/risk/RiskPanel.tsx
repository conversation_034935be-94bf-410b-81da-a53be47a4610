/**
 * 風險監控面板
 * 解決痛點④：風險控管條過於扁平
 * 多段色進度條 + VaR donut 圖
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { 
  Shield, 
  AlertTriangle, 
  TrendingDown,
  Activity,
  Target
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

export const RiskPanel: React.FC = () => {
  const riskSummary = useAgnoStore(state => state.riskSummary)

  // 計算風險狀態
  const { ilNet, var95, riskScore } = riskSummary

  // IL 風險等級
  let ilRisk: 'safe' | 'warning' | 'danger'
  if (ilNet > -5) ilRisk = 'safe'
  else if (ilNet > -8) ilRisk = 'warning'
  else ilRisk = 'danger'

  // VaR 風險等級
  let varRisk: 'safe' | 'warning' | 'danger'
  if (var95 < 2) varRisk = 'safe'
  else if (var95 < 4) varRisk = 'warning'
  else varRisk = 'danger'

  const overallRisk = Math.max(
    ilRisk === 'danger' ? 2 : ilRisk === 'warning' ? 1 : 0,
    varRisk === 'danger' ? 2 : varRisk === 'warning' ? 1 : 0
  ) as 0 | 1 | 2

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  }

  const getILBarSegments = (ilNet: number) => {
    // IL 進度條分段：0% 到 -10%
    const segments = [
      { start: 0, end: -2, color: 'bg-emerald-500', label: '安全' },
      { start: -2, end: -5, color: 'bg-amber-500', label: '注意' },
      { start: -5, end: -8, color: 'bg-orange-500', label: '警告' },
      { start: -8, end: -10, color: 'bg-rose-500', label: '危險' }
    ]
    
    const currentPosition = Math.max(0, Math.min(100, (Math.abs(ilNet) / 10) * 100))
    
    return { segments, currentPosition }
  }

  const getVarDonutColor = (var95: number) => {
    if (var95 < 2) return 'text-emerald-500'
    if (var95 < 4) return 'text-amber-500'
    return 'text-rose-500'
  }

  const getVarDonutStroke = (var95: number) => {
    if (var95 < 2) return '#10b981' // emerald-500
    if (var95 < 4) return '#f59e0b' // amber-500
    return '#ef4444' // rose-500
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600 bg-emerald-50'
    if (score >= 60) return 'text-amber-600 bg-amber-50'
    return 'text-rose-600 bg-rose-50'
  }

  const getRiskLevelText = (level: number) => {
    switch (level) {
      case 0: return '安全'
      case 1: return '警告'
      case 2: return '危險'
      default: return '未知'
    }
  }

  const getRiskLevelColor = (level: number) => {
    switch (level) {
      case 0: return 'bg-emerald-500'
      case 1: return 'bg-amber-500'
      case 2: return 'bg-rose-500'
      default: return 'bg-gray-500'
    }
  }

  const { segments: ilSegments, currentPosition: ilPosition } = getILBarSegments(riskSummary.ilNet)

  // VaR 圓環圖參數
  const varRadius = 35
  const varCircumference = 2 * Math.PI * varRadius
  const varProgress = Math.min(riskSummary.var95 / 10, 1) // 假設最大 VaR 為 10%
  const varStrokeDasharray = `${varProgress * varCircumference} ${varCircumference}`

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-purple-500" />
            <span>風險監控</span>
          </CardTitle>
          
          <Badge className={`${getRiskLevelColor(overallRisk)} text-white`}>
            {getRiskLevelText(overallRisk)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 風險分數 */}
        <div className={`rounded-lg p-3 ${getRiskScoreColor(riskSummary.riskScore)}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span className="text-sm font-medium">風險分數</span>
            </div>
            <div className="text-xl font-bold">
              {riskSummary.riskScore.toFixed(0)}
            </div>
          </div>
        </div>

        {/* IL 多段進度條 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <TrendingDown className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">無常損失 (IL)</span>
            </div>
            <span className={`text-sm font-bold ${ilRisk === 'safe' ? 'text-emerald-600' : ilRisk === 'warning' ? 'text-amber-600' : 'text-rose-600'}`}>
              {formatPercentage(riskSummary.ilNet)}
            </span>
          </div>
          
          {/* 分段進度條 */}
          <div className="relative w-full h-4 bg-gray-200 rounded-full overflow-hidden">
            {/* 背景分段 */}
            <div className="absolute inset-0 flex">
              {ilSegments.map((segment, index) => (
                <div
                  key={index}
                  className={`${segment.color} opacity-30`}
                  style={{ width: '25%' }}
                ></div>
              ))}
            </div>
            
            {/* 當前位置指示器 */}
            <div
              className="absolute top-0 h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500"
              style={{ width: `${ilPosition}%` }}
            ></div>
            
            {/* 閾值標記 */}
            <div className="absolute top-0 h-full flex justify-between items-center px-1">
              <div className="w-0.5 h-full bg-white opacity-50"></div>
              <div className="w-0.5 h-full bg-white opacity-75"></div>
              <div className="w-0.5 h-full bg-white"></div>
              <div className="w-0.5 h-full bg-white opacity-50"></div>
            </div>
          </div>
          
          {/* 標籤 */}
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0%</span>
            <span>-2%</span>
            <span>-5%</span>
            <span className="text-rose-500 font-medium">-8%</span>
            <span>-10%</span>
          </div>
        </div>

        {/* VaR 圓環圖 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">風險價值 (VaR 95%)</span>
            </div>
            <span className={`text-sm font-bold ${getVarDonutColor(riskSummary.var95)}`}>
              {riskSummary.var95.toFixed(2)}%
            </span>
          </div>
          
          <div className="flex items-center justify-center">
            <div className="relative w-24 h-24">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 80 80">
                {/* 背景圓環 */}
                <circle
                  cx="40"
                  cy="40"
                  r={varRadius}
                  stroke="#e5e7eb"
                  strokeWidth="6"
                  fill="none"
                />
                {/* 進度圓環 */}
                <circle
                  cx="40"
                  cy="40"
                  r={varRadius}
                  stroke={getVarDonutStroke(riskSummary.var95)}
                  strokeWidth="6"
                  fill="none"
                  strokeLinecap="round"
                  strokeDasharray={varStrokeDasharray}
                  className="transition-all duration-500"
                />
              </svg>
              {/* 中心文字 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className={`text-lg font-bold ${getVarDonutColor(riskSummary.var95)}`}>
                    {riskSummary.var95.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-500">VaR</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* VaR 閾值說明 */}
          <div className="flex justify-center space-x-4 text-xs text-gray-500 mt-2">
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
              <span>&lt; 2%</span>
            </span>
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 rounded-full bg-amber-500"></div>
              <span>2-4%</span>
            </span>
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 rounded-full bg-rose-500"></div>
              <span>&gt; 4%</span>
            </span>
          </div>
        </div>

        {/* 最大回撤 */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">最大回撤</span>
            </div>
            <span className="text-sm font-bold text-gray-800">
              {formatPercentage(riskSummary.maxDrawdown)}
            </span>
          </div>
        </div>

        {/* 風險提示 */}
        {overallRisk > 0 && (
          <div className={`rounded-lg p-3 border ${overallRisk === 2 ? 'border-rose-200 bg-rose-50' : 'border-amber-200 bg-amber-50'}`}>
            <div className="flex items-start space-x-2">
              <AlertTriangle className={`w-4 h-4 mt-0.5 ${overallRisk === 2 ? 'text-rose-500' : 'text-amber-500'}`} />
              <div>
                <div className={`text-sm font-medium ${overallRisk === 2 ? 'text-rose-800' : 'text-amber-800'}`}>
                  風險警告
                </div>
                <div className={`text-xs ${overallRisk === 2 ? 'text-rose-600' : 'text-amber-600'}`}>
                  {overallRisk === 2
                    ? '檢測到高風險，建議立即檢查持倉' 
                    : '風險指標異常，請密切關注'}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
