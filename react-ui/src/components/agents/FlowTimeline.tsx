/**
 * Agent 流程時間線
 * 解決痛點②：Agent 狀態分兩處
 * 縱向時間線樣式，一眼看完整流程
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { 
  Settings, 
  Shield, 
  Search, 
  PieChart, 
  Target, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

const agentIcons = {
  supervisor: Settings,
  health: Shield,
  market: Search,
  portfolio: PieChart,
  strategy: Target,
  execution: Zap,
  risk: AlertTriangle
}

const agentDescriptions = {
  supervisor: '系統初始化、密鑰分發',
  health: 'RPC/DB/UI 健康檢查',
  market: '掃描 BSC/Solana 池子',
  portfolio: 'NAV 計算、資金配置',
  strategy: '生成 LP 策略計劃',
  execution: '執行交易、廣播 TX',
  risk: 'IL/VaR 監控、風控'
}

export const FlowTimeline: React.FC = () => {
  const agentFlow = useAgnoStore(state => state.agentFlow)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-emerald-500" />
      case 'running':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-rose-500" />
      default:
        return <div className="w-5 h-5 rounded-full bg-gray-300"></div>
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-500'
      case 'running':
        return 'bg-blue-500'
      case 'failed':
        return 'bg-rose-500'
      default:
        return 'bg-gray-300'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成'
      case 'running':
        return '執行中'
      case 'failed':
        return '失敗'
      default:
        return '待機'
    }
  }

  const getCardBorder = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-emerald-200 bg-emerald-50'
      case 'running':
        return 'border-blue-200 bg-blue-50'
      case 'failed':
        return 'border-rose-200 bg-rose-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
          <span>Agent 協作流程</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="relative">
          {/* 時間線主軸 */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
          
          {agentFlow.map((agent, index) => {
            const IconComponent = agentIcons[agent.id as keyof typeof agentIcons] || Settings
            const isLast = index === agentFlow.length - 1
            
            return (
              <div key={agent.id} className="relative flex items-start space-x-4 pb-4">
                {/* 時間線節點 */}
                <div className="relative z-10 flex items-center justify-center">
                  <div className={`w-12 h-12 rounded-full ${getStatusColor(agent.status)} flex items-center justify-center shadow-sm`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                </div>
                
                {/* Agent 卡片 */}
                <div className={`flex-1 min-w-0 ${!isLast ? 'pb-2' : ''}`}>
                  <div className={`rounded-lg border p-3 transition-all duration-200 ${getCardBorder(agent.status)}`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-gray-800 text-sm">
                          {agent.name}
                        </h4>
                        <Badge 
                          variant="secondary" 
                          className="text-xs px-2 py-0.5"
                        >
                          Phase {agent.phase}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(agent.status)}
                        <span className="text-xs text-gray-600">
                          {getStatusText(agent.status)}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-600 mb-2">
                      {agentDescriptions[agent.id as keyof typeof agentDescriptions]}
                    </p>
                    
                    {/* 時間信息 */}
                    {agent.startedAt && (
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          開始: {new Date(agent.startedAt).toLocaleTimeString()}
                        </span>
                        {agent.completedAt && agent.duration && (
                          <span>
                            耗時: {agent.duration.toFixed(1)}s
                          </span>
                        )}
                      </div>
                    )}
                    
                    {/* 運行中的進度條 */}
                    {agent.status === 'running' && (
                      <div className="mt-2">
                        <div className="w-full bg-blue-200 rounded-full h-1">
                          <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {/* 流程總結 */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">流程狀態</span>
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <span className="text-xs text-gray-600">
                  {agentFlow.filter(a => a.status === 'completed').length} 完成
                </span>
              </span>
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span className="text-xs text-gray-600">
                  {agentFlow.filter(a => a.status === 'running').length} 執行中
                </span>
              </span>
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                <span className="text-xs text-gray-600">
                  {agentFlow.filter(a => a.status === 'idle').length} 待機
                </span>
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
