import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value) {
  // 处理无效值
  if (value === null || value === undefined || isNaN(value)) {
    return '$0'
  }

  const numValue = Number(value)
  if (isNaN(numValue)) {
    return '$0'
  }

  // 格式化大数值
  if (numValue >= 1e9) {
    return `$${(numValue / 1e9).toFixed(1)}B`
  }
  if (numValue >= 1e6) {
    return `$${(numValue / 1e6).toFixed(1)}M`
  }
  if (numValue >= 1e3) {
    return `$${(numValue / 1e3).toFixed(1)}K`
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(numValue)
}

export function formatPercentage(value) {
  // 处理无效值
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%'
  }

  const numValue = Number(value)
  if (isNaN(numValue)) {
    return '0.00%'
  }

  return `${numValue.toFixed(2)}%`
}

export function formatNumber(value) {
  if (value >= 1e9) {
    return `${(value / 1e9).toFixed(1)}B`
  }
  if (value >= 1e6) {
    return `${(value / 1e6).toFixed(1)}M`
  }
  if (value >= 1e3) {
    return `${(value / 1e3).toFixed(1)}K`
  }
  return value.toString()
}
