from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import os

def setup_react_integration(app: FastAPI):
    """
    將React UI集成到FastAPI應用中
    """
    
    # 檢查React構建文件是否存在
    react_build_path = "react-ui/dist"
    react_static_path = "react-ui"
    
    if os.path.exists(react_build_path):
        # 生產環境：使用構建後的文件
        app.mount("/static", StaticFiles(directory=react_build_path), name="static")
        
        @app.get("/")
        async def serve_react_app():
            return FileResponse(f"{react_build_path}/index.html")
            
        @app.get("/{path:path}")
        async def serve_react_routes(path: str):
            # 對於React路由，返回index.html
            if not path.startswith("api/"):
                return FileResponse(f"{react_build_path}/index.html")
                
    else:
        # 開發環境：使用靜態HTML
        app.mount("/static", StaticFiles(directory=react_static_path), name="static")
        
        @app.get("/")
        async def serve_react_dev():
            return FileResponse(f"{react_static_path}/test-static.html")

# 使用示例
if __name__ == "__main__":
    from web_ui.modern_app import app
    setup_react_integration(app)
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8082)
