<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-critical { background-color: #ef4444; }
        
        .chain-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .chain-solana { background-color: #9945ff; color: white; }
        .chain-bsc { background-color: #f3ba2f; color: black; }
        
        .risk-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .risk-low { background-color: #dcfce7; color: #166534; }
        .risk-medium { background-color: #fef3c7; color: #92400e; }
        .risk-high { background-color: #fee2e2; color: #991b1b; }
        
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .log-entry {
            border-left: 4px solid #e5e7eb;
            padding-left: 12px;
            margin-bottom: 8px;
        }
        .log-info { border-left-color: #3b82f6; }
        .log-warning { border-left-color: #f59e0b; }
        .log-error { border-left-color: #ef4444; }
        
        .auto-refresh {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">
                        <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                        DyFlow Dashboard
                    </h1>
                    <span class="ml-4 px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                        <span class="status-indicator status-healthy"></span>
                        運行中
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-500">
                        最後更新: <span id="lastUpdate">--</span>
                    </div>
                    <button id="emergencyExit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        緊急退出
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- 左側：系統概覽和池子掃描 (2/3) -->
            <div class="lg:col-span-2 space-y-6">
                
                <!-- 系統概覽 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-tachometer-alt text-blue-600 mr-2"></i>
                        系統概覽
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="metric-card bg-blue-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600" id="totalPositions">--</div>
                            <div class="text-sm text-gray-600">活躍持倉</div>
                        </div>
                        <div class="metric-card bg-green-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-600" id="totalValue">--</div>
                            <div class="text-sm text-gray-600">總價值 (USD)</div>
                        </div>
                        <div class="metric-card bg-purple-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600" id="avgAPR">--</div>
                            <div class="text-sm text-gray-600">平均APR</div>
                        </div>
                        <div class="metric-card bg-yellow-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600" id="riskAlerts">--</div>
                            <div class="text-sm text-gray-600">風險告警</div>
                        </div>
                    </div>
                </div>

                <!-- 池子掃描 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900">
                                <i class="fas fa-search text-green-600 mr-2"></i>
                                池子掃描
                            </h2>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">Solana</button>
                                <button class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">BSC</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="text-left text-sm font-medium text-gray-500">
                                        <th class="pb-3">池子</th>
                                        <th class="pb-3">鏈</th>
                                        <th class="pb-3">TVL</th>
                                        <th class="pb-3">24h交易量</th>
                                        <th class="pb-3">APR</th>
                                        <th class="pb-3">風險</th>
                                        <th class="pb-3">評分</th>
                                    </tr>
                                </thead>
                                <tbody id="poolsTable" class="text-sm">
                                    <!-- 動態填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- LP持倉 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-wallet text-indigo-600 mr-2"></i>
                            LP持倉
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="text-left text-sm font-medium text-gray-500">
                                        <th class="pb-3">池子</th>
                                        <th class="pb-3">鏈</th>
                                        <th class="pb-3">流動性</th>
                                        <th class="pb-3">PnL</th>
                                        <th class="pb-3">IL</th>
                                        <th class="pb-3">APR</th>
                                        <th class="pb-3">狀態</th>
                                    </tr>
                                </thead>
                                <tbody id="positionsTable" class="text-sm">
                                    <!-- 動態填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：Agent日誌和聊天 (1/3) -->
            <div class="space-y-6">
                
                <!-- Agent日誌 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-robot text-blue-600 mr-2"></i>
                            Agent日誌
                        </h3>
                    </div>
                    <div class="p-4 h-64 overflow-y-auto" id="agentLogs">
                        <!-- 動態填充 -->
                    </div>
                </div>

                <!-- Agent控制 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-cogs text-green-600 mr-2"></i>
                            Agent控制
                        </h3>
                    </div>
                    <div class="p-4 space-y-3">
                        <button onclick="executeAgent('PoolPicker')" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm">
                            <i class="fas fa-search mr-2"></i>執行池子掃描
                        </button>
                        <button onclick="executeAgent('RiskSentinel')" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg text-sm">
                            <i class="fas fa-shield-alt mr-2"></i>風險檢查
                        </button>
                        <button onclick="executeAgent('RangeRebalancer')" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm">
                            <i class="fas fa-balance-scale mr-2"></i>重新平衡
                        </button>
                        <button onclick="executeAgent('HedgeAgent')" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm">
                            <i class="fas fa-chart-line mr-2"></i>DCA執行
                        </button>
                    </div>
                </div>

                <!-- 風險告警 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                            風險告警
                        </h3>
                    </div>
                    <div class="p-4 h-48 overflow-y-auto" id="riskAlertsContainer">
                        <!-- 動態填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket連接
        let ws;
        let reconnectInterval;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                console.log('WebSocket連接已建立');
                clearInterval(reconnectInterval);
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };
            
            ws.onclose = function() {
                console.log('WebSocket連接已關閉，嘗試重新連接...');
                reconnectInterval = setInterval(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket錯誤:', error);
            };
        }

        function handleWebSocketMessage(message) {
            if (message.type === 'dashboard_update' || message.type === 'initial_data') {
                updateDashboard(message.data);
            } else if (message.type === 'command_response') {
                showNotification(message.message, message.success ? 'success' : 'error');
            }
        }

        function updateDashboard(data) {
            // 更新最後更新時間
            document.getElementById('lastUpdate').textContent = new Date(data.last_update).toLocaleString();
            
            // 更新系統概覽
            updateSystemOverview(data);
            
            // 更新池子表格
            updatePoolsTable(data.pool_data || []);
            
            // 更新持倉表格
            updatePositionsTable(data.positions || []);
            
            // 更新Agent日誌
            updateAgentLogs(data.agent_logs || []);
            
            // 更新風險告警
            updateRiskAlerts(data.risk_alerts || []);
        }

        function updateSystemOverview(data) {
            const positions = data.positions || [];
            const totalPositions = positions.length;
            const totalValue = positions.reduce((sum, pos) => sum + pos.liquidity_usd, 0);
            const avgAPR = positions.length > 0 ? positions.reduce((sum, pos) => sum + pos.apr, 0) / positions.length : 0;
            const riskAlerts = (data.risk_alerts || []).filter(alert => !alert.resolved).length;
            
            document.getElementById('totalPositions').textContent = totalPositions;
            document.getElementById('totalValue').textContent = `$${totalValue.toLocaleString()}`;
            document.getElementById('avgAPR').textContent = `${avgAPR.toFixed(1)}%`;
            document.getElementById('riskAlerts').textContent = riskAlerts;
        }

        function updatePoolsTable(pools) {
            const tbody = document.getElementById('poolsTable');
            tbody.innerHTML = '';
            
            pools.forEach(pool => {
                const row = document.createElement('tr');
                row.className = 'border-t border-gray-200';
                row.innerHTML = `
                    <td class="py-3 font-medium">${pool.pair}</td>
                    <td class="py-3">
                        <span class="chain-badge chain-${pool.chain}">${pool.chain.toUpperCase()}</span>
                    </td>
                    <td class="py-3">$${(pool.tvl_usd / 1000000).toFixed(2)}M</td>
                    <td class="py-3">$${(pool.volume_24h / 1000).toFixed(0)}K</td>
                    <td class="py-3 font-semibold text-green-600">${pool.apr.toFixed(1)}%</td>
                    <td class="py-3">
                        <span class="risk-badge risk-${pool.risk_level}">${pool.risk_level}</span>
                    </td>
                    <td class="py-3 font-semibold">${pool.score}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positionsTable');
            tbody.innerHTML = '';
            
            positions.forEach(position => {
                const row = document.createElement('tr');
                row.className = 'border-t border-gray-200';
                const pnlColor = position.pnl_pct >= 0 ? 'text-green-600' : 'text-red-600';
                const ilColor = position.il_pct >= 0 ? 'text-green-600' : 'text-red-600';
                
                row.innerHTML = `
                    <td class="py-3 font-medium">${position.pool}</td>
                    <td class="py-3">
                        <span class="chain-badge chain-${position.chain}">${position.chain.toUpperCase()}</span>
                    </td>
                    <td class="py-3">$${position.liquidity_usd.toLocaleString()}</td>
                    <td class="py-3 font-semibold ${pnlColor}">${position.pnl_pct >= 0 ? '+' : ''}${position.pnl_pct.toFixed(1)}%</td>
                    <td class="py-3 font-semibold ${ilColor}">${position.il_pct.toFixed(1)}%</td>
                    <td class="py-3 font-semibold text-green-600">${position.apr.toFixed(1)}%</td>
                    <td class="py-3">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">${position.status}</span>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateAgentLogs(logs) {
            const container = document.getElementById('agentLogs');
            container.innerHTML = '';
            
            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level}`;
                logEntry.innerHTML = `
                    <div class="text-xs text-gray-500">${new Date(log.timestamp).toLocaleTimeString()}</div>
                    <div class="font-medium text-sm">${log.agent}</div>
                    <div class="text-sm text-gray-700">${log.message}</div>
                `;
                container.appendChild(logEntry);
            });
            
            container.scrollTop = container.scrollHeight;
        }

        function updateRiskAlerts(alerts) {
            const container = document.getElementById('riskAlertsContainer');
            container.innerHTML = '';
            
            alerts.forEach(alert => {
                const alertDiv = document.createElement('div');
                alertDiv.className = `p-3 mb-2 rounded-lg ${alert.level === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 'bg-blue-50 border border-blue-200'}`;
                alertDiv.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="text-sm font-medium">${alert.message}</div>
                            <div class="text-xs text-gray-500">${new Date(alert.timestamp).toLocaleString()}</div>
                        </div>
                        ${alert.resolved ? '<span class="text-xs text-green-600">已解決</span>' : ''}
                    </div>
                `;
                container.appendChild(alertDiv);
            });
        }

        function executeAgent(agentName) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'agent_command',
                    command: 'execute_agent',
                    agent_name: agentName
                }));
                showNotification(`正在執行 ${agentName}...`, 'info');
            }
        }

        function showNotification(message, type) {
            // 簡單的通知實現
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-600' : 
                type === 'error' ? 'bg-red-600' : 'bg-blue-600'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // 緊急退出按鈕
        document.getElementById('emergencyExit').addEventListener('click', function() {
            if (confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'agent_command',
                        command: 'emergency_exit'
                    }));
                }
            }
        });

        // 初始化
        connectWebSocket();
    </script>
</body>
</html>
