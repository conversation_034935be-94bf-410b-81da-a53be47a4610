<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow Dashboard</title>
    
    <!-- Vue.js 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .dashboard-container {
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .title {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .pools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .pool-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .pool-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .pool-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(4px);
        }

        .pool-actions {
            display: flex;
            gap: 8px;
            margin-left: 16px;
        }

        .pool-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .pool-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-view {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }

        .btn-evaluate {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .pool-info h4 {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .pool-badges {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-solana {
            background: linear-gradient(45deg, #9945ff, #14f195);
            color: white;
        }
        
        .badge-bsc {
            background: linear-gradient(45deg, #f3ba2f, #ffd700);
            color: black;
        }
        
        .badge-low { background: #d4edda; color: #155724; }
        .badge-medium { background: #fff3cd; color: #856404; }
        .badge-high { background: #f8d7da; color: #721c24; }
        
        .pool-stats {
            display: flex;
            gap: 16px;
            text-align: right;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
        }
        
        .stat-item-value {
            font-weight: 600;
            font-size: 1rem;
        }
        
        .stat-item-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .sidebar {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #000;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .log-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            background: rgba(255, 255, 255, 0.7);
        }
        
        .log-warning { border-left-color: #ffc107; }
        .log-error { border-left-color: #dc3545; }
        .log-success { border-left-color: #28a745; }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .log-agent {
            font-weight: 600;
        }
        
        .log-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        .log-message {
            font-size: 0.9rem;
        }
        
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        
        .main-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .agent-status {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .agent-badge {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .agent-active { background: #d4edda; color: #155724; }
        .agent-monitoring { background: #fff3cd; color: #856404; }
        
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            
            .pools-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .pool-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .pool-stats {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- Header -->
            <div class="glass-card">
                <div class="header">
                    <div>
                        <h1 class="title">
                            <i class="fas fa-chart-line" style="color: #007bff; margin-right: 12px;"></i>
                            DyFlow Agent監控Dashboard
                        </h1>
                        <div class="status">
                            <div class="status-dot"></div>
                            <span>系統運行中 | 最後更新: <span v-text="lastUpdate"></span></span>
                        </div>
                    </div>
                    <button class="btn btn-danger" @click="emergencyExit">
                        <i class="fas fa-exclamation-triangle"></i> 緊急退出
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value positive" v-text="totalPositions"></div>
                    <div class="stat-label">活躍持倉</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive">$<span v-text="formatNumber(totalValue)"></span></div>
                    <div class="stat-label">總價值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive"><span v-text="formatPercent(avgAPR)"></span>%</div>
                    <div class="stat-label">平均APR</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive" v-text="bscPoolCount"></div>
                    <div class="stat-label">BSC池子</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive" v-text="solanaPoolCount"></div>
                    <div class="stat-label">Solana池子</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive" v-text="riskAlerts"></div>
                    <div class="stat-label">風險告警</div>
                </div>
            </div>

            <!-- Main Layout -->
            <div class="main-layout">
                <!-- Left Column: Pools -->
                <div>
                    <!-- Agent Status -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-robot" style="color: #007bff;"></i>
                            Agent監控狀態
                        </div>
                        <div class="agent-status">
                            <span class="agent-badge agent-active">PoolPicker: 運行中</span>
                            <span class="agent-badge agent-monitoring">RiskSentinel: 監控中</span>
                            <span class="agent-badge agent-active">RangeRebalancer: 活躍</span>
                        </div>
                    </div>

                    <!-- Pools Grid -->
                    <div class="pools-grid">
                        <!-- BSC Pools -->
                        <div class="glass-card">
                            <div class="section-title">
                                <i class="fas fa-coins" style="color: #f3ba2f;"></i>
                                BSC池子監控 (<span v-text="bscPools.length"></span>個)
                            </div>
                            <div class="pool-list">
                                <div class="pool-item" v-for="pool in bscPools" :key="pool.id">
                                    <div class="pool-info">
                                        <h4 v-text="pool.pair"></h4>
                                        <div class="pool-badges">
                                            <span class="badge badge-bsc">BSC</span>
                                            <span class="badge" :class="'badge-' + pool.risk_level" v-text="pool.risk_level"></span>
                                        </div>
                                    </div>
                                    <div class="pool-stats">
                                        <div class="stat-item">
                                            <div class="stat-item-value positive"><span v-text="formatPercent(pool.apr)"></span>%</div>
                                            <div class="stat-item-label">APR</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-item-value">$<span v-text="formatMillion(pool.tvl_usd)"></span>M</div>
                                            <div class="stat-item-label">TVL</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-item-value" v-text="pool.score"></div>
                                            <div class="stat-item-label">評分</div>
                                        </div>
                                    </div>
                                    <div class="pool-actions">
                                        <a v-if="pool.pancake_url" :href="pool.pancake_url" target="_blank" class="pool-btn btn-view">
                                            <i class="fas fa-external-link-alt"></i> PancakeSwap
                                        </a>
                                        <button class="pool-btn btn-evaluate" @click="evaluatePool(pool)">
                                            <i class="fas fa-chart-line"></i> Agent評估
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Solana Pools -->
                        <div class="glass-card">
                            <div class="section-title">
                                <i class="fas fa-coins" style="color: #9945ff;"></i>
                                Solana池子監控 (<span v-text="solanaPools.length"></span>個)
                            </div>
                            <div class="pool-list">
                                <div class="pool-item" v-for="pool in solanaPools" :key="pool.id">
                                    <div class="pool-info">
                                        <h4 v-text="pool.pair"></h4>
                                        <div class="pool-badges">
                                            <span class="badge badge-solana">SOLANA</span>
                                            <span class="badge" :class="'badge-' + pool.risk_level" v-text="pool.risk_level"></span>
                                        </div>
                                    </div>
                                    <div class="pool-stats">
                                        <div class="stat-item">
                                            <div class="stat-item-value positive"><span v-text="formatPercent(pool.apr)"></span>%</div>
                                            <div class="stat-item-label">APR</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-item-value">$<span v-text="formatMillion(pool.tvl_usd)"></span>M</div>
                                            <div class="stat-item-label">TVL</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-item-value" v-text="pool.score"></div>
                                            <div class="stat-item-label">評分</div>
                                        </div>
                                    </div>
                                    <div class="pool-actions">
                                        <a v-if="pool.meteora_url" :href="pool.meteora_url" target="_blank" class="pool-btn btn-view">
                                            <i class="fas fa-external-link-alt"></i> Meteora
                                        </a>
                                        <button class="pool-btn btn-evaluate" @click="evaluatePool(pool)">
                                            <i class="fas fa-chart-line"></i> Agent評估
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LP Positions -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-wallet" style="color: #6f42c1;"></i>
                            LP持倉管理 (<span v-text="positions.length"></span>個)
                        </div>
                        <div class="pool-item" v-for="position in positions" :key="position.id">
                            <div class="pool-info">
                                <h4 v-text="position.pool"></h4>
                                <div class="pool-badges">
                                    <span class="badge" :class="'badge-' + position.chain" v-text="position.chain.toUpperCase()"></span>
                                    <span style="font-size: 0.8rem; color: #666;" v-text="position.range"></span>
                                </div>
                            </div>
                            <div class="pool-stats">
                                <div class="stat-item">
                                    <div class="stat-item-value" :class="position.pnl_pct >= 0 ? 'positive' : 'negative'">
                                        <span v-text="formatPercent(position.pnl_pct, true)"></span>%
                                    </div>
                                    <div class="stat-item-label">PnL</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-item-value" :class="position.il_pct >= 0 ? 'positive' : 'negative'">
                                        <span v-text="formatPercent(position.il_pct)"></span>%
                                    </div>
                                    <div class="stat-item-label">IL</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-item-value">$<span v-text="formatNumber(position.liquidity_usd)"></span></div>
                                    <div class="stat-item-label">流動性</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Controls & Logs -->
                <div class="sidebar">
                    <!-- Agent Controls -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-cogs" style="color: #28a745;"></i>
                            Agent控制面板
                        </div>
                        <div class="controls-grid">
                            <button class="btn btn-primary" @click="executeAgent('PoolPicker')">
                                <i class="fas fa-search"></i> 池子掃描
                            </button>
                            <button class="btn btn-warning" @click="executeAgent('RiskSentinel')">
                                <i class="fas fa-shield-alt"></i> 風險檢查
                            </button>
                            <button class="btn btn-success" @click="executeAgent('RangeRebalancer')">
                                <i class="fas fa-balance-scale"></i> 重新平衡
                            </button>
                            <button class="btn btn-primary" @click="executeAgent('HedgeAgent')">
                                <i class="fas fa-chart-line"></i> DCA執行
                            </button>
                        </div>
                        <button class="btn btn-primary" @click="forceUpdate" style="width: 100%; margin-bottom: 12px;">
                            <i class="fas fa-sync-alt"></i> 強制更新數據
                        </button>
                        <button class="btn btn-warning" @click="pauseSystem" style="width: 100%;">
                            <i class="fas fa-pause"></i> 暫停系統
                        </button>
                    </div>

                    <!-- Agent對話框 -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-comments" style="color: #007bff;"></i>
                            Agent對話
                        </div>

                        <!-- 對話歷史 -->
                        <div class="chat-history" style="height: 200px; overflow-y: auto; margin-bottom: 16px; padding: 12px; background: rgba(255,255,255,0.5); border-radius: 8px;">
                            <div v-for="chat in chatHistory" :key="chat.id" class="chat-message" style="margin-bottom: 12px;">
                                <div style="font-size: 0.8rem; color: #666; margin-bottom: 4px;">
                                    <strong v-text="chat.sender"></strong> - <span v-text="formatTime(chat.timestamp)"></span>
                                </div>
                                <div style="padding: 8px; border-radius: 6px;" :style="chat.sender === 'User' ? 'background: #e3f2fd; margin-left: 20px;' : 'background: #f1f8e9; margin-right: 20px;'">
                                    <span v-text="chat.message"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 輸入框 -->
                        <div style="display: flex; gap: 8px;">
                            <input
                                v-model="chatInput"
                                @keyup.enter="sendChatMessage"
                                placeholder="與Agent對話..."
                                style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 6px; font-size: 0.9rem;"
                            />
                            <button @click="sendChatMessage" class="btn btn-primary" style="padding: 8px 16px;">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- 快速命令 -->
                        <div style="margin-top: 12px; display: flex; flex-wrap: gap: 6px;">
                            <button @click="quickCommand('掃描新池子')" class="btn" style="padding: 4px 8px; font-size: 0.8rem; background: #e3f2fd;">
                                掃描新池子
                            </button>
                            <button @click="quickCommand('檢查風險')" class="btn" style="padding: 4px 8px; font-size: 0.8rem; background: #fff3e0;">
                                檢查風險
                            </button>
                            <button @click="quickCommand('重新平衡')" class="btn" style="padding: 4px 8px; font-size: 0.8rem; background: #f3e5f5;">
                                重新平衡
                            </button>
                        </div>
                    </div>

                    <!-- Agent日誌 -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-list-alt" style="color: #6c757d;"></i>
                            Agent執行日誌
                        </div>
                        <div class="log-list">
                            <div v-for="log in agentLogs" :key="log.timestamp"
                                 class="log-item"
                                 :class="'log-' + log.level">
                                <div class="log-header">
                                    <div class="log-agent" v-text="log.agent"></div>
                                    <div class="log-time" v-text="formatTime(log.timestamp)"></div>
                                </div>
                                <div class="log-message" v-text="log.message"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    ws: null,
                    systemStatus: {},
                    poolData: [],
                    positions: [],
                    agentLogs: [],
                    tokenPrices: {},
                    lastUpdate: '--',
                    totalPositions: 0,
                    totalValue: 0,
                    avgAPR: 0,
                    riskAlerts: 0,
                    chatHistory: [],
                    chatInput: '',
                    chatIdCounter: 1
                }
            },
            computed: {
                bscPools() {
                    return this.poolData.filter(pool => pool.chain === 'bsc').slice(0, 15);
                },
                solanaPools() {
                    return this.poolData.filter(pool => pool.chain === 'solana').slice(0, 15);
                },
                bscPoolCount() {
                    return this.bscPools.length;
                },
                solanaPoolCount() {
                    return this.solanaPools.length;
                }
            },
            mounted() {
                this.connectWebSocket();
            },
            methods: {
                connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws`;
                    
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket連接已建立');
                    };
                    
                    this.ws.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        this.handleWebSocketMessage(message);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket連接已關閉，5秒後重新連接...');
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };
                },
                
                handleWebSocketMessage(message) {
                    if (message.type === 'dashboard_update' || message.type === 'initial_data') {
                        const data = message.data;
                        
                        this.systemStatus = data.system_status || {};
                        this.poolData = data.pool_data || [];
                        this.positions = data.positions || [];
                        this.agentLogs = data.agent_logs || [];
                        this.tokenPrices = data.token_prices || {};
                        this.lastUpdate = new Date(data.last_update).toLocaleTimeString();
                        
                        // 計算統計數據
                        this.totalPositions = this.positions.length;
                        this.totalValue = this.positions.reduce((sum, pos) => sum + pos.liquidity_usd, 0);
                        this.avgAPR = this.positions.length > 0 ? 
                            this.positions.reduce((sum, pos) => sum + pos.apr, 0) / this.positions.length : 0;
                        this.riskAlerts = (data.risk_alerts || []).filter(alert => !alert.resolved).length;
                    }
                },
                
                executeAgent(agentName) {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'agent_command',
                            command: 'execute_agent',
                            agent_name: agentName
                        }));
                        console.log(`正在執行 ${agentName}...`);
                    }
                },
                
                forceUpdate() {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'force_update'
                        }));
                        console.log('正在強制更新數據...');
                    }
                },
                
                pauseSystem() {
                    if (confirm('確定要暫停系統嗎？')) {
                        console.log('系統已暫停');
                    }
                },
                
                emergencyExit() {
                    if (confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
                        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                            this.ws.send(JSON.stringify({
                                type: 'agent_command',
                                command: 'emergency_exit'
                            }));
                        }
                    }
                },

                evaluatePool(pool) {
                    // 添加到對話歷史
                    this.addChatMessage('User', `評估池子 ${pool.pair} (${pool.chain.toUpperCase()})`);

                    // 發送Agent評估命令
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'agent_command',
                            command: 'evaluate_pool',
                            pool_data: pool
                        }));

                        // Agent回應
                        setTimeout(() => {
                            const analysis = this.getAgentAnalysis(pool);
                            this.addChatMessage('Agent', `🤖 池子評估完成:\n\n📊 ${pool.pair}\n• TVL: $${this.formatNumber(pool.tvl_usd)}\n• APR: ${pool.apr.toFixed(1)}%\n• 風險: ${pool.risk_level}\n\n${analysis}`);
                        }, 1000);

                        // 添加到Agent日誌
                        this.agentLogs.unshift({
                            timestamp: new Date(),
                            agent: 'PoolEvaluator',
                            level: 'info',
                            message: `開始評估池子 ${pool.pair} (${pool.chain.toUpperCase()})`
                        });
                    }
                },

                sendChatMessage() {
                    if (!this.chatInput.trim()) return;

                    const userMessage = this.chatInput.trim();
                    this.addChatMessage('User', userMessage);
                    this.chatInput = '';

                    // 模擬Agent回應
                    setTimeout(() => {
                        const response = this.getAgentResponse(userMessage);
                        this.addChatMessage('Agent', response);
                    }, 1000);
                },

                quickCommand(command) {
                    this.addChatMessage('User', command);

                    setTimeout(() => {
                        let response = '';
                        switch(command) {
                            case '掃描新池子':
                                response = '🔍 正在掃描新池子...\n\n發現 3 個高分池子:\n• SOL/USDC (評分: 85)\n• BNB/USDT (評分: 78)\n• ORCA/SOL (評分: 72)';
                                break;
                            case '檢查風險':
                                response = '🛡️ 風險檢查完成:\n\n✅ 所有持倉風險正常\n⚠️ PEPE/SOL 池子 IL 接近 -7%\n📊 整體風險評級: 中等';
                                break;
                            case '重新平衡':
                                response = '⚖️ 重新平衡完成:\n\n• SOL/USDC: 調整範圍至 ±10%\n• BNB/USDT: 無需調整\n💰 預計節省手續費: $12.50';
                                break;
                            default:
                                response = '🤖 我正在處理您的請求...';
                        }
                        this.addChatMessage('Agent', response);
                    }, 1500);
                },

                addChatMessage(sender, message) {
                    this.chatHistory.push({
                        id: this.chatIdCounter++,
                        sender: sender,
                        message: message,
                        timestamp: new Date()
                    });

                    // 保持最近50條消息
                    if (this.chatHistory.length > 50) {
                        this.chatHistory = this.chatHistory.slice(-50);
                    }

                    // 滾動到底部
                    this.$nextTick(() => {
                        const chatHistory = document.querySelector('.chat-history');
                        if (chatHistory) {
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        }
                    });
                },

                getAgentResponse(message) {
                    const lowerMessage = message.toLowerCase();

                    if (lowerMessage.includes('池子') || lowerMessage.includes('pool')) {
                        return '🏊 目前監控中的池子:\n• BSC: 1 個池子\n• Solana: 3 個池子\n\n最高APR: 45.6% (PEPE/SOL)\n最安全: BNB/USDT (低風險)';
                    } else if (lowerMessage.includes('風險') || lowerMessage.includes('risk')) {
                        return '🛡️ 當前風險狀況:\n• 整體風險: 中等\n• IL風險: -2.1% (正常)\n• VaR: 3.2% (安全範圍)\n\n建議: 繼續監控 PEPE/SOL 池子';
                    } else if (lowerMessage.includes('收益') || lowerMessage.includes('profit')) {
                        return '💰 收益統計:\n• 總PnL: +8.5%\n• 手續費收入: $156.80\n• 最佳表現: BNB/USDT (+12.3%)\n\n本月收益率: +15.2%';
                    } else if (lowerMessage.includes('退出') || lowerMessage.includes('exit')) {
                        return '🚪 退出選項:\n• 緊急退出: 立即關閉所有持倉\n• 部分退出: 選擇特定池子\n• 定時退出: 設置條件觸發\n\n需要我執行哪種退出策略？';
                    } else {
                        return `🤖 我理解您說的是: "${message}"\n\n我可以幫您:\n• 分析池子表現\n• 檢查風險狀況\n• 執行交易策略\n• 監控市場變化\n\n請告訴我您需要什麼幫助？`;
                    }
                },

                getAgentAnalysis(pool) {
                    let analysis = [];

                    // TVL分析
                    if (pool.tvl_usd >= 1000000) {
                        analysis.push('✅ 流動性充足，滑點風險低');
                    } else if (pool.tvl_usd >= 100000) {
                        analysis.push('⚠️ 流動性中等，注意滑點');
                    } else {
                        analysis.push('❌ 流動性較低，高滑點風險');
                    }

                    // APR分析
                    if (pool.apr >= 50) {
                        analysis.push('⚠️ APR較高，注意無常損失風險');
                    } else if (pool.apr >= 10) {
                        analysis.push('✅ APR合理，收益風險平衡');
                    } else {
                        analysis.push('📊 APR較低，相對穩定');
                    }

                    // 風險分析
                    if (pool.risk_level === 'low') {
                        analysis.push('✅ 低風險池子，適合穩健投資');
                    } else if (pool.risk_level === 'medium') {
                        analysis.push('⚠️ 中等風險，需要監控');
                    } else {
                        analysis.push('❌ 高風險池子，謹慎投資');
                    }

                    // 鏈特定分析
                    if (pool.chain === 'solana') {
                        analysis.push('⚡ Solana生態，交易速度快');
                    } else if (pool.chain === 'bsc') {
                        analysis.push('🔗 BSC生態，手續費較低');
                    }

                    return analysis.join('\n• ');
                },
                
                formatTime(timestamp) {
                    return new Date(timestamp).toLocaleTimeString();
                },
                
                formatNumber(num) {
                    if (num >= 1000000) {
                        return (num / 1000000).toFixed(2) + 'M';
                    } else if (num >= 1000) {
                        return (num / 1000).toFixed(1) + 'K';
                    }
                    return num.toLocaleString();
                },
                
                formatMillion(num) {
                    return (num / 1000000).toFixed(2);
                },
                
                formatPercent(num, showSign = false) {
                    const formatted = num.toFixed(1);
                    if (showSign && num >= 0) {
                        return '+' + formatted;
                    }
                    return formatted;
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
