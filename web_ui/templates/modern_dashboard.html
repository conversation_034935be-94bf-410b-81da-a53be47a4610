<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    
    <!-- Vue.js 3 + Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            margin: 0;
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chain-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .chain-solana {
            background: linear-gradient(45deg, #9945ff, #14f195);
            color: white;
        }
        
        .chain-bsc {
            background: linear-gradient(45deg, #f3ba2f, #ffd700);
            color: #000;
        }
        
        .risk-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        
        .pool-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .pool-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(4px);
        }
        
        .pool-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .pool-pair {
            font-weight: 600;
            font-size: 1rem;
        }
        
        .pool-stats {
            display: flex;
            gap: 16px;
            align-items: center;
        }
        
        .log-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            background: rgba(255, 255, 255, 0.7);
        }
        
        .log-warning { border-left-color: #ffc107; }
        .log-error { border-left-color: #dc3545; }
        .log-success { border-left-color: #28a745; }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .control-btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #000;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-offline { background: #dc3545; }
        
        .price-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .price-item {
            text-align: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }
        
        .price-symbol {
            font-weight: 600;
            color: #666;
        }
        
        .price-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: #007bff;
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- Header -->
            <div class="header-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h1 style="margin: 0; color: #333;">
                            <i class="fas fa-chart-line" style="color: #007bff; margin-right: 12px;"></i>
                            DyFlow Real Data Dashboard
                        </h1>
                        <p style="margin: 8px 0 0 0; color: #666;">
                            <span class="status-indicator status-online"></span>
                            系統運行中 | 最後更新: <span v-text="lastUpdate"></span>
                        </p>
                    </div>
                    <div>
                        <button class="control-btn btn-danger" @click="emergencyExit" style="padding: 8px 16px;">
                            <i class="fas fa-exclamation-triangle"></i> 緊急退出
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" style="color: #007bff;" v-text="totalPositions"></div>
                    <div class="stat-label">活躍持倉</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #28a745;">$<span v-text="totalValue.toLocaleString()"></span></div>
                    <div class="stat-label">總價值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #6f42c1;"><span v-text="avgAPR.toFixed(1)"></span>%</div>
                    <div class="stat-label">平均APR</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #fd7e14;" v-text="poolCount"></div>
                    <div class="stat-label">掃描池子</div>
                </div>
            </div>

            <!-- Main Grid -->
            <div class="main-grid">
                <!-- Left Column: Pools & Positions -->
                <div>
                    <!-- Token Prices -->
                    <div class="content-card" style="margin-bottom: 24px;">
                        <div class="card-title">
                            <i class="fas fa-coins" style="color: #ffc107;"></i>
                            實時代幣價格
                        </div>
                        <div class="price-display">
                            <div class="price-item" v-for="(price, symbol) in tokenPrices" :key="symbol">
                                <div class="price-symbol" v-text="symbol"></div>
                                <div class="price-value">$<span v-text="price.toLocaleString()"></span></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pool Scanning -->
                    <div class="content-card" style="margin-bottom: 24px;">
                        <div class="card-title">
                            <i class="fas fa-search" style="color: #28a745;"></i>
                            池子掃描 (<span v-text="poolData.length"></span>個)
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <div class="pool-item" v-for="pool in poolData.slice(0, 10)" :key="pool.id">
                                <div class="pool-info">
                                    <div class="pool-pair" v-text="pool.pair"></div>
                                    <div style="display: flex; gap: 8px; align-items: center;">
                                        <span class="chain-badge" :class="'chain-' + pool.chain" v-text="pool.chain.toUpperCase()"></span>
                                        <span class="risk-badge" :class="'risk-' + pool.risk_level" v-text="pool.risk_level"></span>
                                    </div>
                                </div>
                                <div class="pool-stats">
                                    <div style="text-align: right;">
                                        <div style="font-weight: 600; color: #28a745;"><span v-text="pool.apr.toFixed(1)"></span>%</div>
                                        <div style="font-size: 0.8rem; color: #666;">APR</div>
                                    </div>
                                    <div style="text-align: right;">
                                        <div style="font-weight: 600;">$<span v-text="(pool.tvl_usd / 1000000).toFixed(2)"></span>M</div>
                                        <div style="font-size: 0.8rem; color: #666;">TVL</div>
                                    </div>
                                    <div style="text-align: right;">
                                        <div style="font-weight: 600;" v-text="pool.score"></div>
                                        <div style="font-size: 0.8rem; color: #666;">評分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LP Positions -->
                    <div class="content-card">
                        <div class="card-title">
                            <i class="fas fa-wallet" style="color: #6f42c1;"></i>
                            LP持倉 (<span v-text="positions.length"></span>個)
                        </div>
                        <div v-for="position in positions" :key="position.id" class="pool-item">
                            <div class="pool-info">
                                <div class="pool-pair" v-text="position.pool"></div>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <span class="chain-badge" :class="'chain-' + position.chain" v-text="position.chain.toUpperCase()"></span>
                                    <span style="font-size: 0.8rem; color: #666;" v-text="position.range"></span>
                                </div>
                            </div>
                            <div class="pool-stats">
                                <div style="text-align: right;">
                                    <div style="font-weight: 600;" :style="{ color: position.pnl_pct >= 0 ? '#28a745' : '#dc3545' }">
                                        <span v-text="(position.pnl_pct >= 0 ? '+' : '') + position.pnl_pct.toFixed(1)"></span>%
                                    </div>
                                    <div style="font-size: 0.8rem; color: #666;">PnL</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600;" :style="{ color: position.il_pct >= 0 ? '#28a745' : '#dc3545' }">
                                        <span v-text="position.il_pct.toFixed(1)"></span>%
                                    </div>
                                    <div style="font-size: 0.8rem; color: #666;">IL</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600;">$<span v-text="position.liquidity_usd.toLocaleString()"></span></div>
                                    <div style="font-size: 0.8rem; color: #666;">流動性</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Controls & Logs -->
                <div>
                    <!-- Agent Controls -->
                    <div class="content-card" style="margin-bottom: 24px;">
                        <div class="card-title">
                            <i class="fas fa-robot" style="color: #007bff;"></i>
                            Agent控制
                        </div>
                        <div class="control-buttons">
                            <button class="control-btn btn-primary" @click="executeAgent('PoolPicker')">
                                <i class="fas fa-search"></i> 池子掃描
                            </button>
                            <button class="control-btn btn-warning" @click="executeAgent('RiskSentinel')">
                                <i class="fas fa-shield-alt"></i> 風險檢查
                            </button>
                            <button class="control-btn btn-success" @click="executeAgent('RangeRebalancer')">
                                <i class="fas fa-balance-scale"></i> 重新平衡
                            </button>
                            <button class="control-btn btn-primary" @click="executeAgent('HedgeAgent')">
                                <i class="fas fa-chart-line"></i> DCA執行
                            </button>
                        </div>
                        <button class="control-btn btn-primary" @click="forceUpdate" style="width: 100%;">
                            <i class="fas fa-sync-alt"></i> 強制更新數據
                        </button>
                    </div>

                    <!-- Agent Logs -->
                    <div class="content-card">
                        <div class="card-title">
                            <i class="fas fa-list-alt" style="color: #6c757d;"></i>
                            Agent日誌
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <div v-for="log in agentLogs" :key="log.timestamp" 
                                 class="log-item" 
                                 :class="'log-' + log.level">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                    <strong>{{ log.agent }}</strong>
                                    <small>{{ formatTime(log.timestamp) }}</small>
                                </div>
                                <div>{{ log.message }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    ws: null,
                    systemStatus: {},
                    poolData: [],
                    positions: [],
                    agentLogs: [],
                    tokenPrices: {},
                    lastUpdate: '--',
                    totalPositions: 0,
                    totalValue: 0,
                    avgAPR: 0,
                    poolCount: 0
                }
            },
            mounted() {
                this.connectWebSocket();
            },
            methods: {
                connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws`;
                    
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket連接已建立');
                    };
                    
                    this.ws.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        this.handleWebSocketMessage(message);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket連接已關閉，5秒後重新連接...');
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };
                },
                
                handleWebSocketMessage(message) {
                    if (message.type === 'dashboard_update' || message.type === 'initial_data') {
                        const data = message.data;
                        
                        this.systemStatus = data.system_status || {};
                        this.poolData = data.pool_data || [];
                        this.positions = data.positions || [];
                        this.agentLogs = data.agent_logs || [];
                        this.tokenPrices = data.token_prices || {};
                        this.lastUpdate = new Date(data.last_update).toLocaleTimeString();
                        
                        // 計算統計數據
                        this.totalPositions = this.positions.length;
                        this.totalValue = this.positions.reduce((sum, pos) => sum + pos.liquidity_usd, 0);
                        this.avgAPR = this.positions.length > 0 ? 
                            this.positions.reduce((sum, pos) => sum + pos.apr, 0) / this.positions.length : 0;
                        this.poolCount = this.poolData.length;
                    }
                },
                
                executeAgent(agentName) {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'agent_command',
                            command: 'execute_agent',
                            agent_name: agentName
                        }));
                        this.showNotification(`正在執行 ${agentName}...`, 'info');
                    }
                },
                
                forceUpdate() {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'force_update'
                        }));
                        this.showNotification('正在強制更新數據...', 'info');
                    }
                },
                
                emergencyExit() {
                    if (confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
                        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                            this.ws.send(JSON.stringify({
                                type: 'agent_command',
                                command: 'emergency_exit'
                            }));
                        }
                    }
                },
                
                formatTime(timestamp) {
                    return new Date(timestamp).toLocaleTimeString();
                },
                
                showNotification(message, type) {
                    // 簡單的通知實現
                    console.log(`${type.toUpperCase()}: ${message}`);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
