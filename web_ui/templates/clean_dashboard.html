<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow Modern Dashboard</title>
    
    <!-- Vue.js 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .title {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .price-item {
            text-align: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }
        
        .price-symbol {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
        }
        
        .price-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .pool-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .pool-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .pool-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(4px);
        }
        
        .pool-info h4 {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .pool-badges {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-solana {
            background: linear-gradient(45deg, #9945ff, #14f195);
            color: white;
        }
        
        .badge-bsc {
            background: linear-gradient(45deg, #f3ba2f, #ffd700);
            color: black;
        }
        
        .badge-low { background: #d4edda; color: #155724; }
        .badge-medium { background: #fff3cd; color: #856404; }
        .badge-high { background: #f8d7da; color: #721c24; }
        
        .pool-stats {
            display: flex;
            gap: 16px;
            text-align: right;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
        }
        
        .stat-item-value {
            font-weight: 600;
            font-size: 1rem;
        }
        
        .stat-item-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #000;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .log-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            background: rgba(255, 255, 255, 0.7);
        }
        
        .log-warning { border-left-color: #ffc107; }
        .log-error { border-left-color: #dc3545; }
        .log-success { border-left-color: #28a745; }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .log-agent {
            font-weight: 600;
        }
        
        .log-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        .log-message {
            font-size: 0.9rem;
        }
        
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .pool-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .pool-stats {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- Header -->
            <div class="glass-card">
                <div class="header">
                    <div>
                        <h1 class="title">
                            <i class="fas fa-chart-line" style="color: #007bff; margin-right: 12px;"></i>
                            DyFlow Real Data Dashboard
                        </h1>
                        <div class="status">
                            <div class="status-dot"></div>
                            <span>系統運行中 | 最後更新: <span v-text="lastUpdate"></span></span>
                        </div>
                    </div>
                    <button class="btn btn-danger" @click="emergencyExit">
                        <i class="fas fa-exclamation-triangle"></i> 緊急退出
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value positive" v-text="totalPositions"></div>
                    <div class="stat-label">活躍持倉</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive">$<span v-text="formatNumber(totalValue)"></span></div>
                    <div class="stat-label">總價值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive"><span v-text="formatPercent(avgAPR)"></span>%</div>
                    <div class="stat-label">平均APR</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value positive" v-text="poolCount"></div>
                    <div class="stat-label">掃描池子</div>
                </div>
            </div>

            <!-- Main Grid -->
            <div class="main-grid">
                <!-- Left Column -->
                <div>
                    <!-- Token Prices -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-coins" style="color: #ffc107;"></i>
                            實時代幣價格
                        </div>
                        <div class="price-grid">
                            <div class="price-item" v-for="(price, symbol) in tokenPrices" :key="symbol">
                                <div class="price-symbol" v-text="symbol"></div>
                                <div class="price-value">$<span v-text="formatNumber(price)"></span></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pool Scanning -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-search" style="color: #28a745;"></i>
                            池子掃描 (<span v-text="poolData.length"></span>個)
                        </div>
                        <div class="pool-list">
                            <div class="pool-item" v-for="pool in poolData.slice(0, 10)" :key="pool.id">
                                <div class="pool-info">
                                    <h4 v-text="pool.pair"></h4>
                                    <div class="pool-badges">
                                        <span class="badge" :class="'badge-' + pool.chain" v-text="pool.chain.toUpperCase()"></span>
                                        <span class="badge" :class="'badge-' + pool.risk_level" v-text="pool.risk_level"></span>
                                    </div>
                                </div>
                                <div class="pool-stats">
                                    <div class="stat-item">
                                        <div class="stat-item-value positive"><span v-text="formatPercent(pool.apr)"></span>%</div>
                                        <div class="stat-item-label">APR</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-item-value">$<span v-text="formatMillion(pool.tvl_usd)"></span>M</div>
                                        <div class="stat-item-label">TVL</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-item-value" v-text="pool.score"></div>
                                        <div class="stat-item-label">評分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LP Positions -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-wallet" style="color: #6f42c1;"></i>
                            LP持倉 (<span v-text="positions.length"></span>個)
                        </div>
                        <div class="pool-item" v-for="position in positions" :key="position.id">
                            <div class="pool-info">
                                <h4 v-text="position.pool"></h4>
                                <div class="pool-badges">
                                    <span class="badge" :class="'badge-' + position.chain" v-text="position.chain.toUpperCase()"></span>
                                    <span style="font-size: 0.8rem; color: #666;" v-text="position.range"></span>
                                </div>
                            </div>
                            <div class="pool-stats">
                                <div class="stat-item">
                                    <div class="stat-item-value" :class="position.pnl_pct >= 0 ? 'positive' : 'negative'">
                                        <span v-text="formatPercent(position.pnl_pct, true)"></span>%
                                    </div>
                                    <div class="stat-item-label">PnL</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-item-value" :class="position.il_pct >= 0 ? 'positive' : 'negative'">
                                        <span v-text="formatPercent(position.il_pct)"></span>%
                                    </div>
                                    <div class="stat-item-label">IL</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-item-value">$<span v-text="formatNumber(position.liquidity_usd)"></span></div>
                                    <div class="stat-item-label">流動性</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Agent Controls -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-robot" style="color: #007bff;"></i>
                            Agent控制
                        </div>
                        <div class="controls-grid">
                            <button class="btn btn-primary" @click="executeAgent('PoolPicker')">
                                <i class="fas fa-search"></i> 池子掃描
                            </button>
                            <button class="btn btn-warning" @click="executeAgent('RiskSentinel')">
                                <i class="fas fa-shield-alt"></i> 風險檢查
                            </button>
                            <button class="btn btn-success" @click="executeAgent('RangeRebalancer')">
                                <i class="fas fa-balance-scale"></i> 重新平衡
                            </button>
                            <button class="btn btn-primary" @click="executeAgent('HedgeAgent')">
                                <i class="fas fa-chart-line"></i> DCA執行
                            </button>
                        </div>
                        <button class="btn btn-primary" @click="forceUpdate" style="width: 100%;">
                            <i class="fas fa-sync-alt"></i> 強制更新數據
                        </button>
                    </div>

                    <!-- Agent Logs -->
                    <div class="glass-card">
                        <div class="section-title">
                            <i class="fas fa-list-alt" style="color: #6c757d;"></i>
                            Agent日誌
                        </div>
                        <div class="log-list">
                            <div v-for="log in agentLogs" :key="log.timestamp"
                                 class="log-item"
                                 :class="'log-' + log.level">
                                <div class="log-header">
                                    <div class="log-agent" v-text="log.agent"></div>
                                    <div class="log-time" v-text="formatTime(log.timestamp)"></div>
                                </div>
                                <div class="log-message" v-text="log.message"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    ws: null,
                    systemStatus: {},
                    poolData: [],
                    positions: [],
                    agentLogs: [],
                    tokenPrices: {},
                    lastUpdate: '--',
                    totalPositions: 0,
                    totalValue: 0,
                    avgAPR: 0,
                    poolCount: 0
                }
            },
            mounted() {
                this.connectWebSocket();
            },
            methods: {
                connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws`;
                    
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket連接已建立');
                    };
                    
                    this.ws.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        this.handleWebSocketMessage(message);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket連接已關閉，5秒後重新連接...');
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };
                },
                
                handleWebSocketMessage(message) {
                    if (message.type === 'dashboard_update' || message.type === 'initial_data') {
                        const data = message.data;
                        
                        this.systemStatus = data.system_status || {};
                        this.poolData = data.pool_data || [];
                        this.positions = data.positions || [];
                        this.agentLogs = data.agent_logs || [];
                        this.tokenPrices = data.token_prices || {};
                        this.lastUpdate = new Date(data.last_update).toLocaleTimeString();
                        
                        // 計算統計數據
                        this.totalPositions = this.positions.length;
                        this.totalValue = this.positions.reduce((sum, pos) => sum + pos.liquidity_usd, 0);
                        this.avgAPR = this.positions.length > 0 ? 
                            this.positions.reduce((sum, pos) => sum + pos.apr, 0) / this.positions.length : 0;
                        this.poolCount = this.poolData.length;
                    }
                },
                
                executeAgent(agentName) {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'agent_command',
                            command: 'execute_agent',
                            agent_name: agentName
                        }));
                        console.log(`正在執行 ${agentName}...`);
                    }
                },
                
                forceUpdate() {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'force_update'
                        }));
                        console.log('正在強制更新數據...');
                    }
                },
                
                emergencyExit() {
                    if (confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
                        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                            this.ws.send(JSON.stringify({
                                type: 'agent_command',
                                command: 'emergency_exit'
                            }));
                        }
                    }
                },
                
                formatTime(timestamp) {
                    return new Date(timestamp).toLocaleTimeString();
                },
                
                formatNumber(num) {
                    if (num >= 1000000) {
                        return (num / 1000000).toFixed(2) + 'M';
                    } else if (num >= 1000) {
                        return (num / 1000).toFixed(1) + 'K';
                    }
                    return num.toLocaleString();
                },
                
                formatMillion(num) {
                    return (num / 1000000).toFixed(2);
                },
                
                formatPercent(num, showSign = false) {
                    const formatted = num.toFixed(1);
                    if (showSign && num >= 0) {
                        return '+' + formatted;
                    }
                    return formatted;
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
