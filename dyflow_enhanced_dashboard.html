<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 DyFlow 真实数据Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .pools-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-section {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .agent-logs {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            height: 40%;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            height: 60%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .pool-tabs {
            display: flex;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .tab-button {
            flex: 1;
            padding: 15px;
            border: none;
            background: #e9ecef;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }

        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #eee;
        }

        .data-table tr:hover {
            background: #f8f9fa;
            cursor: pointer;
        }

        .trade-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .trade-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }

        .message.ai {
            background: #f3e5f5;
        }

        .log-entry {
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            font-family: monospace;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pool-section {
            margin-bottom: 30px;
        }

        .pool-section h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>🚀 DyFlow 真实数据Dashboard</h1>
            <div class="status-grid">
                <div class="status-card">
                    <h4>🔗 连接状态</h4>
                    <p><span class="status-indicator status-connected" id="wsStatus"></span><span id="connectionStatus">连接中...</span></p>
                </div>
                <div class="status-card">
                    <h4>💰 BNB价格</h4>
                    <p id="bnbPrice">加载中...</p>
                </div>
                <div class="status-card">
                    <h4>☀️ SOL价格</h4>
                    <p id="solPrice">加载中...</p>
                </div>
                <div class="status-card">
                    <h4>📊 数据更新</h4>
                    <p id="lastUpdate">等待数据...</p>
                </div>
                <div class="status-card">
                    <h4>🎯 活跃池子</h4>
                    <p id="activePoolsCount">0</p>
                </div>
                <div class="status-card">
                    <h4>⚡ 更新频率</h4>
                    <p>3-5秒</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Pools Section -->
            <div class="pools-section">
                <div class="pool-tabs">
                    <button class="tab-button active" onclick="showTab('bsc')">🟡 BSC池子 (真实数据)</button>
                    <button class="tab-button" onclick="showTab('solana')">🟣 Solana池子 (增强数据)</button>
                </div>

                <!-- BSC Pools -->
                <div id="bscPools" class="pool-section">
                    <h3>🟡 BSC池子 (真实数据) PancakeSwap</h3>
                    <div style="overflow-x: auto;">
                        <table class="data-table" id="bscPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>协议</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>24h量</th>
                                    <th>风险</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="bscPoolsBody">
                                <tr><td colspan="7">加载中...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Solana Pools -->
                <div id="solanaPools" class="pool-section" style="display: none;">
                    <h3>🟣 Solana池子 (增强数据) 多DEX</h3>
                    <div style="overflow-x: auto;">
                        <table class="data-table" id="solanaPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>协议</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>24h量</th>
                                    <th>风险</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="solanaPoolsBody">
                                <tr><td colspan="7">加载中...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Chat and Logs Section -->
            <div class="chat-section">
                <!-- Agent Logs -->
                <div class="agent-logs">
                    <h4>🤖 Agent日志</h4>
                    <div id="agentLogs">
                        <div class="log-entry">[系统] DyFlow Agent 启动中...</div>
                        <div class="log-entry">[连接] 正在连接到BSC和Solana网络...</div>
                        <div class="log-entry">[数据] 开始获取实时池子数据...</div>
                    </div>
                </div>

                <!-- Chat Interface -->
                <div class="chat-container">
                    <h4>💬 Agent对话</h4>
                    <div class="chat-messages" id="chatMessages">
                        <div class="message ai">
                            🤖 你好！我是DyFlow交易助手。我可以帮你：<br>
                            • 扫描最佳LP池子<br>
                            • 执行自动化投资<br>
                            • 分析风险和收益<br>
                            • 管理投资组合<br><br>
                            试试说："扫描最佳池子" 或 "我想投资BNB/USD1"
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <input type="text" class="chat-input" id="chatInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                        <button class="send-button" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Trade Modal -->
    <div id="tradeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeTradeModal()">&times;</span>
            <h3>🚀 执行LP投资</h3>
            <div id="tradeDetails"></div>
            <div style="margin-top: 20px;">
                <label>投资金额:</label>
                <input type="number" id="investAmount" placeholder="输入金额" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                <button onclick="executeInvestment()" style="width: 100%; padding: 12px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 5px; font-weight: bold; cursor: pointer;">确认投资</button>
            </div>
        </div>
    </div>

    <script>
        let ws;
        let currentPools = { bsc: [], solana: [] };
        let selectedPool = null;
        let selectedChain = null;

        // WebSocket连接
        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8001/ws');
            
            ws.onopen = function() {
                document.getElementById('connectionStatus').textContent = '已连接';
                document.getElementById('wsStatus').className = 'status-indicator status-connected';
                addLog('[连接] WebSocket连接成功');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function() {
                document.getElementById('connectionStatus').textContent = '连接断开';
                document.getElementById('wsStatus').className = 'status-indicator status-disconnected';
                addLog('[连接] WebSocket连接断开，5秒后重连...');
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                addLog('[错误] WebSocket连接错误: ' + error);
            };
        }

        function handleWebSocketMessage(data) {
            if (data.type === 'real_data_update') {
                updateDashboard(data);
            } else if (data.type === 'chat_response') {
                addChatMessage('ai', data.ai_response);
            } else if (data.type === 'trade_result') {
                handleTradeResult(data.result);
            }
        }

        function updateDashboard(data) {
            // 更新价格
            if (data.prices) {
                document.getElementById('bnbPrice').textContent = `$${data.prices.BNB || '---'}`;
                document.getElementById('solPrice').textContent = `$${data.prices.SOL || '---'}`;
            }
            
            // 更新时间
            const now = new Date();
            document.getElementById('lastUpdate').textContent = now.toLocaleTimeString();
            
            // 更新池子数据
            if (data.bsc_pools) {
                currentPools.bsc = data.bsc_pools;
                updatePoolTable('bsc', data.bsc_pools);
            }
            
            if (data.solana_pools) {
                currentPools.solana = data.solana_pools;
                updatePoolTable('solana', data.solana_pools);
            }
            
            // 更新活跃池子数量
            const totalPools = (data.bsc_pools?.length || 0) + (data.solana_pools?.length || 0);
            document.getElementById('activePoolsCount').textContent = totalPools;
            
            addLog(`[数据] 更新完成: BSC ${data.bsc_pools?.length || 0}个, Solana ${data.solana_pools?.length || 0}个池子`);
        }

        function updatePoolTable(chain, pools) {
            const tableBody = document.getElementById(chain + 'PoolsBody');
            tableBody.innerHTML = '';

            pools.slice(0, 25).forEach((pool, index) => {
                const row = tableBody.insertRow();
                const riskColor = pool.risk_level === '低' ? 'green' : pool.risk_level === '中' ? 'orange' : 'red';

                row.innerHTML = `
                    <td>${pool.pair}</td>
                    <td>${pool.protocol}</td>
                    <td><strong>${pool.apr}%</strong></td>
                    <td>$${pool.tvl}M</td>
                    <td>$${pool.volume_24h}K</td>
                    <td style="color: ${riskColor}">${pool.risk_level}</td>
                    <td><button class="trade-button" onclick="openTradeModal('${chain}', ${index})">投资</button></td>
                `;

                row.onclick = function() {
                    showPoolDetails(pool, chain);
                };
            });
        }

        function showTab(tabName) {
            // 隐藏所有标签页
            document.getElementById('bscPools').style.display = 'none';
            document.getElementById('solanaPools').style.display = 'none';

            // 显示选中的标签页
            document.getElementById(tabName + 'Pools').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function openTradeModal(chain, poolIndex) {
            const pool = currentPools[chain][poolIndex];
            selectedPool = pool;
            selectedChain = chain;

            document.getElementById('tradeDetails').innerHTML = `
                <h4>📊 池子详情</h4>
                <p><strong>链:</strong> ${chain}</p>
                <p><strong>交易对:</strong> ${pool.pair}</p>
                <p><strong>协议:</strong> ${pool.protocol}</p>
                <p><strong>APR:</strong> ${pool.apr}%</p>
                <p><strong>TVL:</strong> $${pool.tvl}M</p>
                <p><strong>风险等级:</strong> ${pool.risk_level}</p>
                <p><strong>建议:</strong> ${pool.recommendation}</p>
            `;

            document.getElementById('tradeModal').style.display = 'block';
        }

        function closeTradeModal() {
            document.getElementById('tradeModal').style.display = 'none';
        }

        function executeInvestment() {
            const amount = document.getElementById('investAmount').value;
            if (!amount || !selectedPool) {
                alert('请输入投资金额');
                return;
            }

            const tradeData = {
                chain: selectedChain,
                pool: selectedPool,
                amount: parseFloat(amount),
                action: 'invest'
            };

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'execute_trade',
                    trade_data: tradeData
                }));

                addChatMessage('user', `投资 $${amount} 到 ${selectedPool.pair} (${selectedChain})`);
                closeTradeModal();
            }
        }

        function handleTradeResult(result) {
            if (result.success) {
                addChatMessage('ai', `✅ 交易成功！\n交易哈希: ${result.tx_hash}\n预估Gas: ${result.estimated_gas}\n滑点: ${result.slippage}`);
                addLog(`[交易] 成功执行投资: ${result.tx_hash}`);
            } else {
                addChatMessage('ai', `❌ 交易失败: ${result.error}`);
            }
        }

        function showPoolDetails(pool, chain) {
            const message = `📊 ${pool.pair} (${chain}) 详情:\nAPR: ${pool.apr}%\nTVL: $${pool.tvl}M\n24h交易量: $${pool.volume_24h}K\n风险等级: ${pool.risk_level}`;
            addChatMessage('ai', message);
        }

        function addChatMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = content.replace(/\n/g, '<br>');
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function addLog(message) {
            const logsContainer = document.getElementById('agentLogs');
            const logDiv = document.createElement('div');
            logDiv.className = 'log-entry';
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logDiv);
            logsContainer.scrollTop = logsContainer.scrollHeight;

            // 保持最多50条日志
            while (logsContainer.children.length > 50) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message && ws && ws.readyState === WebSocket.OPEN) {
                addChatMessage('user', message);
                ws.send(JSON.stringify({
                    type: 'chat_message',
                    message: message
                }));
                input.value = '';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载时连接WebSocket
        window.onload = function() {
            connectWebSocket();
            addLog('[系统] Dashboard初始化完成');
        };

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('tradeModal');
            if (event.target === modal) {
                closeTradeModal();
            }
        };
    </script>
</body>
</html>
