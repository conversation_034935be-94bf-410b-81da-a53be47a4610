#!/usr/bin/env python3
"""
DyFlow v3.3 UI 測試腳本
測試新的 React UI 功能，包括投資組合、階段監控和真實數據集成
"""

import asyncio
import subprocess
import time
import requests
import json
from datetime import datetime
import sys
import os

class DyFlowV33UITester:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.base_url = "http://localhost:8001"
        self.frontend_url = "http://localhost:3000"
        
    async def start_backend(self):
        """啟動後端服務"""
        print("🚀 啟動 DyFlow v3.3 後端服務...")
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, "dyflow_real_data_backend.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待後端啟動
            for i in range(30):
                try:
                    response = requests.get(f"{self.base_url}/api/real-data", timeout=2)
                    if response.status_code == 200:
                        print("✅ 後端服務已啟動")
                        return True
                except:
                    time.sleep(1)
                    print(f"⏳ 等待後端啟動... ({i+1}/30)")
            
            print("❌ 後端啟動超時")
            return False
            
        except Exception as e:
            print(f"❌ 後端啟動失敗: {e}")
            return False
    
    async def start_frontend(self):
        """啟動前端服務"""
        print("🚀 啟動 React UI...")
        try:
            # 切換到 react-ui 目錄並啟動
            os.chdir("react-ui")
            self.frontend_process = subprocess.Popen([
                "npm", "run", "dev"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待前端啟動
            for i in range(60):
                try:
                    response = requests.get(self.frontend_url, timeout=2)
                    if response.status_code == 200:
                        print("✅ React UI 已啟動")
                        os.chdir("..")  # 回到根目錄
                        return True
                except:
                    time.sleep(1)
                    if i % 10 == 0:
                        print(f"⏳ 等待前端啟動... ({i+1}/60)")
            
            print("❌ 前端啟動超時")
            os.chdir("..")
            return False
            
        except Exception as e:
            print(f"❌ 前端啟動失敗: {e}")
            os.chdir("..")
            return False
    
    async def test_api_endpoints(self):
        """測試 API 端點"""
        print("\n📊 測試 API 端點...")
        
        endpoints = [
            ("/api/real-data", "真實數據"),
            ("/api/agents/status", "Agent 狀態"),
            ("/api/portfolio/status", "投資組合狀態"),
            ("/api/positions", "LP 持倉"),
            ("/api/phases/status", "階段狀態"),
            ("/api/risk/assessment", "風險評估")
        ]
        
        results = {}
        
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    results[endpoint] = {
                        "status": "✅ 成功",
                        "data_size": len(str(data)),
                        "sample": str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                    }
                    print(f"✅ {name}: {response.status_code}")
                else:
                    results[endpoint] = {
                        "status": f"❌ 錯誤 {response.status_code}",
                        "error": response.text[:200]
                    }
                    print(f"❌ {name}: {response.status_code}")
                    
            except Exception as e:
                results[endpoint] = {
                    "status": f"❌ 異常",
                    "error": str(e)
                }
                print(f"❌ {name}: {str(e)}")
        
        return results
    
    async def test_portfolio_data(self):
        """測試投資組合數據"""
        print("\n💼 測試投資組合數據...")
        
        try:
            # 測試投資組合狀態
            response = requests.get(f"{self.base_url}/api/portfolio/status", timeout=10)
            if response.status_code == 200:
                portfolio = response.json()
                print(f"✅ 投資組合總價值: ${portfolio.get('total_value_usd', 0):,.2f}")
                print(f"✅ 24h PnL: ${portfolio.get('total_pnl_usd', 0):,.2f}")
                print(f"✅ 總體 IL: ${portfolio.get('total_il_usd', 0):,.2f}")
                print(f"✅ 平均 APR: {portfolio.get('avg_apr', 0):.1f}%")
                print(f"✅ 風險評分: {portfolio.get('risk_score', 0):.1f}/100")
            
            # 測試持倉數據
            response = requests.get(f"{self.base_url}/api/positions", timeout=10)
            if response.status_code == 200:
                positions = response.json()
                print(f"✅ LP 持倉數量: {len(positions)}")
                for pos in positions[:3]:  # 顯示前3個持倉
                    print(f"   - {pos.get('pool', 'Unknown')}: ${pos.get('liquidity_usd', 0):,.0f} "
                          f"(APR: {pos.get('apr', 0):.1f}%, IL: {pos.get('il_percentage', 0):.2f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 投資組合數據測試失敗: {e}")
            return False
    
    async def test_phase_monitoring(self):
        """測試階段監控"""
        print("\n🔄 測試階段監控...")
        
        try:
            response = requests.get(f"{self.base_url}/api/phases/status", timeout=10)
            if response.status_code == 200:
                phases = response.json()
                current_phase = phases.get('current_phase', 0)
                completed_phases = phases.get('completed_phases', 0)
                progress = phases.get('progress_percentage', 0)
                
                print(f"✅ 當前階段: Phase {current_phase}")
                print(f"✅ 已完成階段: {completed_phases}/9")
                print(f"✅ 整體進度: {progress:.1f}%")
                
                # 顯示各階段狀態
                phase_status = phases.get('phase_status', {})
                for phase_id, status in phase_status.items():
                    phase_name = f"Phase {phase_id}"
                    phase_status_text = status.get('status', 'unknown')
                    print(f"   - {phase_name}: {phase_status_text}")
            
            return True
            
        except Exception as e:
            print(f"❌ 階段監控測試失敗: {e}")
            return False
    
    async def test_real_data_integration(self):
        """測試真實數據集成"""
        print("\n🌐 測試真實數據集成...")
        
        try:
            response = requests.get(f"{self.base_url}/api/real-data", timeout=30)
            if response.status_code == 200:
                data = response.json()
                
                bsc_pools = data.get('bsc_pools', [])
                solana_pools = data.get('solana_pools', [])
                prices = data.get('prices', {})
                
                print(f"✅ BSC 池子數量: {len(bsc_pools)}")
                print(f"✅ Solana 池子數量: {len(solana_pools)}")
                print(f"✅ 代幣價格數量: {len(prices)}")
                
                # 顯示前3個 BSC 池子
                if bsc_pools:
                    print("   BSC 池子樣本:")
                    for pool in bsc_pools[:3]:
                        print(f"   - {pool.get('pair', 'Unknown')}: "
                              f"TVL ${pool.get('tvl_usd', 0):,.0f}, "
                              f"APR {pool.get('apr', 0):.1f}%")
                
                # 顯示前3個 Solana 池子
                if solana_pools:
                    print("   Solana 池子樣本:")
                    for pool in solana_pools[:3]:
                        print(f"   - {pool.get('pair', 'Unknown')}: "
                              f"TVL ${pool.get('tvl_usd', 0):,.0f}, "
                              f"APR {pool.get('apr', 0):.1f}%")
                
                # 顯示代幣價格
                if prices:
                    print("   代幣價格:")
                    for token, price in list(prices.items())[:5]:
                        if isinstance(price, (int, float)):
                            print(f"   - {token}: ${price:.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 真實數據集成測試失敗: {e}")
            return False
    
    async def run_ui_tests(self):
        """運行 UI 測試"""
        print("\n🧪 運行 UI 測試...")
        
        try:
            # 使用 Playwright 測試 UI
            os.chdir("react-ui")
            result = subprocess.run([
                "npx", "playwright", "test", "--headed"
            ], capture_output=True, text=True, timeout=120)
            
            os.chdir("..")
            
            if result.returncode == 0:
                print("✅ UI 測試通過")
                return True
            else:
                print(f"❌ UI 測試失敗: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ UI 測試異常: {e}")
            os.chdir("..")
            return False
    
    async def cleanup(self):
        """清理資源"""
        print("\n🧹 清理資源...")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ 前端服務已停止")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ 後端服務已停止")
    
    async def run_full_test(self):
        """運行完整測試"""
        print("🚀 開始 DyFlow v3.3 UI 完整測試")
        print("=" * 60)
        
        try:
            # 1. 啟動後端
            if not await self.start_backend():
                return False
            
            # 2. 測試 API 端點
            api_results = await self.test_api_endpoints()
            
            # 3. 測試投資組合數據
            portfolio_ok = await self.test_portfolio_data()
            
            # 4. 測試階段監控
            phases_ok = await self.test_phase_monitoring()
            
            # 5. 測試真實數據集成
            data_ok = await self.test_real_data_integration()
            
            # 6. 啟動前端
            if not await self.start_frontend():
                return False
            
            # 7. 運行 UI 測試
            ui_ok = await self.run_ui_tests()
            
            # 總結結果
            print("\n" + "=" * 60)
            print("📊 測試結果總結")
            print(f"API 端點: {'✅ 通過' if all('✅' in r['status'] for r in api_results.values()) else '❌ 失敗'}")
            print(f"投資組合: {'✅ 通過' if portfolio_ok else '❌ 失敗'}")
            print(f"階段監控: {'✅ 通過' if phases_ok else '❌ 失敗'}")
            print(f"真實數據: {'✅ 通過' if data_ok else '❌ 失敗'}")
            print(f"UI 測試: {'✅ 通過' if ui_ok else '❌ 失敗'}")
            
            overall_success = all([
                all('✅' in r['status'] for r in api_results.values()),
                portfolio_ok, phases_ok, data_ok, ui_ok
            ])
            
            if overall_success:
                print("\n🎉 DyFlow v3.3 UI 測試全部通過！")
                print(f"🌐 前端地址: {self.frontend_url}")
                print(f"🔗 後端地址: {self.base_url}")
                print("\n請在瀏覽器中訪問前端地址查看 UI")
                
                # 保持服務運行
                print("\n⏳ 服務將保持運行，按 Ctrl+C 停止...")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n👋 用戶中斷，正在停止服務...")
            else:
                print("\n⚠️ 部分測試失敗，請檢查日誌")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ 測試過程中發生異常: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    tester = DyFlowV33UITester()
    success = await tester.run_full_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
