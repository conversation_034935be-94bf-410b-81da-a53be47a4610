#!/usr/bin/env python3
"""
DyFlow v3.3 Agno Framework 啟動腳本
使用正確的 Agno 架構替代 NATS 消息總線
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from workflows.dyflow_agno_workflow import DyFlowAgnoWorkflow, AGNO_AVAILABLE
    from agno.utils.pprint import pprint_run_response
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    print("請確保已安裝 Agno Framework: pip install agno")
    sys.exit(1)

def print_header():
    """打印標題"""
    print("=" * 60)
    print("  🚀 DyFlow v3.3 - Agno Framework 架構")
    print("  24/7 自動化單邊 LP 策略系統")
    print("=" * 60)
    print()

def check_prerequisites():
    """檢查前置條件"""
    print("🔍 檢查系統前置條件...")
    
    issues = []
    
    # 檢查 Agno Framework
    if not AGNO_AVAILABLE:
        issues.append("❌ Agno Framework 未安裝")
    else:
        print("✅ Agno Framework: 可用")
    
    # 檢查 Ollama 服務器
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            print("✅ Ollama 服務器: 運行中")
        else:
            issues.append("❌ Ollama 服務器: 無響應")
    except Exception:
        issues.append("❌ Ollama 服務器: 未運行")
    
    # 檢查數據目錄
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir(parents=True, exist_ok=True)
        print("✅ 數據目錄: 已創建")
    else:
        print("✅ 數據目錄: 存在")
    
    if issues:
        print("\n⚠️  發現問題:")
        for issue in issues:
            print(f"  {issue}")
        print("\n建議解決方案:")
        print("1. 安裝 Agno: pip install agno")
        print("2. 啟動 Ollama: ollama serve")
        print("3. 下載模型: ollama pull qwen3:7b")
        return False
    
    print("✅ 所有前置條件滿足")
    return True

def main():
    """主函數"""
    print_header()
    
    # 檢查前置條件
    if not check_prerequisites():
        print("\n❌ 前置條件不滿足，無法啟動")
        return
    
    print("\n🚀 啟動 DyFlow Agno Workflow...")
    print("=" * 40)
    
    try:
        # 創建 workflow
        workflow = DyFlowAgnoWorkflow(
            session_id=f"dyflow-main-{int(asyncio.get_event_loop().time())}"
        )
        
        print(f"📋 Workflow 會話 ID: {workflow.session_id}")
        print(f"💾 存儲位置: {workflow.storage.db_file}")
        print()
        
        # 執行 workflow
        print("🔄 開始執行 8-phase 啟動序列...")
        print("-" * 40)
        
        responses = workflow.run(start_phase=0)
        
        # 打印響應
        for response in responses:
            if response.content:
                print(f"📝 {response.content}")
                print("-" * 40)
        
        # 獲取最終狀態
        final_status = workflow.get_current_status()
        
        print("\n📊 最終狀態報告:")
        print(f"  整體狀態: {final_status.overall_status}")
        print(f"  進度: {final_status.progress_percentage:.1f}%")
        print(f"  當前階段: Phase {final_status.current_phase}")
        print(f"  完成階段: {len([p for p in final_status.phase_results if p.status == 'completed'])}/9")
        
        if final_status.overall_status == "completed":
            print("\n🎉 DyFlow 系統啟動完成！")
            print("系統現在可以開始執行 LP 策略。")
        elif final_status.overall_status == "failed":
            print("\n❌ 系統啟動失敗")
            failed_phases = [p for p in final_status.phase_results if p.status == "failed"]
            for phase in failed_phases:
                print(f"  Phase {phase.phase_id} ({phase.phase_name}): {phase.error_message}")
        else:
            print(f"\n⏸️  系統啟動暫停在 Phase {final_status.current_phase}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用戶中斷，正在停止...")
    except Exception as e:
        print(f"\n❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()

def test_agno_communication():
    """測試 Agno Agent 通訊"""
    print("\n🧪 測試 Agno Agent 通訊...")
    
    try:
        from agno.agent import Agent
        from agno.team import Team
        from agno.models.ollama import Ollama
        
        # 創建測試 agents
        agent1 = Agent(
            name="TestAgent1",
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=["You are a test agent. Respond with 'Agent 1 ready!'"]
        )
        
        agent2 = Agent(
            name="TestAgent2", 
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=["You are a test agent. Respond with 'Agent 2 ready!'"]
        )
        
        # 創建 team
        team = Team(
            name="TestTeam",
            mode="coordinate",
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            members=[agent1, agent2],
            instructions=["Coordinate the test agents to confirm they are ready."]
        )
        
        # 測試通訊
        response = team.run("Test agent communication")
        
        if response and response.content:
            print("✅ Agno Agent 通訊測試成功")
            print(f"📝 響應: {response.content[:200]}...")
            return True
        else:
            print("❌ Agno Agent 通訊測試失敗 - 無響應")
            return False
            
    except Exception as e:
        print(f"❌ Agno Agent 通訊測試失敗: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="DyFlow v3.3 Agno Framework 啟動器")
    parser.add_argument("--test", action="store_true", help="僅測試 Agno 通訊")
    parser.add_argument("--phase", type=int, default=0, help="從指定階段開始 (0-8)")
    
    args = parser.parse_args()
    
    if args.test:
        print_header()
        if check_prerequisites():
            test_agno_communication()
    else:
        main()
