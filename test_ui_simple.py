#!/usr/bin/env python3
"""
簡單的 UI 測試腳本
使用 requests 和 selenium 來測試 DyFlow v3.3 UI
"""

import requests
import time
import json
from datetime import datetime

def test_backend_apis():
    """測試後端 API"""
    print("🧪 測試後端 API...")
    
    base_url = "http://localhost:8001"
    
    endpoints = [
        ("/api/real-data", "真實數據"),
        ("/api/agents/status", "Agent 狀態"),
        ("/api/portfolio/status", "投資組合狀態"),
        ("/api/positions", "LP 持倉"),
        ("/api/phases/status", "階段狀態"),
        ("/api/risk/assessment", "風險評估")
    ]
    
    results = {}
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                results[endpoint] = {
                    "status": "✅ 成功",
                    "data_size": len(str(data))
                }
                print(f"✅ {name}: {response.status_code} ({len(str(data))} bytes)")
            else:
                results[endpoint] = {
                    "status": f"❌ 錯誤 {response.status_code}",
                    "error": response.text[:200]
                }
                print(f"❌ {name}: {response.status_code}")
                
        except Exception as e:
            results[endpoint] = {
                "status": f"❌ 異常",
                "error": str(e)
            }
            print(f"❌ {name}: {str(e)}")
    
    return results

def test_frontend_accessibility():
    """測試前端可訪問性"""
    print("\n🌐 測試前端可訪問性...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            print("✅ 前端服務可訪問")
            print(f"✅ 響應大小: {len(response.text)} bytes")
            
            # 檢查是否包含關鍵內容
            content = response.text.lower()
            checks = [
                ("dyflow", "DyFlow 標題"),
                ("dashboard", "Dashboard 關鍵字"),
                ("react", "React 框架"),
                ("portfolio", "投資組合"),
                ("phases", "階段"),
                ("agents", "Agent")
            ]
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"✅ 包含 {description}")
                else:
                    print(f"⚠️ 缺少 {description}")
            
            return True
        else:
            print(f"❌ 前端服務錯誤: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端服務異常: {e}")
        return False

def test_portfolio_data():
    """測試投資組合數據"""
    print("\n💼 測試投資組合數據...")
    
    try:
        # 測試投資組合狀態
        response = requests.get("http://localhost:8001/api/portfolio/status", timeout=10)
        if response.status_code == 200:
            portfolio = response.json()
            
            print(f"✅ 投資組合總價值: ${portfolio.get('total_value_usd', 0):,.2f}")
            print(f"✅ 24h PnL: ${portfolio.get('total_pnl_usd', 0):,.2f} ({portfolio.get('pnl_percentage', 0):.2f}%)")
            print(f"✅ 總體 IL: ${portfolio.get('total_il_usd', 0):,.2f} ({portfolio.get('il_percentage', 0):.2f}%)")
            print(f"✅ 平均 APR: {portfolio.get('avg_apr', 0):.1f}%")
            print(f"✅ 24h 手續費: ${portfolio.get('fees_24h_usd', 0):,.2f}")
            print(f"✅ 風險評分: {portfolio.get('risk_score', 0):.1f}/100")
            print(f"✅ 持倉數量: {portfolio.get('positions_count', 0)}")
            print(f"✅ 活躍策略: {', '.join(portfolio.get('active_strategies', []))}")
            print(f"✅ 支持鏈: {', '.join(portfolio.get('chains', []))}")
            
            # 驗證數據合理性
            if portfolio.get('total_value_usd', 0) > 0:
                print("✅ 投資組合價值合理")
            else:
                print("⚠️ 投資組合價值異常")
            
            if 0 <= portfolio.get('risk_score', 0) <= 100:
                print("✅ 風險評分範圍正常")
            else:
                print("⚠️ 風險評分範圍異常")
        
        # 測試持倉數據
        response = requests.get("http://localhost:8001/api/positions", timeout=10)
        if response.status_code == 200:
            positions = response.json()
            print(f"\n✅ LP 持倉數量: {len(positions)}")
            
            for i, pos in enumerate(positions[:3]):  # 顯示前3個持倉
                print(f"   持倉 {i+1}: {pos.get('pool', 'Unknown')}")
                print(f"     - 鏈: {pos.get('chain', 'Unknown')}")
                print(f"     - 策略: {pos.get('strategy', 'Unknown')}")
                print(f"     - 流動性: ${pos.get('liquidity_usd', 0):,.0f}")
                print(f"     - PnL: ${pos.get('pnl_usd', 0):,.2f}")
                print(f"     - IL: {pos.get('il_percentage', 0):.2f}%")
                print(f"     - APR: {pos.get('apr', 0):.1f}%")
                print(f"     - 狀態: {pos.get('status', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 投資組合數據測試失敗: {e}")
        return False

def test_phase_monitoring():
    """測試階段監控"""
    print("\n🔄 測試階段監控...")
    
    try:
        response = requests.get("http://localhost:8001/api/phases/status", timeout=10)
        if response.status_code == 200:
            phases = response.json()
            
            current_phase = phases.get('current_phase', 0)
            completed_phases = phases.get('completed_phases', 0)
            total_phases = phases.get('total_phases', 9)
            progress = phases.get('progress_percentage', 0)
            
            print(f"✅ 當前階段: Phase {current_phase}")
            print(f"✅ 已完成階段: {completed_phases}/{total_phases}")
            print(f"✅ 整體進度: {progress:.1f}%")
            
            # 顯示各階段狀態
            phase_status = phases.get('phase_status', {})
            phase_names = [
                "系統初始化", "健康檢查", "UI 啟動", "錢包測試", "市場情報",
                "投資組合", "策略生成", "交易執行", "風控監控"
            ]
            
            print("\n階段詳情:")
            for i in range(9):
                phase_id = str(i)
                status = phase_status.get(phase_id, {})
                phase_status_text = status.get('status', 'unknown')
                phase_name = phase_names[i] if i < len(phase_names) else f"Phase {i}"
                
                status_icon = {
                    'completed': '✅',
                    'running': '🔄',
                    'failed': '❌',
                    'pending': '⏳'
                }.get(phase_status_text, '❓')
                
                print(f"   {status_icon} Phase {i}: {phase_name} - {phase_status_text}")
                
                if status.get('started_at'):
                    print(f"      開始時間: {status.get('started_at')}")
                if status.get('completed_at'):
                    print(f"      完成時間: {status.get('completed_at')}")
                if status.get('error'):
                    print(f"      錯誤: {status.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 階段監控測試失敗: {e}")
        return False

def test_real_data_integration():
    """測試真實數據集成"""
    print("\n🌐 測試真實數據集成...")
    
    try:
        response = requests.get("http://localhost:8001/api/real-data", timeout=30)
        if response.status_code == 200:
            data = response.json()
            
            bsc_pools = data.get('bsc_pools', [])
            solana_pools = data.get('solana_pools', [])
            prices = data.get('prices', {})
            
            print(f"✅ BSC 池子數量: {len(bsc_pools)}")
            print(f"✅ Solana 池子數量: {len(solana_pools)}")
            print(f"✅ 代幣價格數量: {len(prices)}")
            
            # 顯示 BSC 池子樣本
            if bsc_pools:
                print("\n   BSC 池子樣本 (前3個):")
                for i, pool in enumerate(bsc_pools[:3]):
                    print(f"   {i+1}. {pool.get('pair', 'Unknown')}")
                    print(f"      TVL: ${pool.get('tvl_usd', 0):,.0f}")
                    print(f"      APR: {pool.get('apr', 0):.1f}%")
                    print(f"      協議: {pool.get('protocol', 'Unknown')}")
            
            # 顯示 Solana 池子樣本
            if solana_pools:
                print("\n   Solana 池子樣本 (前3個):")
                for i, pool in enumerate(solana_pools[:3]):
                    print(f"   {i+1}. {pool.get('pair', 'Unknown')}")
                    print(f"      TVL: ${pool.get('tvl_usd', 0):,.0f}")
                    print(f"      APR: {pool.get('apr', 0):.1f}%")
                    print(f"      協議: {pool.get('protocol', 'Unknown')}")
            
            # 顯示代幣價格
            if prices:
                print("\n   代幣價格:")
                for token, price in list(prices.items())[:5]:
                    if isinstance(price, (int, float)):
                        print(f"   - {token}: ${price:.2f}")
            
            # 數據質量檢查
            total_pools = len(bsc_pools) + len(solana_pools)
            if total_pools > 0:
                print(f"\n✅ 總池子數量: {total_pools}")
                
                # 檢查高 APR 池子
                high_apr_pools = [p for p in bsc_pools + solana_pools if p.get('apr', 0) > 100]
                print(f"✅ 高收益池子 (APR > 100%): {len(high_apr_pools)}")
                
                # 檢查高 TVL 池子
                high_tvl_pools = [p for p in bsc_pools + solana_pools if p.get('tvl_usd', 0) > 100000]
                print(f"✅ 高流動性池子 (TVL > $100K): {len(high_tvl_pools)}")
            else:
                print("⚠️ 沒有獲取到池子數據")
        
        return True
        
    except Exception as e:
        print(f"❌ 真實數據集成測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始 DyFlow v3.3 UI 簡單測試")
    print("=" * 60)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 執行各項測試
    tests = [
        ("後端 API", test_backend_apis),
        ("前端可訪問性", test_frontend_accessibility),
        ("投資組合數據", test_portfolio_data),
        ("階段監控", test_phase_monitoring),
        ("真實數據集成", test_real_data_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results[test_name] = False
    
    # 總結結果
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n總體結果: {passed}/{total} 測試通過 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有測試通過！DyFlow v3.3 UI 運行正常")
        print("\n🌐 訪問地址:")
        print("   前端: http://localhost:3000")
        print("   後端: http://localhost:8001")
        print("\n📋 功能特點:")
        print("   ✅ 投資組合總體 IL 預估和 24hr 收益預估")
        print("   ✅ 八階段啟動序列監控")
        print("   ✅ 真實數據監控 (BSC + Solana)")
        print("   ✅ 七大 Agent 狀態顯示")
        print("   ✅ 策略映射矩陣")
        print("   ✅ 風控指標監控")
        print("   ✅ 模擬交易執行")
    else:
        print(f"\n⚠️ {total-passed} 個測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
