#!/usr/bin/env python3
"""
DyFlow Main - 主要啟動腳本
基於PRD v3.0實現的24/7自動化單邊LP策略系統
Framework: Agno | Chains: BSC (PancakeSwap v3) & Solana (Meteora DLMM v2)
"""

import asyncio
import signal
import sys
import os
from typing import Optional
from datetime import datetime
import structlog
import argparse

# 添加src到路径
sys.path.insert(0, 'src')

# DyFlow imports
from src.supervisor import DyFlowSupervisor, SupervisorConfig
from src.utils.config import Config
from src.utils.helpers import setup_logging
from src.utils.exceptions import DyFlowException

# 設置日誌
logger = structlog.get_logger(__name__)

class DyFlowSystem:
    """DyFlow系統主類"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self.config: Optional[Config] = None
        self.supervisor: Optional[DyFlowSupervisor] = None
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
    async def initialize(self) -> None:
        """初始化系統"""
        try:
            logger.info("dyflow_system_initializing")
            
            # 載入配置
            self.config = Config()
            
            # 創建Supervisor配置
            supervisor_config = SupervisorConfig(
                enable_agno=self.config.get('supervisor.enable_agno', True),
                enable_scheduling=self.config.get('supervisor.enable_scheduling', True),
                enable_monitoring=self.config.get('supervisor.enable_monitoring', True),
                max_concurrent_agents=self.config.get('supervisor.max_concurrent_agents', 5),
                agent_timeout_seconds=self.config.get('supervisor.agent_timeout_seconds', 300),
                workflow_timeout_seconds=self.config.get('supervisor.workflow_timeout_seconds', 600),
                error_retry_attempts=self.config.get('supervisor.error_retry_attempts', 3),
                error_retry_delay_seconds=self.config.get('supervisor.error_retry_delay_seconds', 30)
            )
            
            # 創建Supervisor
            self.supervisor = DyFlowSupervisor(self.config, supervisor_config)
            await self.supervisor.initialize()
            
            logger.info("dyflow_system_initialized")
            
        except Exception as e:
            logger.error("dyflow_system_initialization_failed", error=str(e))
            raise DyFlowException(f"DyFlow系統初始化失敗: {e}")
    
    async def start(self) -> None:
        """啟動系統"""
        if self.is_running:
            logger.warning("dyflow_system_already_running")
            return
        
        try:
            if not self.supervisor:
                await self.initialize()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("dyflow_system_starting",
                       start_time=self.start_time.isoformat())
            
            # 設置信號處理
            self._setup_signal_handlers()
            
            # 啟動Supervisor
            await self.supervisor.start()
            
        except Exception as e:
            logger.error("dyflow_system_start_failed", error=str(e))
            self.is_running = False
            raise
    
    async def stop(self) -> None:
        """停止系統"""
        if not self.is_running:
            logger.info("dyflow_system_not_running")
            return
        
        try:
            logger.info("dyflow_system_stopping")
            
            self.is_running = False
            
            # 停止Supervisor
            if self.supervisor:
                await self.supervisor.stop()
            
            # 計算運行時間
            if self.start_time:
                uptime = datetime.now() - self.start_time
                logger.info("dyflow_system_stopped",
                           uptime_seconds=uptime.total_seconds())
            
        except Exception as e:
            logger.error("dyflow_system_stop_failed", error=str(e))
    
    def _setup_signal_handlers(self) -> None:
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info("signal_received", signal=signum)
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def get_system_status(self) -> dict:
        """獲取系統狀態"""
        status = {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            "supervisor_status": None
        }
        
        if self.supervisor:
            status["supervisor_status"] = self.supervisor.get_system_status()
        
        return status

async def run_system(config_path: str, mode: str = "full") -> None:
    """運行DyFlow系統"""
    try:
        # 設置日誌
        setup_logging()
        
        logger.info("dyflow_main_starting",
                   config_path=config_path,
                   mode=mode,
                   python_version=sys.version,
                   working_directory=os.getcwd())
        
        # 創建系統實例
        dyflow_system = DyFlowSystem(config_path)
        
        if mode == "full":
            # 完整模式 - 啟動所有組件
            await dyflow_system.start()
            
            # 保持運行直到收到停止信號
            try:
                while dyflow_system.is_running:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("keyboard_interrupt_received")
            finally:
                await dyflow_system.stop()
                
        elif mode == "test":
            # 測試模式 - 初始化並運行一次
            await dyflow_system.initialize()
            
            # 手動執行一次所有Agent
            if dyflow_system.supervisor:
                logger.info("test_mode_executing_agents")
                
                # 執行PoolPicker
                pool_picker_result = await dyflow_system.supervisor.execute_agent_manually('pool_picker')
                logger.info("pool_picker_test_result", result=pool_picker_result.status)
                
                # 執行RiskSentinel
                risk_sentinel_result = await dyflow_system.supervisor.execute_agent_manually('risk_sentinel')
                logger.info("risk_sentinel_test_result", result=risk_sentinel_result.status)
                
                # 執行PortfolioManager
                portfolio_result = await dyflow_system.supervisor.execute_agent_manually('portfolio_manager')
                logger.info("portfolio_manager_test_result", result=portfolio_result.status)
                
                logger.info("test_mode_completed")
            
        elif mode == "status":
            # 狀態模式 - 只顯示系統狀態
            await dyflow_system.initialize()
            status = dyflow_system.get_system_status()
            
            print("\n" + "="*60)
            print("DyFlow System Status")
            print("="*60)
            print(f"Running: {status['is_running']}")
            print(f"Start Time: {status['start_time']}")
            print(f"Uptime: {status['uptime_seconds']:.0f} seconds")
            
            if status['supervisor_status']:
                supervisor_status = status['supervisor_status']
                print(f"\nSupervisor:")
                print(f"  Agno Enabled: {supervisor_status['supervisor']['agno_enabled']}")
                print(f"  Agents: {len(supervisor_status['agents'])}")
                print(f"  Workflows: {len(supervisor_status['workflows'])}")
                print(f"  Recent Errors: {len(supervisor_status['recent_errors'])}")
            
            print("="*60)
            
        else:
            raise DyFlowException(f"不支持的運行模式: {mode}")
            
    except Exception as e:
        logger.error("dyflow_main_failed", error=str(e))
        sys.exit(1)

def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description="DyFlow - 24/7自動化單邊LP策略系統",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
運行模式:
  full    - 完整模式，啟動所有組件並持續運行 (默認)
  test    - 測試模式，初始化並執行一次所有Agent
  status  - 狀態模式，顯示系統狀態信息

示例:
  python dyflow_main.py                    # 完整模式
  python dyflow_main.py --mode test        # 測試模式
  python dyflow_main.py --mode status      # 狀態模式
  python dyflow_main.py --config custom.yaml  # 自定義配置文件
        """
    )
    
    parser.add_argument(
        "--config",
        default="config/config.yaml",
        help="配置文件路徑 (默認: config/config.yaml)"
    )
    
    parser.add_argument(
        "--mode",
        choices=["full", "test", "status"],
        default="full",
        help="運行模式 (默認: full)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日誌級別 (默認: INFO)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="DyFlow v3.0 - Low-Float Meme Coin LP Strategy System"
    )
    
    args = parser.parse_args()
    
    # 設置日誌級別
    os.environ['LOG_LEVEL'] = args.log_level
    
    # 檢查配置文件
    if not os.path.exists(args.config):
        print(f"錯誤: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 運行系統
    try:
        asyncio.run(run_system(args.config, args.mode))
    except KeyboardInterrupt:
        print("\n程序被用戶中斷")
        sys.exit(0)
    except Exception as e:
        print(f"程序運行失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
