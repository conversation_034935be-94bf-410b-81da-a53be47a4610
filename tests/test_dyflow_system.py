#!/usr/bin/env python3
"""
DyFlow系统测试
基于Agno Framework的LP监控系统测试
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')


async def test_dyflow_initialization():
    """测试DyFlow系统初始化"""
    print("🧪 测试DyFlow系统初始化...")
    
    try:
        # 导入主系统
        from dyflow_main import DyFlowSystem
        
        # 创建系统实例
        dyflow = DyFlowSystem()
        
        # 测试初始化
        success = await dyflow.initialize()
        
        if success:
            print("✅ DyFlow系统初始化成功")
            return True
        else:
            print("❌ DyFlow系统初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 初始化测试异常: {e}")
        return False


async def test_workflow_execution():
    """测试工作流程执行"""
    print("🧪 测试工作流程执行...")
    
    try:
        from dyflow_main import DyFlowSystem
        
        dyflow = DyFlowSystem()
        
        # 初始化系统
        if not await dyflow.initialize():
            print("❌ 系统初始化失败")
            return False
        
        # 执行监控周期
        result = await dyflow.run_monitoring_cycle(chains=['bsc'], max_pools=3)
        
        if result.get('status') == 'completed':
            print("✅ 工作流程执行成功")
            print(f"   工作流程ID: {result.get('workflow_id')}")
            return True
        else:
            print(f"❌ 工作流程执行失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 工作流程测试异常: {e}")
        return False


async def test_agno_framework():
    """测试Agno Framework组件"""
    print("🧪 测试Agno Framework组件...")
    
    try:
        # 测试Workflow导入
        from agno.workflow import Workflow
        print("✅ Agno Workflow导入成功")
        
        # 测试Agent导入
        from agno.agent import Agent
        print("✅ Agno Agent导入成功")
        
        # 测试Storage导入
        from agno.storage.sqlite import SqliteStorage
        print("✅ Agno Storage导入成功")
        
        # 测试LP监控工作流程
        from src.workflows.lp_monitoring_workflow import LPMonitoringWorkflow
        
        workflow = LPMonitoringWorkflow()
        print("✅ LP监控工作流程创建成功")
        print(f"   Agent数量: 3")
        
        return True
        
    except Exception as e:
        print(f"❌ Agno Framework测试异常: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🌟 DyFlow系统测试套件")
    print("=" * 50)
    
    tests = [
        ("Agno Framework组件", test_agno_framework),
        ("DyFlow系统初始化", test_dyflow_initialization),
        ("工作流程执行", test_workflow_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果总结
    print(f"\n📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    print("使用方法:")
    print("  python tests/test_dyflow_system.py")
    print()
    
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
