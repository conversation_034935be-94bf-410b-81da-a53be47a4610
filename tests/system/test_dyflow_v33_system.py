"""
DyFlow v3.3 系統測試
測試 PRD v3.3 定義的八階段啟動序列和 Agent 架構
"""

import asyncio
import pytest
import json
import sys
import os
from datetime import datetime, timezone
from typing import Dict, Any

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 測試配置
TEST_CONFIG = {
    'health_guard': {
        'check_interval': 30,
        'bsc_rpc_url': 'https://bsc-dataseed.binance.org/',
        'solana_rpc_url': 'https://api.mainnet-beta.solana.com',
        'pancake_subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
        'meteora_api_url': 'https://dammv2-api.meteora.ag',
        'supabase_url': 'https://test.supabase.co'
    },
    'market_intel': {
        'scan_interval': 30,
        'damm_scanner': {
            'meteora_api_url': 'https://dammv2-api.meteora.ag',
            'timeout': 30,
            'max_pools': 10,
            'min_tvl': 100000,
            'max_created_days': 7,
            'min_fee_tvl_ratio': 0.01,
            'min_fees_24h': 1
        },
        'chain_scanner': {
            'timeout': 30
        }
    },
    'portfolio': {
        'nav_update_interval': 300
    },
    'strategy': {
        'strategy_types': ['SPOT_BALANCED', 'CURVE_BALANCED', 'BID_ASK_BALANCED', 'SPOT_IMBALANCED_DAMM']
    },
    'execution': {
        'max_concurrent_tx': 3
    },
    'risk': {
        'monitoring_interval': 60
    },
    'fee_collector': {
        'schedule': '0 2 * * *'
    }
}

class TestDyFlowV33System:
    """DyFlow v3.3 系統測試類"""
    
    @pytest.mark.asyncio
    async def test_supervisor_agent_initialization(self):
        """測試 SupervisorAgent 初始化"""
        try:
            from src.core.dyflow_v33_supervisor import SupervisorAgent
            
            supervisor = SupervisorAgent(TEST_CONFIG)
            
            # 檢查初始狀態
            assert supervisor.current_phase.value == 0  # Phase 0
            assert not supervisor.is_running
            assert len(supervisor.phase_status) == 9  # 9 個階段 (0-8)
            
            print("✅ SupervisorAgent 初始化測試通過")
            return True
            
        except Exception as e:
            print(f"❌ SupervisorAgent 初始化測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_health_guard_agent(self):
        """測試 HealthGuardAgent"""
        try:
            from src.agents.health_guard_agent import HealthGuardAgent
            
            health_agent = HealthGuardAgent(TEST_CONFIG['health_guard'])
            
            # 執行健康檢查
            health_result = await health_agent.check_system_health()
            
            # 驗證結果
            assert 'health_score' in health_result
            assert 'component_status' in health_result
            assert 'last_check' in health_result
            assert isinstance(health_result['health_score'], float)
            assert 0.0 <= health_result['health_score'] <= 1.0
            
            print(f"✅ HealthGuardAgent 測試通過，健康分數: {health_result['health_score']:.2f}")
            return True
            
        except Exception as e:
            print(f"❌ HealthGuardAgent 測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_market_intel_agent(self):
        """測試 MarketIntelAgent"""
        try:
            from src.agents.market_intel_agent import MarketIntelAgent
            
            intel_agent = MarketIntelAgent(TEST_CONFIG['market_intel'])
            
            # 檢查工具初始化
            assert 'damm_scanner' in intel_agent.tools or 'chain_scanner' in intel_agent.tools
            
            # 模擬池子掃描
            await intel_agent._scan_and_rank_pools()
            
            # 檢查結果
            rankings = intel_agent.get_pool_rankings()
            assert 'pools' in rankings
            assert 'total_count' in rankings
            assert 'scan_time' in rankings
            
            print(f"✅ MarketIntelAgent 測試通過，掃描到 {rankings['total_count']} 個池子")
            return True
            
        except Exception as e:
            print(f"❌ MarketIntelAgent 測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_damm_scanner_tool(self):
        """測試 DammScannerTool"""
        try:
            from src.tools.damm_scanner_tool import DammScannerTool
            
            scanner = DammScannerTool(TEST_CONFIG['market_intel']['damm_scanner'])
            
            # 測試池子掃描
            pools = await scanner.scan_damm_pools()
            
            # 驗證結果
            assert isinstance(pools, list)
            
            if pools:
                pool = pools[0]
                required_fields = ['id', 'tvl', 'fees_24h', 'fee_tvl_ratio']
                for field in required_fields:
                    assert field in pool, f"池子缺少必需字段: {field}"
            
            print(f"✅ DammScannerTool 測試通過，找到 {len(pools)} 個符合條件的池子")
            return True
            
        except Exception as e:
            print(f"❌ DammScannerTool 測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_fee_collector_tool(self):
        """測試 FeeCollectorTool"""
        try:
            from src.tools.fee_collector_tool import FeeCollectorTool
            
            collector = FeeCollectorTool(TEST_CONFIG['fee_collector'])
            
            # 測試手續費收割
            collection_result = await collector.collect_all_fees()
            
            # 驗證結果
            assert 'success' in collection_result
            assert 'positions_processed' in collection_result
            assert 'total_fees_collected' in collection_result
            assert 'collection_time' in collection_result
            
            # 檢查統計信息
            stats = collector.get_collection_stats()
            assert 'total_fees_collected' in stats
            assert 'schedule' in stats
            
            print(f"✅ FeeCollectorTool 測試通過，收割費用: ${collection_result['total_fees_collected']:.2f}")
            return True
            
        except Exception as e:
            print(f"❌ FeeCollectorTool 測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_strategy_mapping_matrix(self):
        """測試策略映射矩陣"""
        try:
            from src.agents.market_intel_agent import MarketIntelAgent
            
            intel_agent = MarketIntelAgent(TEST_CONFIG['market_intel'])
            
            # 測試不同條件的策略映射
            test_pools = [
                {
                    'tvl_usd': 50000000,  # $50M TVL
                    'volume_24h_usd': 1000000,  # $1M 交易量
                    'fees_24h_usd': 5000,  # $5K 費用
                    'volatility': 0.005  # 0.5% 波動率
                },
                {
                    'tvl_usd': 5000000,  # $5M TVL
                    'volume_24h_usd': 500000,  # $500K 交易量
                    'fees_24h_usd': 10000,  # $10K 費用
                    'volatility': 0.025  # 2.5% 波動率
                },
                {
                    'tvl_usd': 500000,  # $500K TVL
                    'volume_24h_usd': 100000,  # $100K 交易量
                    'fees_24h_usd': 2000,  # $2K 費用
                    'volatility': 0.05  # 5% 波動率
                }
            ]
            
            strategies = []
            for pool in test_pools:
                strategy = await intel_agent._determine_strategy_type(pool)
                strategies.append(strategy)
            
            # 驗證策略類型
            valid_strategies = ['SPOT_BALANCED', 'CURVE_BALANCED', 'BID_ASK_BALANCED', 'SPOT_IMBALANCED_DAMM']
            for strategy in strategies:
                assert strategy in valid_strategies, f"無效的策略類型: {strategy}"
            
            print(f"✅ 策略映射矩陣測試通過，策略分配: {strategies}")
            return True
            
        except Exception as e:
            print(f"❌ 策略映射矩陣測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_eight_phase_startup(self):
        """測試八階段啟動序列"""
        try:
            from src.core.dyflow_v33_supervisor import SupervisorAgent, Phase
            
            supervisor = SupervisorAgent(TEST_CONFIG)
            
            # 測試各階段的執行
            phases_tested = []
            
            # Phase 0: 初始化
            success = await supervisor._execute_phase(Phase.PHASE_0_INIT)
            assert success, "Phase 0 執行失敗"
            phases_tested.append("Phase 0: 初始化")
            
            # Phase 1: 健康檢查 (可能失敗，因為需要真實的網絡連接)
            try:
                success = await supervisor._execute_phase(Phase.PHASE_1_HEALTH)
                phases_tested.append(f"Phase 1: 健康檢查 ({'成功' if success else '失敗'})")
            except Exception as e:
                phases_tested.append(f"Phase 1: 健康檢查 (跳過 - {str(e)[:50]})")
            
            # Phase 2: UI 啟動
            success = await supervisor._execute_phase(Phase.PHASE_2_UI)
            assert success, "Phase 2 執行失敗"
            phases_tested.append("Phase 2: UI 啟動")
            
            # 檢查系統狀態
            status = supervisor.get_system_status()
            assert 'phase_status' in status
            assert 'agents_count' in status
            assert 'tools_count' in status
            
            print("✅ 八階段啟動序列測試通過")
            for phase in phases_tested:
                print(f"   {phase}")
            return True
            
        except Exception as e:
            print(f"❌ 八階段啟動序列測試失敗: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_workflow_yaml_validation(self):
        """測試 Workflow YAML 配置驗證"""
        try:
            import yaml
            
            # 讀取 workflow 配置
            with open('workflows/dyflow_v33_workflow.yaml', 'r', encoding='utf-8') as f:
                workflow_config = yaml.safe_load(f)
            
            # 驗證必需的配置項
            required_sections = ['channels', 'globals', 'agents', 'tools', 'strategy_mapping', 'risk_management']
            for section in required_sections:
                assert section in workflow_config, f"缺少配置節: {section}"
            
            # 驗證 Agent 配置
            agents = workflow_config['agents']
            required_agents = ['supervisor', 'health', 'intel', 'portfolio', 'strategy', 'exec', 'risk']
            agent_ids = [agent['id'] for agent in agents]
            for required_agent in required_agents:
                assert required_agent in agent_ids, f"缺少 Agent: {required_agent}"
            
            # 驗證 Tool 配置
            tools = workflow_config['tools']
            required_tools = ['damm_scanner', 'chain_scanner', 'vol_oracle', 'wallet_signer', 'dex_router', 'tx_sim', 'conn_probe', 'fee_collector']
            tool_ids = [tool['id'] for tool in tools]
            for required_tool in required_tools:
                assert required_tool in tool_ids, f"缺少 Tool: {required_tool}"
            
            # 驗證策略映射
            strategy_mapping = workflow_config['strategy_mapping']
            required_strategies = ['SPOT_BALANCED', 'CURVE_BALANCED', 'BID_ASK_BALANCED', 'SPOT_IMBALANCED_DAMM']
            for strategy in required_strategies:
                assert strategy in strategy_mapping, f"缺少策略: {strategy}"
            
            print("✅ Workflow YAML 配置驗證通過")
            print(f"   - {len(agents)} 個 Agent 配置")
            print(f"   - {len(tools)} 個 Tool 配置")
            print(f"   - {len(strategy_mapping)} 個策略映射")
            return True
            
        except Exception as e:
            print(f"❌ Workflow YAML 配置驗證失敗: {e}")
            return False

async def run_all_tests():
    """運行所有測試"""
    print("🚀 開始 DyFlow v3.3 系統測試")
    print("=" * 60)
    
    test_suite = TestDyFlowV33System()
    
    tests = [
        ("SupervisorAgent 初始化", test_suite.test_supervisor_agent_initialization),
        ("HealthGuardAgent", test_suite.test_health_guard_agent),
        ("MarketIntelAgent", test_suite.test_market_intel_agent),
        ("DammScannerTool", test_suite.test_damm_scanner_tool),
        ("FeeCollectorTool", test_suite.test_fee_collector_tool),
        ("策略映射矩陣", test_suite.test_strategy_mapping_matrix),
        ("八階段啟動序列", test_suite.test_eight_phase_startup),
        ("Workflow YAML 配置", test_suite.test_workflow_yaml_validation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 測試: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results.append((test_name, False))
    
    # 統計結果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print("\n" + "=" * 60)
    print("📊 測試結果統計")
    print(f"總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 保存測試結果
    test_results = {
        "test_summary": {
            "total_tests": total,
            "passed_tests": passed,
            "failed_tests": total - passed,
            "success_rate": f"{success_rate:.1f}%"
        },
        "test_results": [
            {
                "test_name": name,
                "status": "success" if result else "failed",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            for name, result in results
        ],
        "generated_at": datetime.now(timezone.utc).isoformat(),
        "dyflow_version": "v3.3",
        "architecture": "PRD_v3.3_compliant"
    }
    
    with open('dyflow_v33_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 測試結果已保存到: dyflow_v33_test_results.json")
    
    if success_rate >= 70:
        print("🎉 DyFlow v3.3 系統測試整體通過！")
    else:
        print("⚠️ DyFlow v3.3 系統測試需要改進")
    
    return success_rate >= 70

if __name__ == "__main__":
    asyncio.run(run_all_tests())
