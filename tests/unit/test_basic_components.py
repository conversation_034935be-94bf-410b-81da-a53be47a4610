#!/usr/bin/env python3
"""
DyFlow Basic Components Test
測試基本組件的功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')

def test_imports():
    """測試基本導入"""
    print("Testing basic imports...")
    
    try:
        # 測試工具導入
        from src.tools.prometheus_metrics_tool import PrometheusMetricsTool
        print("✅ PrometheusMetricsTool imported successfully")
        
        from src.tools.system_health_tool import SystemHealthTool
        print("✅ SystemHealthTool imported successfully")
        
        # 測試Agent導入
        from src.agents.pool_picker_agent import PoolPickerAgent
        print("✅ PoolPickerAgent imported successfully")
        
        from src.agents.exit_handler_agent import ExitHandlerAgent
        print("✅ ExitHandlerAgent imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

async def test_prometheus_metrics():
    """測試Prometheus指標工具"""
    print("\nTesting Prometheus Metrics Tool...")

    try:
        from src.tools.prometheus_metrics_tool import PrometheusMetricsTool

        config = {
            'metrics_port': 8001,
            'collection_interval': 30
        }

        tool = PrometheusMetricsTool(config)
        await tool.initialize()
        
        # 測試指標收集
        result = await tool.run("collect")
        
        if result['success']:
            print(f"✅ Prometheus metrics collected: {result['metrics_collected']} metrics")
            return True
        else:
            print(f"❌ Prometheus metrics failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Prometheus test failed: {e}")
        return False

async def test_system_health():
    """測試系統健康檢查工具"""
    print("\nTesting System Health Tool...")

    try:
        from src.tools.system_health_tool import SystemHealthTool

        config = {
            'thresholds': {
                'rpc_latency_ms': 5000,
                'api_latency_ms': 3000,
                'cpu_usage_percent': 80,
                'memory_usage_percent': 85
            }
        }

        tool = SystemHealthTool(config)
        
        # 測試系統資源檢查
        result = await tool.run("system")
        
        if result['success']:
            print(f"✅ System health check completed: {len(result['checks'])} checks")
            for check in result['checks']:
                status_icon = "✅" if check['status'] == 'healthy' else "⚠️" if check['status'] == 'warning' else "❌"
                print(f"   {status_icon} {check['name']}: {check['value']}{check['unit']} (threshold: {check['threshold']}{check['unit']})")
            return True
        else:
            print(f"❌ System health check failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ System health test failed: {e}")
        return False

async def test_pool_picker():
    """測試PoolPicker Agent"""
    print("\nTesting PoolPicker Agent...")

    try:
        from src.agents.pool_picker_agent import PoolPickerAgent

        config = {
            'min_tvl_usd': 20000,
            'max_fdv_usd': 1000000,
            'max_circulating_ratio': 0.05,
            'min_total_score': 60,
            'max_pools_per_chain': 5
        }

        agent = PoolPickerAgent(config)
        print("✅ PoolPicker Agent created")

        # 測試基本屬性
        assert agent.min_tvl_usd == 20000
        assert agent.max_fdv_usd == 1000000
        assert agent.max_circulating_ratio == 0.05
        print("✅ PoolPicker Agent configuration verified")

        # 測試初始化 (可能會因為缺少工具而失敗，但這是預期的)
        try:
            await agent.initialize()
            print("✅ PoolPicker Agent initialized")
        except Exception as e:
            print(f"⚠️ PoolPicker initialization failed (expected): {e}")

        # 清理
        try:
            await agent.cleanup()
            print("✅ PoolPicker Agent cleaned up")
        except:
            pass

        return True

    except Exception as e:
        print(f"❌ PoolPicker test failed: {e}")
        return False

def test_workflow_config():
    """測試工作流程配置"""
    print("\nTesting Workflow Configuration...")
    
    try:
        import yaml
        
        yaml_path = "workflows/low-float.yaml"
        if not os.path.exists(yaml_path):
            print(f"❌ Workflow config file not found: {yaml_path}")
            return False
        
        with open(yaml_path, 'r', encoding='utf-8') as f:
            workflow_config = yaml.safe_load(f)
        
        # 驗證基本結構
        required_fields = ['version', 'name', 'steps']
        for field in required_fields:
            if field not in workflow_config:
                print(f"❌ Missing required field: {field}")
                return False
        
        steps = workflow_config['steps']
        print(f"✅ Workflow config loaded: {len(steps)} steps")
        
        # 統計步驟類型
        agent_steps = [s for s in steps if 'agent' in s]
        tool_steps = [s for s in steps if 'uses' in s]
        
        print(f"   Agent steps: {len(agent_steps)}")
        print(f"   Tool steps: {len(tool_steps)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow config test failed: {e}")
        return False

def test_main_script():
    """測試主啟動腳本"""
    print("\nTesting Main Script...")
    
    try:
        main_script = "dyflow_main.py"
        if not os.path.exists(main_script):
            print(f"❌ Main script not found: {main_script}")
            return False
        
        with open(main_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵組件
        required_components = [
            'DyFlowSystem',
            'async def run_system',
            'def main()',
            'argparse'
        ]
        
        for component in required_components:
            if component not in content:
                print(f"❌ Missing component in main script: {component}")
                return False
        
        print(f"✅ Main script validated: {len(content)} characters")
        return True
        
    except Exception as e:
        print(f"❌ Main script test failed: {e}")
        return False

async def main():
    """主測試函數"""
    print("="*60)
    print("DyFlow Basic Components Test")
    print("="*60)
    
    start_time = datetime.now()
    test_results = []
    
    # 運行所有測試
    tests = [
        ("Imports", test_imports),
        ("Prometheus Metrics", test_prometheus_metrics),
        ("System Health", test_system_health),
        ("PoolPicker Agent", test_pool_picker),
        ("Workflow Config", test_workflow_config),
        ("Main Script", test_main_script)
    ]
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            test_results.append((test_name, False))
    
    # 計算結果
    total_tests = len(test_results)
    passed_tests = sum(1 for _, result in test_results if result)
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    test_duration = (datetime.now() - start_time).total_seconds()
    
    # 打印結果
    print("\n" + "="*60)
    print("Test Results Summary")
    print("="*60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    print(f"Duration: {test_duration:.2f}s")
    
    print("\nDetailed Results:")
    for test_name, result in test_results:
        status_icon = "✅" if result else "❌"
        print(f"{status_icon} {test_name}")
    
    print("="*60)
    
    # 返回適當的退出碼
    if failed_tests == 0:
        print("🎉 All tests passed!")
        return 0
    elif passed_tests > 0:
        print("⚠️ Some tests failed")
        return 1
    else:
        print("💥 All tests failed")
        return 2

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
        sys.exit(130)
    except Exception as e:
        print(f"測試運行失敗: {e}")
        sys.exit(1)
