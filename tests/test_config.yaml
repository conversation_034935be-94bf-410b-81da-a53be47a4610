# Dy-Flow v3 測試配置文件
# 用於真實數據整合測試

# 數據庫配置
database:
  supabase_url: "${SUPABASE_URL}"
  supabase_key: "${SUPABASE_KEY}"
  test_mode: false  # 使用真實數據庫

# 鏈配置
chains:
  bsc:
    rpc_url: "https://bsc-dataseed.binance.org/"
    explorer_api: "https://api.bscscan.com/api"
    explorer_key: "${BSCSCAN_API_KEY}"
    protocols:
      - pancakeswap_v3
    
  solana:
    rpc_url: "https://api.mainnet-beta.solana.com"
    protocols:
      - meteora

# API 配置
apis:
  pancakeswap:
    base_url: "https://api.pancakeswap.info/api/v2"
    subgraph_url: "https://api.thegraph.com/subgraphs/name/pancakeswap/exchange-v3-bsc"
    
  meteora:
    base_url: "https://dlmm-api.meteora.ag"
    
  price_feeds:
    coingecko: "https://api.coingecko.com/api/v3"
    binance: "https://api.binance.com/api/v3"

# 策略配置
strategies:
  active:
    max_pools: 5
    min_score: 0.70
    rebalance_frequency: "1h"
    
  hedge:
    max_pools: 3
    min_score: 0.60
    hedge_ratio: 0.8
    
  passive:
    max_pools: 2
    min_score: 0.50
    rebalance_frequency: "24h"

# 風險控制配置
risk:
  k_drop: 2.5
  min_drop_pct: 0.06
  atr_period: 14
  tvl_alert_threshold: 0.40
  max_position_size: 0.1
  stop_loss_pct: 0.15

# 測試配置
testing:
  # 數據收集限制
  max_pools_per_scout: 20
  test_timeout_seconds: 300
  
  # Scout 測試參數
  scout_bsc:
    min_tvl: 100000  # $100K 最小 TVL
    max_pools: 10
    
  scout_meteora:
    min_tvl: 100000  # $100K 最小 TVL
    max_pools: 10
  
  # Scorer 測試參數
  scorer:
    min_pools_required: 5
    expected_score_range: [0.0, 1.0]
    
  # Planner 測試參數
  planner:
    min_scored_pools: 5
    expected_strategies: ["active", "hedge", "passive"]
    
  # Risk Sentinel 測試參數
  risk_sentinel:
    price_history_hours: 24
    min_risk_events: 0  # 可能沒有風險事件

# 日誌配置
logging:
  level: "INFO"
  format: "json"
  output: "console"

# 重試配置
retry:
  max_attempts: 3
  delay_seconds: 5
  backoff_multiplier: 2

# 超時配置
timeouts:
  api_request: 30
  database_query: 60
  agent_execution: 300