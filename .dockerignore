# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
htmlcov

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
temp/

# Test files
tests/
test_*.py
*_test.py

# Documentation
docs/
*.md
README*

# Scripts
scripts/
*.sh
*.bat

# Temporary files
*.tmp
*.temp
temp/

# Data files
data/
*.csv
*.json
*.sqlite
*.db

# Environment files
.env*
!.env.example

# Build artifacts
build/
dist/
*.egg-info/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.backup
*.bak

# Cache
.cache/
.pytest_cache/