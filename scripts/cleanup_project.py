#!/usr/bin/env python3
"""
DyFlow 项目清理脚本
清理重复文件、整理项目结构
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Set
import json

class ProjectCleaner:
    """项目清理器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.backup_dir = self.project_root / "backup"
        self.cleanup_report = {
            "removed_files": [],
            "moved_files": [],
            "created_directories": [],
            "errors": []
        }
    
    def create_backup(self):
        """创建备份"""
        print("📦 创建项目备份...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir()
        
        # 备份重要文件
        important_files = [
            "dyflow_main.py",
            "start_dyflow_ui.py", 
            "start_web_ui.py",
            "start_agno_backend.py"
        ]
        
        backup_src = self.backup_dir / "original_files"
        backup_src.mkdir()
        
        for file_name in important_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                shutil.copy2(src_file, backup_src / file_name)
                print(f"  ✅ 备份: {file_name}")
        
        print(f"📦 备份完成，位置: {self.backup_dir}")
    
    def identify_duplicate_files(self) -> Dict[str, List[Path]]:
        """识别重复文件"""
        print("🔍 识别重复文件...")
        
        # 定义可能重复的文件模式
        duplicate_patterns = {
            "web_apps": [
                "web_ui/app.py",
                "web_ui/modern_app.py", 
                "web_ui/react_app.py",
                "web_ui/real_data_app.py",
                "web_ui/simple_app.py"
            ],
            "backends": [
                "dyflow_agno_backend.py",
                "dyflow_real_data_backend.py"
            ],
            "starters": [
                "start_dyflow_ui.py",
                "start_web_ui.py", 
                "start_agno_backend.py"
            ],
            "test_files": [
                "test_agent_communication.py",
                "test_agno_integration.py",
                "test_all_agents.py",
                "test_basic_components.py",
                "test_complete_system.py",
                "test_real_api_integration.py",
                "test_workflow_execution.py"
            ]
        }
        
        duplicates = {}
        for category, file_list in duplicate_patterns.items():
            existing_files = []
            for file_path in file_list:
                full_path = self.project_root / file_path
                if full_path.exists():
                    existing_files.append(full_path)
            
            if len(existing_files) > 1:
                duplicates[category] = existing_files
                print(f"  🔍 发现 {category} 重复文件: {len(existing_files)} 个")
        
        return duplicates
    
    def clean_duplicate_web_apps(self, duplicate_files: List[Path]):
        """清理重复的Web应用"""
        print("🧹 清理重复的Web应用...")
        
        # 保留 unified_app.py，移除其他
        keep_files = {"unified_app.py"}
        
        for file_path in duplicate_files:
            if file_path.name not in keep_files:
                try:
                    # 移动到备份目录而不是删除
                    backup_path = self.backup_dir / "removed_web_apps" 
                    backup_path.mkdir(exist_ok=True)
                    
                    shutil.move(str(file_path), str(backup_path / file_path.name))
                    self.cleanup_report["moved_files"].append(str(file_path))
                    print(f"  🗑️ 移除: {file_path.name}")
                    
                except Exception as e:
                    self.cleanup_report["errors"].append(f"移除 {file_path} 失败: {e}")
                    print(f"  ❌ 移除失败: {file_path.name} - {e}")
    
    def clean_duplicate_backends(self, duplicate_files: List[Path]):
        """清理重复的后端文件"""
        print("🧹 清理重复的后端文件...")
        
        # 保留功能最完整的后端
        keep_files = {"dyflow_real_data_backend.py"}
        
        for file_path in duplicate_files:
            if file_path.name not in keep_files:
                try:
                    backup_path = self.backup_dir / "removed_backends"
                    backup_path.mkdir(exist_ok=True)
                    
                    shutil.move(str(file_path), str(backup_path / file_path.name))
                    self.cleanup_report["moved_files"].append(str(file_path))
                    print(f"  🗑️ 移除: {file_path.name}")
                    
                except Exception as e:
                    self.cleanup_report["errors"].append(f"移除 {file_path} 失败: {e}")
                    print(f"  ❌ 移除失败: {file_path.name} - {e}")
    
    def clean_duplicate_starters(self, duplicate_files: List[Path]):
        """清理重复的启动文件"""
        print("🧹 清理重复的启动文件...")
        
        # 这些文件将被新的统一启动器替代
        for file_path in duplicate_files:
            try:
                backup_path = self.backup_dir / "removed_starters"
                backup_path.mkdir(exist_ok=True)
                
                shutil.move(str(file_path), str(backup_path / file_path.name))
                self.cleanup_report["moved_files"].append(str(file_path))
                print(f"  🗑️ 移除: {file_path.name}")
                
            except Exception as e:
                self.cleanup_report["errors"].append(f"移除 {file_path} 失败: {e}")
                print(f"  ❌ 移除失败: {file_path.name} - {e}")
    
    def organize_test_files(self, test_files: List[Path]):
        """整理测试文件"""
        print("📁 整理测试文件...")
        
        # 创建测试目录结构
        test_dirs = {
            "tests/unit": [],
            "tests/integration": [],
            "tests/system": []
        }
        
        # 分类测试文件
        file_categories = {
            "test_basic_components.py": "tests/unit",
            "test_agent_communication.py": "tests/unit", 
            "test_agno_integration.py": "tests/integration",
            "test_all_agents.py": "tests/integration",
            "test_trading_executor.py": "tests/integration",
            "test_real_api_integration.py": "tests/integration",
            "test_complete_system.py": "tests/system",
            "test_workflow_execution.py": "tests/system"
        }
        
        for file_path in test_files:
            target_dir = file_categories.get(file_path.name, "tests/misc")
            target_path = self.project_root / target_dir
            target_path.mkdir(parents=True, exist_ok=True)
            
            try:
                new_path = target_path / file_path.name
                if not new_path.exists():
                    shutil.move(str(file_path), str(new_path))
                    self.cleanup_report["moved_files"].append(f"{file_path} -> {new_path}")
                    print(f"  📁 移动: {file_path.name} -> {target_dir}")
                
            except Exception as e:
                self.cleanup_report["errors"].append(f"移动 {file_path} 失败: {e}")
                print(f"  ❌ 移动失败: {file_path.name} - {e}")
    
    def clean_cache_files(self):
        """清理缓存文件"""
        print("🧹 清理缓存文件...")
        
        cache_patterns = [
            "**/__pycache__",
            "**/*.pyc",
            "**/*.pyo", 
            "**/.pytest_cache",
            "**/node_modules",
            "**/.DS_Store"
        ]
        
        for pattern in cache_patterns:
            for path in self.project_root.glob(pattern):
                try:
                    if path.is_dir():
                        shutil.rmtree(path)
                    else:
                        path.unlink()
                    print(f"  🗑️ 清理: {path.relative_to(self.project_root)}")
                    
                except Exception as e:
                    print(f"  ⚠️ 清理失败: {path} - {e}")
    
    def create_directory_structure(self):
        """创建标准目录结构"""
        print("📁 创建标准目录结构...")
        
        directories = [
            "scripts",
            "docs/api",
            "docs/deployment", 
            "logs",
            "data/cache",
            "data/exports",
            "tests/unit",
            "tests/integration", 
            "tests/system",
            "config/environments"
        ]
        
        for dir_path in directories:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                full_path.mkdir(parents=True, exist_ok=True)
                self.cleanup_report["created_directories"].append(str(full_path))
                print(f"  📁 创建: {dir_path}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = self.project_root / "cleanup_report.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已生成: {report_path}")
        print(f"  - 移除/移动文件: {len(self.cleanup_report['moved_files'])}")
        print(f"  - 创建目录: {len(self.cleanup_report['created_directories'])}")
        print(f"  - 错误: {len(self.cleanup_report['errors'])}")
    
    def run_cleanup(self):
        """运行完整清理流程"""
        print("🧹 开始DyFlow项目清理")
        print("=" * 50)
        
        # 1. 创建备份
        self.create_backup()
        
        # 2. 识别重复文件
        duplicates = self.identify_duplicate_files()
        
        # 3. 清理重复文件
        if "web_apps" in duplicates:
            self.clean_duplicate_web_apps(duplicates["web_apps"])
        
        if "backends" in duplicates:
            self.clean_duplicate_backends(duplicates["backends"])
        
        if "starters" in duplicates:
            self.clean_duplicate_starters(duplicates["starters"])
        
        if "test_files" in duplicates:
            self.organize_test_files(duplicates["test_files"])
        
        # 4. 清理缓存文件
        self.clean_cache_files()
        
        # 5. 创建标准目录结构
        self.create_directory_structure()
        
        # 6. 生成报告
        self.generate_cleanup_report()
        
        print("\n✅ 项目清理完成!")
        print("\n📋 清理摘要:")
        print(f"  - 备份位置: {self.backup_dir}")
        print(f"  - 新的统一启动器: dyflow.py")
        print(f"  - 统一Web应用: web_ui/unified_app.py")
        print(f"  - 统一配置管理: config/unified_config.py")

def main():
    """主函数"""
    print("🧹 DyFlow 项目清理工具")
    print("=" * 50)
    
    response = input("是否继续清理项目? 这将移动/删除一些文件 (y/N): ")
    if response.lower() != 'y':
        print("取消清理")
        return
    
    cleaner = ProjectCleaner()
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
