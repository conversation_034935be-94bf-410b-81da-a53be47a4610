#!/usr/bin/env python3
"""
DyFlow v3.3 啟動腳本
整合 Agno Workflow + React UI + 事件驅動架構
"""

import asyncio
import sys
import os
import signal
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 導入 DyFlow 組件
try:
    from src.agents.execution_agent import ExecutionAgent, LPPlan, ExitRequest
    from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
    from src.agents.market_intel_agent import MarketIntelAgent
    from src.utils.helpers import get_utc_timestamp
except ImportError as e:
    print(f"❌ 導入 DyFlow 組件失敗: {e}")
    sys.exit(1)

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = structlog.get_logger(__name__)

class DyFlowV33Orchestrator:
    """DyFlow v3.3 協調器"""
    
    def __init__(self):
        self.name = "DyFlow v3.3 Orchestrator"
        self.running = False
        self.agents: Dict[str, Any] = {}
        self.event_bus: Dict[str, list] = {
            "bus.pool": [],
            "bus.lpplan": [],
            "bus.tx": [],
            "ExitRequest": [],
            "globalsDiff": [],
            "task_state_changed": [],
            "lp_update": [],
            "risk_update": [],
            "health_update": []
        }
        
        # 階段狀態
        self.phases = {
            0: {"name": "Supervisor", "status": "pending"},
            1: {"name": "HealthGuard", "status": "pending"},
            2: {"name": "WebUI", "status": "pending"},
            3: {"name": "WalletProbe", "status": "pending"},
            4: {"name": "MarketIntel", "status": "pending"},
            5: {"name": "PortfolioManager", "status": "pending"},
            6: {"name": "Strategy", "status": "pending"},
            7: {"name": "Execution", "status": "pending"},
            8: {"name": "RiskSentinel", "status": "pending"}
        }
        
        self.current_phase = 0
        
    async def initialize(self):
        """初始化系統"""
        logger.info("dyflow_v33_initializing")
        
        try:
            # 初始化 Agents
            await self._initialize_agents()
            
            # 設置信號處理
            self._setup_signal_handlers()
            
            logger.info("dyflow_v33_initialized_successfully")
            return True
            
        except Exception as e:
            logger.error("dyflow_v33_initialization_failed", error=str(e))
            return False
    
    async def _initialize_agents(self):
        """初始化所有 Agent"""
        
        # ExecutionAgent
        self.agents["execution"] = ExecutionAgent()
        logger.info("execution_agent_initialized")
        
        # RiskSentinelAgent
        risk_config = {
            "monitor_interval": 10,
            "il_fuse": -0.08,
            "var_threshold": 0.04,
            "enable_agno": True,
            "model_name": "qwen2.5:3b"
        }
        self.agents["risk_sentinel"] = RiskSentinelAgnoAgent(risk_config)
        logger.info("risk_sentinel_agent_initialized")
        
        # MarketIntelAgent (模擬)
        self.agents["market_intel"] = MarketIntelAgent({})
        logger.info("market_intel_agent_initialized")
    
    async def start_workflow(self):
        """啟動 8 階段工作流程"""
        logger.info("dyflow_v33_workflow_starting")
        self.running = True
        
        try:
            # 執行 8 階段啟動序列
            for phase in range(9):  # 0-8
                if not self.running:
                    break
                    
                success = await self._execute_phase(phase)
                if success:
                    self.phases[phase]["status"] = "completed"
                    self._publish_event("task_state_changed", {
                        "phase": phase,
                        "status": "completed",
                        "timestamp": get_utc_timestamp()
                    })
                else:
                    self.phases[phase]["status"] = "failed"
                    logger.error("phase_failed", phase=phase)
                    break
                    
                self.current_phase = phase + 1
            
            # 啟動業務循環
            if self.running and self.current_phase >= 8:
                await self._start_business_loop()
                
        except Exception as e:
            logger.error("workflow_execution_failed", error=str(e))
            self.running = False
    
    async def _execute_phase(self, phase: int) -> bool:
        """執行單個階段"""
        phase_name = self.phases[phase]["name"]
        logger.info("executing_phase", phase=phase, name=phase_name)
        
        self.phases[phase]["status"] = "running"
        
        # 模擬階段執行
        await asyncio.sleep(1)  # 模擬執行時間
        
        if phase == 0:  # Supervisor
            return await self._phase_supervisor()
        elif phase == 1:  # HealthGuard
            return await self._phase_health_guard()
        elif phase == 2:  # WebUI
            return await self._phase_webui()
        elif phase == 3:  # WalletProbe
            return await self._phase_wallet_probe()
        elif phase == 4:  # MarketIntel
            return await self._phase_market_intel()
        elif phase == 5:  # PortfolioManager
            return await self._phase_portfolio_manager()
        elif phase == 6:  # Strategy
            return await self._phase_strategy()
        elif phase == 7:  # Execution
            return await self._phase_execution()
        elif phase == 8:  # RiskSentinel
            return await self._phase_risk_sentinel()
        
        return False
    
    async def _phase_supervisor(self) -> bool:
        """階段 0: Supervisor"""
        logger.info("phase_supervisor_executing")
        # 模擬配置加載
        return True
    
    async def _phase_health_guard(self) -> bool:
        """階段 1: HealthGuard"""
        logger.info("phase_health_guard_executing")
        # 模擬健康檢查
        self._publish_event("health_update", {
            "rpcBsc": {"status": "connected", "latency": 85},
            "rpcSolana": {"status": "connected", "latency": 120},
            "subgraphPancake": {"status": "connected", "latency": 95},
            "apiMeteora": {"status": "connected", "latency": 110},
            "dbSupabase": {"status": "connected", "latency": 65}
        })
        return True
    
    async def _phase_webui(self) -> bool:
        """階段 2: WebUI"""
        logger.info("phase_webui_executing")
        return True
    
    async def _phase_wallet_probe(self) -> bool:
        """階段 3: WalletProbe"""
        logger.info("phase_wallet_probe_executing")
        return True
    
    async def _phase_market_intel(self) -> bool:
        """階段 4: MarketIntel"""
        logger.info("phase_market_intel_executing")
        # 開始推送池子事件
        await self._start_pool_scanning()
        return True
    
    async def _phase_portfolio_manager(self) -> bool:
        """階段 5: PortfolioManager"""
        logger.info("phase_portfolio_manager_executing")
        return True
    
    async def _phase_strategy(self) -> bool:
        """階段 6: Strategy"""
        logger.info("phase_strategy_executing")
        # 生成 LP 計劃
        await self._generate_lp_plan()
        return True
    
    async def _phase_execution(self) -> bool:
        """階段 7: Execution"""
        logger.info("phase_execution_executing")
        # 執行 LP 計劃
        await self._execute_lp_plan()
        return True
    
    async def _phase_risk_sentinel(self) -> bool:
        """階段 8: RiskSentinel"""
        logger.info("phase_risk_sentinel_executing")
        # 啟動風險監控
        await self._start_risk_monitoring()
        return True
    
    async def _start_pool_scanning(self):
        """開始池子掃描"""
        # 模擬池子事件
        pool_event = {
            "pool_id": "SOL/USDC",
            "chain": "sol",
            "tvl": 12000000,
            "fee_tvl_pct": 0.08,
            "sigma": 0.045,
            "spread": 0.0003
        }
        self._publish_event("bus.pool", pool_event)
        logger.info("pool_scanning_started")
    
    async def _generate_lp_plan(self):
        """生成 LP 計劃"""
        lp_plan = LPPlan(
            plan_id="plan_001",
            pool_id="SOL/USDC",
            strategy="SPOT_IMBALANCED_DAMM",
            k=0.038,
            notional_usd=2000,
            risk_profile={
                "il_cut": -0.08,
                "sigma_cut": 0.05,
                "var_cut": 0.05,
                "holding_window": 3600,
                "exit_asset": "SOL"
            }
        )
        
        self._publish_event("bus.lpplan", {
            "plan_id": lp_plan.plan_id,
            "pool_id": lp_plan.pool_id,
            "strategy": lp_plan.strategy,
            "k": lp_plan.k,
            "notional_usd": lp_plan.notional_usd,
            "risk_profile": lp_plan.risk_profile
        })
        logger.info("lp_plan_generated", plan_id=lp_plan.plan_id)
    
    async def _execute_lp_plan(self):
        """執行 LP 計劃"""
        # 模擬交易執行
        tx_event = {
            "position_id": "pos_001",
            "status": "open",
            "tx_hash": "jupiter_pos_001_1234567890",
            "timestamp": get_utc_timestamp()
        }
        self._publish_event("bus.tx", tx_event)
        logger.info("lp_plan_executed", position_id=tx_event["position_id"])
    
    async def _start_risk_monitoring(self):
        """啟動風險監控"""
        logger.info("risk_monitoring_started")
        # 風險監控將在業務循環中持續運行
    
    async def _start_business_loop(self):
        """啟動業務循環"""
        logger.info("business_loop_starting")
        
        while self.running:
            try:
                # 風險監控 (每 10 秒)
                await self._monitor_risks()
                
                # 等待下一個循環
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error("business_loop_error", error=str(e))
                await asyncio.sleep(5)
    
    async def _monitor_risks(self):
        """監控風險"""
        # 模擬風險檢查
        risk_update = {
            "ilNet": -1.9,
            "var95": 2.8,
            "maxDrawdown": -3.2,
            "riskScore": 75,
            "healthScore": 85
        }
        self._publish_event("risk_update", risk_update)
    
    def _publish_event(self, channel: str, data: Any):
        """發佈事件到事件總線"""
        if channel in self.event_bus:
            self.event_bus[channel].append({
                "data": data,
                "timestamp": get_utc_timestamp()
            })
            logger.debug("event_published", channel=channel, data=data)
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info("shutdown_signal_received", signal=signum)
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def shutdown(self):
        """關閉系統"""
        logger.info("dyflow_v33_shutting_down")
        self.running = False
        
        # 清理資源
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'cleanup'):
                    await agent.cleanup()
                logger.info("agent_cleaned_up", agent=agent_name)
            except Exception as e:
                logger.error("agent_cleanup_failed", agent=agent_name, error=str(e))
        
        logger.info("dyflow_v33_shutdown_completed")

async def main():
    """主函數"""
    print("🚀 DyFlow v3.3 啟動中...")
    
    orchestrator = DyFlowV33Orchestrator()
    
    try:
        # 初始化
        if not await orchestrator.initialize():
            print("❌ 初始化失敗")
            return 1
        
        print("✅ 初始化完成")
        print("📋 開始執行 8 階段工作流程...")
        
        # 啟動工作流程
        await orchestrator.start_workflow()
        
        print("🎉 DyFlow v3.3 運行中...")
        print("按 Ctrl+C 停止")
        
        # 保持運行
        while orchestrator.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號")
    except Exception as e:
        print(f"❌ 運行錯誤: {e}")
        return 1
    finally:
        await orchestrator.shutdown()
        print("👋 DyFlow v3.3 已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
