#!/usr/bin/env python3
"""
DyFlow 依赖安装脚本
自动安装和配置所有必需的依赖项
"""

import subprocess
import sys
import os
from pathlib import Path
import platform

class DependencyInstaller:
    """依赖安装器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.system = platform.system().lower()
        self.errors = []
        self.installed = []
    
    def print_banner(self):
        """打印横幅"""
        print("""
╔══════════════════════════════════════════════════════════════╗
║                   DyFlow 依赖安装器                           ║
║              自动安装和配置所有必需依赖                        ║
╚══════════════════════════════════════════════════════════════╝
        """)
    
    def check_python_version(self):
        """检查Python版本"""
        print("🐍 检查Python版本...")
        version = sys.version_info
        
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print(f"❌ Python版本过低: {version.major}.{version.minor}")
            print("   需要Python 3.8或更高版本")
            return False
        
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_python_packages(self):
        """安装Python包"""
        print("\n📦 安装Python依赖包...")
        
        # 基础依赖
        basic_packages = [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "websockets>=12.0",
            "aiohttp>=3.9.0",
            "structlog>=23.2.0",
            "pydantic>=2.5.0",
            "python-multipart>=0.0.6",
            "jinja2>=3.1.0",
            "pyyaml>=6.0"
        ]
        
        # Solana相关依赖
        solana_packages = [
            "solana>=0.30.0",
            "solders>=0.18.0",
            "anchorpy>=0.19.0"
        ]
        
        # 数据库和API依赖
        db_packages = [
            "supabase>=2.0.0",
            "asyncpg>=0.29.0",
            "httpx>=0.25.0"
        ]
        
        # Web3相关依赖
        web3_packages = [
            "web3>=6.12.0",
            "eth-account>=0.10.0"
        ]
        
        all_packages = basic_packages + solana_packages + db_packages + web3_packages
        
        for package in all_packages:
            try:
                print(f"  📥 安装 {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"  ✅ {package} 安装成功")
                    self.installed.append(package)
                else:
                    print(f"  ❌ {package} 安装失败: {result.stderr}")
                    self.errors.append(f"Python package {package}: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"  ⏰ {package} 安装超时")
                self.errors.append(f"Python package {package}: Installation timeout")
            except Exception as e:
                print(f"  ❌ {package} 安装异常: {e}")
                self.errors.append(f"Python package {package}: {e}")
    
    def install_ollama(self):
        """安装Ollama"""
        print("\n🤖 安装Ollama...")
        
        try:
            # 检查Ollama是否已安装
            result = subprocess.run(["ollama", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("  ✅ Ollama已安装")
                self.installed.append("ollama")
                return True
        except FileNotFoundError:
            pass
        
        try:
            if self.system == "darwin":  # macOS
                print("  📥 在macOS上安装Ollama...")
                # 使用curl安装
                install_cmd = [
                    "curl", "-fsSL", "https://ollama.ai/install.sh"
                ]
                result = subprocess.run(install_cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    # 执行安装脚本
                    subprocess.run(["sh"], input=result.stdout, text=True)
                    print("  ✅ Ollama安装完成")
                    self.installed.append("ollama")
                    return True
                else:
                    print(f"  ❌ Ollama安装失败: {result.stderr}")
                    self.errors.append(f"Ollama installation: {result.stderr}")
                    
            elif self.system == "linux":
                print("  📥 在Linux上安装Ollama...")
                install_cmd = [
                    "curl", "-fsSL", "https://ollama.ai/install.sh", "|", "sh"
                ]
                result = subprocess.run(" ".join(install_cmd), shell=True, 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("  ✅ Ollama安装完成")
                    self.installed.append("ollama")
                    return True
                else:
                    print(f"  ❌ Ollama安装失败: {result.stderr}")
                    self.errors.append(f"Ollama installation: {result.stderr}")
                    
            else:
                print(f"  ⚠️ 不支持的系统: {self.system}")
                print("  请手动安装Ollama: https://ollama.ai/")
                self.errors.append("Ollama: Unsupported system")
                
        except Exception as e:
            print(f"  ❌ Ollama安装异常: {e}")
            self.errors.append(f"Ollama installation: {e}")
        
        return False
    
    def download_ollama_models(self):
        """下载Ollama模型"""
        print("\n🧠 下载Ollama模型...")
        
        models = ["qwen2.5:3b", "llama3.2:3b"]
        
        for model in models:
            try:
                print(f"  📥 下载模型 {model}...")
                result = subprocess.run([
                    "ollama", "pull", model
                ], capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0:
                    print(f"  ✅ {model} 下载成功")
                    self.installed.append(f"ollama-model-{model}")
                else:
                    print(f"  ❌ {model} 下载失败: {result.stderr}")
                    self.errors.append(f"Ollama model {model}: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"  ⏰ {model} 下载超时")
                self.errors.append(f"Ollama model {model}: Download timeout")
            except FileNotFoundError:
                print("  ⚠️ Ollama未安装，跳过模型下载")
                break
            except Exception as e:
                print(f"  ❌ {model} 下载异常: {e}")
                self.errors.append(f"Ollama model {model}: {e}")
    
    def install_node_dependencies(self):
        """安装Node.js依赖"""
        print("\n📦 安装Node.js依赖...")
        
        react_ui_path = self.project_root / "react-ui"
        if not react_ui_path.exists():
            print("  ⚠️ React UI目录不存在，跳过Node.js依赖安装")
            return
        
        try:
            # 检查npm是否可用
            result = subprocess.run(["npm", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("  ❌ npm不可用，请先安装Node.js")
                self.errors.append("npm not available")
                return
            
            print(f"  ✅ npm版本: {result.stdout.strip()}")
            
            # 安装依赖
            print("  📥 安装React UI依赖...")
            result = subprocess.run([
                "npm", "install"
            ], cwd=react_ui_path, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("  ✅ React UI依赖安装成功")
                self.installed.append("react-ui-dependencies")
            else:
                print(f"  ❌ React UI依赖安装失败: {result.stderr}")
                self.errors.append(f"React UI dependencies: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("  ⏰ Node.js依赖安装超时")
            self.errors.append("Node.js dependencies: Installation timeout")
        except Exception as e:
            print(f"  ❌ Node.js依赖安装异常: {e}")
            self.errors.append(f"Node.js dependencies: {e}")
    
    def create_environment_file(self):
        """创建环境变量文件"""
        print("\n⚙️ 创建环境配置文件...")
        
        env_file = self.project_root / ".env.example"
        env_content = """# DyFlow 环境配置示例
# 复制此文件为 .env 并填入真实值

# Supabase配置
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# 区块链RPC配置
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# API密钥
BSCSCAN_API_KEY=your_bscscan_api_key_here
PANCAKESWAP_API_KEY=your_pancakeswap_api_key_here

# 日志配置
LOG_LEVEL=INFO
ENVIRONMENT=development

# AI模型配置
OLLAMA_MODEL=qwen2.5:3b
OPENAI_API_KEY=your_openai_api_key_here
"""
        
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            print(f"  ✅ 环境配置文件已创建: {env_file}")
            self.installed.append("environment-file")
        except Exception as e:
            print(f"  ❌ 环境配置文件创建失败: {e}")
            self.errors.append(f"Environment file: {e}")
    
    def run_verification_test(self):
        """运行验证测试"""
        print("\n🧪 运行验证测试...")
        
        try:
            result = subprocess.run([
                sys.executable, "dyflow.py", "test", "--type", "basic"
            ], cwd=self.project_root, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("  ✅ 基本功能测试通过")
                self.installed.append("verification-test")
            else:
                print(f"  ❌ 基本功能测试失败: {result.stderr}")
                self.errors.append(f"Verification test: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("  ⏰ 验证测试超时")
            self.errors.append("Verification test: Timeout")
        except Exception as e:
            print(f"  ❌ 验证测试异常: {e}")
            self.errors.append(f"Verification test: {e}")
    
    def generate_report(self):
        """生成安装报告"""
        print("\n" + "="*60)
        print("📊 安装报告")
        print("="*60)
        
        print(f"\n✅ 成功安装的组件 ({len(self.installed)}):")
        for item in self.installed:
            print(f"  - {item}")
        
        if self.errors:
            print(f"\n❌ 安装失败的组件 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  - {error}")
        
        success_rate = len(self.installed) / (len(self.installed) + len(self.errors)) * 100
        print(f"\n📈 安装成功率: {success_rate:.1f}%")
        
        if self.errors:
            print("\n🔧 解决建议:")
            print("  1. 检查网络连接")
            print("  2. 确保有足够的磁盘空间")
            print("  3. 检查系统权限")
            print("  4. 手动安装失败的组件")
        
        print("\n🚀 下一步:")
        print("  1. 复制 .env.example 为 .env 并配置")
        print("  2. 运行: python dyflow.py test --type trading")
        print("  3. 运行: python dyflow.py ui")
    
    def run_installation(self):
        """运行完整安装流程"""
        self.print_banner()
        
        if not self.check_python_version():
            return False
        
        self.install_python_packages()
        self.install_ollama()
        self.download_ollama_models()
        self.install_node_dependencies()
        self.create_environment_file()
        self.run_verification_test()
        self.generate_report()
        
        return len(self.errors) == 0

def main():
    """主函数"""
    installer = DependencyInstaller()
    success = installer.run_installation()
    
    if success:
        print("\n🎉 所有依赖安装成功！")
        return 0
    else:
        print("\n⚠️ 部分依赖安装失败，请查看上面的错误信息")
        return 1

if __name__ == "__main__":
    exit(main())
