#!/usr/bin/env python3
"""
DyFlow v3.3 WebSocket 服務器
將真實數據推送到前端 React UI
"""

import asyncio
import json
import websockets
import sys
import os
from pathlib import Path
from typing import Set, Dict, Any
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.real_data_service import real_data_service

# 配置日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

class DyFlowWebSocketServer:
    """DyFlow WebSocket 服務器"""
    
    def __init__(self):
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.running = False
        self.last_pool_data = {"bsc": [], "solana": []}
        
    async def register_client(self, websocket):
        """註冊新客戶端"""
        self.clients.add(websocket)
        logger.info("client_connected", client_count=len(self.clients))
        
        # 發送初始數據
        await self.send_initial_data(websocket)
        
    async def unregister_client(self, websocket):
        """註銷客戶端"""
        self.clients.discard(websocket)
        logger.info("client_disconnected", client_count=len(self.clients))
        
    async def send_initial_data(self, websocket):
        """發送初始數據給新連接的客戶端"""
        try:
            # 發送連接確認
            await websocket.send(json.dumps({
                "type": "connection_established",
                "data": {
                    "message": "Connected to DyFlow v3.3 Real Data Server",
                    "timestamp": asyncio.get_event_loop().time()
                }
            }))
            
            # 發送當前池子數據
            if self.last_pool_data["bsc"] or self.last_pool_data["solana"]:
                await websocket.send(json.dumps({
                    "type": "initial_pool_data",
                    "data": {
                        "bsc_pools": self.last_pool_data["bsc"][:10],  # 只發送前 10 個
                        "sol_pools": self.last_pool_data["solana"][:10],
                        "timestamp": asyncio.get_event_loop().time()
                    }
                }))
                
        except Exception as e:
            logger.error("send_initial_data_failed", error=str(e))
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """廣播消息給所有客戶端"""
        if not self.clients:
            return
            
        message_json = json.dumps(message)
        disconnected_clients = set()
        
        for client in self.clients:
            try:
                await client.send(message_json)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.error("broadcast_failed", error=str(e))
                disconnected_clients.add(client)
        
        # 清理斷開的連接
        for client in disconnected_clients:
            self.clients.discard(client)
    
    async def handle_client(self, websocket, path):
        """處理客戶端連接"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error("invalid_json_received", message=message)
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)
    
    async def handle_client_message(self, websocket, data: Dict[str, Any]):
        """處理客戶端消息"""
        message_type = data.get("type")
        
        if message_type == "ping":
            await websocket.send(json.dumps({
                "type": "pong",
                "data": {"timestamp": asyncio.get_event_loop().time()}
            }))
        elif message_type == "request_pool_data":
            await self.send_pool_data_update()
        else:
            logger.warning("unknown_message_type", type=message_type)
    
    async def start_data_update_loop(self):
        """啟動數據更新循環"""
        logger.info("data_update_loop_starting")
        
        while self.running:
            try:
                # 獲取最新的池子數據
                all_pools = await real_data_service.get_all_pools()
                
                # 更新緩存
                self.last_pool_data = all_pools
                
                # 發送池子數據更新
                await self.send_pool_data_update()
                
                # 發送系統狀態更新
                await self.send_system_status_update(all_pools)
                
                # 等待 30 秒
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error("data_update_loop_error", error=str(e))
                await asyncio.sleep(10)
    
    async def send_pool_data_update(self):
        """發送池子數據更新"""
        bsc_pools = self.last_pool_data.get("bsc", [])
        sol_pools = self.last_pool_data.get("solana", [])
        
        # 轉換為前端格式
        formatted_bsc_pools = []
        for pool in bsc_pools[:20]:  # 只發送前 20 個
            formatted_pool = {
                "id": pool.get("pool_id", ""),
                "chain": "bsc",
                "pair": pool.get("pair", ""),
                "tvl_usd": pool.get("tvl_usd", 0),
                "volume_24h": pool.get("volume_24h", 0),
                "fees_24h": pool.get("fees_24h", 0),
                "fee_apr": pool.get("fee_apr", 0),
                "total_apr": pool.get("total_apr", 0),
                "risk_level": pool.get("risk_level", "medium"),
                "strategy_type": pool.get("strategy_type", "SPOT_BALANCED"),
                "last_update": pool.get("last_update", "")
            }
            formatted_bsc_pools.append(formatted_pool)
        
        formatted_sol_pools = []
        for pool in sol_pools[:20]:  # 只發送前 20 個
            formatted_pool = {
                "id": pool.get("pool_id", ""),
                "chain": "solana", 
                "pair": pool.get("pair", ""),
                "tvl_usd": pool.get("tvl_usd", 0),
                "volume_24h": pool.get("volume_24h", 0),
                "fees_24h": pool.get("fees_24h", 0),
                "fee_apr": pool.get("fee_apr", 0),
                "total_apr": pool.get("total_apr", 0),
                "risk_level": pool.get("risk_level", "medium"),
                "strategy_type": pool.get("strategy_type", "SPOT_IMBALANCED_DAMM"),
                "last_update": pool.get("last_update", "")
            }
            formatted_sol_pools.append(formatted_pool)
        
        # 廣播池子數據
        await self.broadcast_to_all({
            "type": "pool_data_update",
            "data": {
                "bsc_pools": formatted_bsc_pools,
                "sol_pools": formatted_sol_pools,
                "bsc_count": len(bsc_pools),
                "sol_count": len(sol_pools),
                "timestamp": asyncio.get_event_loop().time()
            }
        })
        
        logger.info("pool_data_broadcasted", 
                   bsc_pools=len(formatted_bsc_pools),
                   sol_pools=len(formatted_sol_pools),
                   clients=len(self.clients))
    
    async def send_system_status_update(self, all_pools: Dict[str, Any]):
        """發送系統狀態更新"""
        bsc_pools = all_pools.get("bsc", [])
        sol_pools = all_pools.get("solana", [])
        
        # 計算統計數據
        total_bsc_tvl = sum(pool.get("tvl_usd", 0) for pool in bsc_pools)
        total_sol_tvl = sum(pool.get("tvl_usd", 0) for pool in sol_pools)
        avg_bsc_apr = sum(pool.get("fee_apr", 0) for pool in bsc_pools) / len(bsc_pools) if bsc_pools else 0
        avg_sol_apr = sum(pool.get("fee_apr", 0) for pool in sol_pools) / len(sol_pools) if sol_pools else 0
        
        await self.broadcast_to_all({
            "type": "system_status_update",
            "data": {
                "bsc_pools_count": len(bsc_pools),
                "sol_pools_count": len(sol_pools),
                "total_pools": len(bsc_pools) + len(sol_pools),
                "total_bsc_tvl": total_bsc_tvl,
                "total_sol_tvl": total_sol_tvl,
                "avg_bsc_apr": avg_bsc_apr,
                "avg_sol_apr": avg_sol_apr,
                "data_source": "real_apis",
                "timestamp": asyncio.get_event_loop().time()
            }
        })
    
    async def start_server(self, host="localhost", port=8001):
        """啟動 WebSocket 服務器"""
        self.running = True
        
        # 啟動數據更新循環
        data_task = asyncio.create_task(self.start_data_update_loop())
        
        # 啟動 WebSocket 服務器
        logger.info("websocket_server_starting", host=host, port=port)
        
        async with websockets.serve(self.handle_client, host, port):
            logger.info("websocket_server_started", host=host, port=port)
            print(f"🌐 WebSocket 服務器已啟動: ws://{host}:{port}")
            print("📊 正在推送真實的 PancakeSwap V3 數據到前端...")
            
            # 保持服務器運行
            await data_task

async def main():
    """主函數"""
    print("🚀 DyFlow v3.3 WebSocket 服務器啟動中...")
    
    server = DyFlowWebSocketServer()
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號")
        server.running = False
    except Exception as e:
        print(f"❌ 服務器錯誤: {e}")
        return 1
    
    print("👋 WebSocket 服務器已停止")
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
