#!/usr/bin/env python3
"""
DyFlow v3.3 完整 Workflow 測試
測試 AI Agent 驅動的完整工作流程
"""

import asyncio
import subprocess
import time
import requests
import json
from datetime import datetime
import sys
import os

class DyFlowWorkflowTester:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.base_url = "http://localhost:8001"
        self.frontend_url = "http://localhost:3000"
        
    async def start_services(self):
        """啟動前後端服務"""
        print("🚀 啟動 DyFlow v3.3 服務...")
        
        # 啟動後端
        print("📡 啟動後端服務...")
        self.backend_process = subprocess.Popen([
            sys.executable, "dyflow_real_data_backend.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待後端啟動
        for i in range(30):
            try:
                response = requests.get(f"{self.base_url}/api/real-data", timeout=2)
                if response.status_code == 200:
                    print("✅ 後端服務已啟動")
                    break
            except:
                time.sleep(1)
                if i % 5 == 0:
                    print(f"⏳ 等待後端啟動... ({i+1}/30)")
        else:
            print("❌ 後端啟動超時")
            return False
        
        # 啟動前端
        print("🌐 啟動前端服務...")
        os.chdir("react-ui")
        self.frontend_process = subprocess.Popen([
            "npm", "run", "dev"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待前端啟動
        for i in range(60):
            try:
                response = requests.get(self.frontend_url, timeout=2)
                if response.status_code == 200:
                    print("✅ 前端服務已啟動")
                    os.chdir("..")
                    break
            except:
                time.sleep(1)
                if i % 10 == 0:
                    print(f"⏳ 等待前端啟動... ({i+1}/60)")
        else:
            print("❌ 前端啟動超時")
            os.chdir("..")
            return False
        
        return True
    
    async def test_workflow_apis(self):
        """測試 Workflow API"""
        print("\n🧪 測試 Workflow API...")
        
        # 測試階段狀態 API
        try:
            response = requests.get(f"{self.base_url}/api/phases/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 階段狀態 API: 當前 Phase {data.get('current_phase', 0)}")
            else:
                print(f"❌ 階段狀態 API 錯誤: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 階段狀態 API 異常: {e}")
            return False
        
        # 測試重置 API
        try:
            response = requests.post(f"{self.base_url}/api/workflow/reset", timeout=10)
            if response.status_code == 200:
                print("✅ Workflow 重置 API 正常")
            else:
                print(f"❌ Workflow 重置 API 錯誤: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Workflow 重置 API 異常: {e}")
            return False
        
        return True
    
    async def execute_complete_workflow(self):
        """執行完整的 Workflow"""
        print("\n🔄 執行完整 Workflow...")
        
        # 啟動 Workflow
        try:
            response = requests.post(f"{self.base_url}/api/workflow/start", timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Workflow 啟動成功: {result.get('message', '')}")
            else:
                print(f"❌ Workflow 啟動失敗: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Workflow 啟動異常: {e}")
            return False
        
        # 監控 Workflow 進度
        print("\n📊 監控 Workflow 進度...")
        start_time = time.time()
        max_wait_time = 120  # 最多等待 2 分鐘
        
        while time.time() - start_time < max_wait_time:
            try:
                response = requests.get(f"{self.base_url}/api/phases/status", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    
                    current_phase = data.get('current_phase', 0)
                    completed_phases = data.get('completed_phases', 0)
                    total_phases = data.get('total_phases', 9)
                    progress = data.get('progress_percentage', 0)
                    is_running = data.get('is_running', False)
                    
                    print(f"📈 Phase {current_phase}: {completed_phases}/{total_phases} 完成 ({progress:.1f}%)")
                    
                    # 顯示 Agent 狀態
                    agents = data.get('agents', {})
                    if agents:
                        agent_count = len(agents)
                        active_agents = sum(1 for agent in agents.values() if agent.get('status') == 'active')
                        print(f"🤖 Agent 狀態: {active_agents}/{agent_count} 活躍")
                    
                    # 檢查是否完成
                    if not is_running and completed_phases == total_phases:
                        print("🎉 Workflow 執行完成！")
                        return True
                    
                    # 檢查是否失敗
                    if not is_running and completed_phases < total_phases:
                        # 檢查是否有失敗的階段
                        phase_status = data.get('phase_status', {})
                        failed_phases = [p for p, status in phase_status.items() 
                                       if status.get('status') == 'failed']
                        if failed_phases:
                            print(f"❌ Workflow 失敗，失敗階段: {failed_phases}")
                            return False
                
                await asyncio.sleep(3)  # 每 3 秒檢查一次
                
            except Exception as e:
                print(f"⚠️ 監控異常: {e}")
                await asyncio.sleep(5)
        
        print("⏰ Workflow 執行超時")
        return False
    
    async def verify_workflow_results(self):
        """驗證 Workflow 執行結果"""
        print("\n🔍 驗證 Workflow 執行結果...")
        
        # 檢查最終狀態
        try:
            response = requests.get(f"{self.base_url}/api/phases/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                completed_phases = data.get('completed_phases', 0)
                agents = data.get('agents', {})
                
                print(f"✅ 最終狀態: {completed_phases}/9 階段完成")
                print(f"✅ Agent 數量: {len(agents)}")
                
                # 檢查關鍵 Agent
                expected_agents = ['supervisor', 'market_intel', 'portfolio', 'strategy', 'execution', 'risk']
                found_agents = []
                
                for agent_key in expected_agents:
                    if agent_key in agents:
                        agent = agents[agent_key]
                        print(f"✅ {agent.get('name', agent_key)}: {agent.get('status', 'unknown')}")
                        found_agents.append(agent_key)
                    else:
                        print(f"❌ 缺少 Agent: {agent_key}")
                
                # 檢查策略生成結果
                if 'strategy' in agents and 'strategies' in agents['strategy']:
                    strategies = agents['strategy']['strategies']
                    print(f"✅ 生成策略數量: {len(strategies)}")
                    for i, strategy in enumerate(strategies):
                        print(f"   策略 {i+1}: {strategy.get('type')} - {strategy.get('pool')} (APR: {strategy.get('target_apr', 0):.1f}%)")
                
                # 檢查交易執行結果
                if 'execution' in agents and 'transactions' in agents['execution']:
                    transactions = agents['execution']['transactions']
                    print(f"✅ 執行交易數量: {len(transactions)}")
                    for i, tx in enumerate(transactions):
                        print(f"   交易 {i+1}: {tx.get('pool')} - {tx.get('status')} (Hash: {tx.get('tx_hash', '')[:10]}...)")
                
                # 檢查投資組合狀態
                if 'portfolio' in agents:
                    portfolio = agents['portfolio']
                    nav = portfolio.get('nav', 0)
                    positions = portfolio.get('positions', [])
                    print(f"✅ 投資組合 NAV: ${nav:,.2f}")
                    print(f"✅ 持倉數量: {len(positions)}")
                
                return len(found_agents) >= 4  # 至少要有 4 個 Agent
                
        except Exception as e:
            print(f"❌ 結果驗證異常: {e}")
            return False
    
    async def cleanup(self):
        """清理資源"""
        print("\n🧹 清理資源...")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ 前端服務已停止")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ 後端服務已停止")
    
    async def run_complete_test(self):
        """運行完整測試"""
        print("🚀 開始 DyFlow v3.3 完整 Workflow 測試")
        print("=" * 60)
        print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            # 1. 啟動服務
            if not await self.start_services():
                return False
            
            # 2. 測試 API
            if not await self.test_workflow_apis():
                return False
            
            # 3. 執行完整 Workflow
            if not await self.execute_complete_workflow():
                return False
            
            # 4. 驗證結果
            if not await self.verify_workflow_results():
                return False
            
            print("\n" + "=" * 60)
            print("🎉 DyFlow v3.3 完整 Workflow 測試成功！")
            print()
            print("📋 測試結果:")
            print("✅ 服務啟動正常")
            print("✅ API 功能正常")
            print("✅ Workflow 執行成功")
            print("✅ AI Agent 正常工作")
            print("✅ 策略生成成功")
            print("✅ 交易執行成功")
            print("✅ 投資組合管理正常")
            print()
            print("🌐 訪問地址:")
            print(f"   前端: {self.frontend_url}")
            print(f"   後端: {self.base_url}")
            print()
            print("⏳ 服務將保持運行，按 Ctrl+C 停止...")
            
            # 保持服務運行
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 用戶中斷，正在停止服務...")
            
            return True
            
        except Exception as e:
            print(f"❌ 測試過程中發生異常: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    tester = DyFlowWorkflowTester()
    success = await tester.run_complete_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
