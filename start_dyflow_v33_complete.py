#!/usr/bin/env python3
"""
DyFlow v3.3 完整系統啟動器
啟動 Agno Workflow API + React UI 的完整 LP 策略系統
"""

import asyncio
import subprocess
import sys
import time
import signal
import os
from pathlib import Path
import structlog

logger = structlog.get_logger(__name__)

class DyFlowV33Launcher:
    """DyFlow v3.3 完整系統啟動器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = {}
        self.running = False
        
    def print_banner(self):
        """打印啟動橫幅"""
        print("""
╔══════════════════════════════════════════════════════════════╗
║                     DyFlow v3.3 Complete                    ║
║              24/7 自動化多 Agent LP 策略系統                  ║
║                                                              ║
║  🤖 7 個專業 Agents (Agno Framework)                        ║
║  🔄 8-phase 啟動序列                                         ║
║  🌐 React UI + WebSocket 實時通訊                            ║
║  ⚡ BSC (PancakeSwap v3) + Solana (Meteora DLMM v2)         ║
║  🛡️ 動態風控 + 自動收割                                      ║
╚══════════════════════════════════════════════════════════════╝
        """)
        
    def check_dependencies(self):
        """檢查依賴"""
        print("🔍 檢查系統依賴...")
        
        # 檢查 Python 包
        required_packages = [
            "fastapi", "uvicorn", "websockets", "structlog", 
            "pydantic", "aiohttp"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                print(f"  ❌ {package} (缺失)")
                missing_packages.append(package)
        
        # 檢查 Ollama
        try:
            result = subprocess.run(["ollama", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("  ✅ Ollama")
            else:
                print("  ⚠️ Ollama (未運行)")
        except:
            print("  ❌ Ollama (未安裝)")
        
        # 檢查 React UI
        react_dist = self.project_root / "react-ui" / "dist"
        if react_dist.exists():
            print("  ✅ React UI (已構建)")
        else:
            print("  ⚠️ React UI (需要構建)")
        
        if missing_packages:
            print(f"\n❌ 缺失依賴: {', '.join(missing_packages)}")
            print("請運行: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ 依賴檢查完成\n")
        return True
    
    def start_agno_api(self):
        """啟動 Agno Workflow API"""
        print("🚀 啟動 Agno Workflow API (Port 8001)...")
        
        api_script = self.project_root / "web_ui" / "agno_workflow_api.py"
        
        if not api_script.exists():
            print(f"❌ API 腳本不存在: {api_script}")
            return False
        
        try:
            process = subprocess.Popen(
                [sys.executable, str(api_script)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes["agno_api"] = process
            print("✅ Agno Workflow API 已啟動")
            
            # 等待 API 啟動
            time.sleep(3)
            return True
            
        except Exception as e:
            print(f"❌ 啟動 Agno API 失敗: {e}")
            return False
    
    def start_react_ui(self):
        """啟動 React UI"""
        print("🌐 啟動 React UI...")
        
        react_dir = self.project_root / "react-ui"
        
        if not react_dir.exists():
            print("❌ React UI 目錄不存在")
            return False
        
        # 檢查是否已構建
        dist_dir = react_dir / "dist"
        if not dist_dir.exists():
            print("📦 構建 React UI...")
            try:
                # 安裝依賴
                subprocess.run(["npm", "install"], 
                             cwd=str(react_dir), 
                             check=True)
                
                # 構建
                subprocess.run(["npm", "run", "build"], 
                             cwd=str(react_dir), 
                             check=True)
                
                print("✅ React UI 構建完成")
                
            except subprocess.CalledProcessError as e:
                print(f"❌ React UI 構建失敗: {e}")
                return False
        
        # 啟動開發服務器
        try:
            process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=str(react_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes["react_ui"] = process
            print("✅ React UI 已啟動 (Port 3000)")
            
            return True
            
        except Exception as e:
            print(f"❌ 啟動 React UI 失敗: {e}")
            return False
    
    def check_services(self):
        """檢查服務狀態"""
        print("\n📊 檢查服務狀態...")
        
        import requests
        
        # 檢查 Agno API
        try:
            response = requests.get("http://localhost:8001/api/system/status", timeout=5)
            if response.status_code == 200:
                print("  ✅ Agno Workflow API (http://localhost:8001)")
            else:
                print(f"  ⚠️ Agno API 響應異常: {response.status_code}")
        except:
            print("  ❌ Agno Workflow API 無法連接")
        
        # 檢查 React UI
        try:
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print("  ✅ React UI (http://localhost:3000)")
            else:
                print(f"  ⚠️ React UI 響應異常: {response.status_code}")
        except:
            print("  ❌ React UI 無法連接")
    
    def show_usage_info(self):
        """顯示使用說明"""
        print("""
🎯 DyFlow v3.3 使用說明:

1. 打開瀏覽器訪問: http://localhost:3000
2. 查看系統健康狀態和 Agent 狀態
3. 點擊「啟動 DyFlow Agent」開始自動化 LP 策略
4. 監控實時池子掃描和策略執行
5. 查看投資組合和風險指標

📡 API 端點:
- 系統狀態: http://localhost:8001/api/system/status
- Agent 狀態: http://localhost:8001/api/agents
- 池子數據: http://localhost:8001/api/pools
- WebSocket: ws://localhost:8001/ws

🔧 控制命令:
- 啟動工作流程: POST /api/workflow/start
- 停止工作流程: POST /api/workflow/stop
- 設置交易模式: POST /api/trading_mode {"mode": "active"}

按 Ctrl+C 停止所有服務
        """)
    
    def cleanup(self):
        """清理進程"""
        print("\n🛑 正在停止所有服務...")
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"  停止 {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        print("✅ 所有服務已停止")
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        print(f"\n收到信號 {signum}")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    async def run(self):
        """運行完整系統"""
        self.running = True
        
        # 設置信號處理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.print_banner()
        
        # 檢查依賴
        if not self.check_dependencies():
            return 1
        
        # 啟動服務
        if not self.start_agno_api():
            return 1
        
        if not self.start_react_ui():
            self.cleanup()
            return 1
        
        # 等待服務啟動
        print("⏳ 等待服務啟動...")
        await asyncio.sleep(5)
        
        # 檢查服務狀態
        self.check_services()
        
        # 顯示使用說明
        self.show_usage_info()
        
        # 保持運行
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.cleanup()
        
        return 0

def main():
    """主函數"""
    launcher = DyFlowV33Launcher()
    return asyncio.run(launcher.run())

if __name__ == "__main__":
    exit(main())
