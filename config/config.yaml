# DyFlow v3 + Agno Framework Configuration
# Low-Float Meme Coin LP Strategy Configuration

# ========== General Configuration ==========
app:
  name: "DyFlow Low-Float LP Strategy"
  version: "3.1"
  environment: "development"  # development, staging, production
  debug: true
  log_level: "INFO"

# ========== Database Configuration ==========
database:
  type: "supabase"
  url: "${SUPABASE_URL}"
  key: "${SUPABASE_ANON_KEY}"
  service_role_key: "${SUPABASE_SERVICE_ROLE_KEY}"
  
  # Connection settings
  max_connections: 10
  connection_timeout: 30
  retry_attempts: 3
  
  # Tables
  tables:
    positions: "positions"
    hedges: "hedges"
    fees: "fees"
    alerts: "alerts"
    pool_snapshots: "pool_snapshots"
    agent_results: "agent_results"

# ========== Blockchain Configuration ==========
chains:
  bsc:
    name: "Binance Smart Chain"
    chain_id: 56
    rpc_url: "${BSC_RPC_URL}"
    backup_rpc_urls:
      - "https://bsc-dataseed1.binance.org/"
      - "https://bsc-dataseed2.binance.org/"
    
    # Contract addresses
    contracts:
      pancake_v3_factory: "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865"
      pancake_v3_router: "0x13f4EA83D0bd40E75C8222255bc855a974568Dd4"
      pancake_v3_quoter: "0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997"
      wbnb: "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c"
      usdt: "0x55d398326f99059fF775485246999027B3197955"
    
    # Gas settings
    gas:
      max_gas_price_gwei: 20
      gas_limit_multiplier: 1.2
      priority_fee_gwei: 1
  
  solana:
    name: "Solana"
    cluster: "mainnet-beta"
    rpc_url: "${SOLANA_RPC_URL}"
    backup_rpc_urls:
      - "https://api.mainnet-beta.solana.com"
      - "https://solana-api.projectserum.com"
    
    # Program IDs
    programs:
      meteora_dlmm: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"
      jupiter_v6: "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"
      
    # Transaction settings
    transaction:
      max_retries: 3
      confirmation_timeout: 60
      commitment: "confirmed"

# ========== Tools Configuration ==========
tools:
  meteora_dlmm:
    api_base: "https://dlmm-api.meteora.ag"
    rpc_url: "${SOLANA_RPC_URL}"
    program_id: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"
    
  pancake_subgraph:
    subgraph_url: "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
    api_key: "${GRAPH_API_KEY}"
    rpc_url: "${BSC_RPC_URL}"
    
  jupiter_swap:
    api_base: "https://quote-api.jup.ag/v6"
    rpc_url: "${SOLANA_RPC_URL}"
    default_slippage_bps: 50
    max_accounts: 64
    
  oneinch_swap:
    api_base: "https://api.1inch.dev"
    api_key: "${ONEINCH_API_KEY}"
    rpc_url: "${BSC_RPC_URL}"
    chain_id: 56
    default_slippage: 1.0
    
  supabase_db:
    url: "${SUPABASE_URL}"
    key: "${SUPABASE_ANON_KEY}"
    service_role_key: "${SUPABASE_SERVICE_ROLE_KEY}"
    realtime_enabled: true

# ========== Agents Configuration ==========
agents:
  planner_agno:
    agno:
      primary_model: "gpt-4o"
      secondary_model: "claude-3-7-sonnet-latest"
      use_reasoning: true
      enable_memory: true
      debug_mode: false
    
    strategy:
      max_positions: 10
      position_size_usd: 5000
      rebalance_threshold: 0.15
      
  risk_sentinel_agno:
    agno:
      primary_model: "gpt-4o"
      secondary_model: "claude-3-7-sonnet-latest"
      use_reasoning: true
      enable_memory: true
      enable_market_data: true
      debug_mode: false
    
    risk_thresholds:
      il_fuse_threshold: -8.0
      var_fuse_threshold: 4.0
      max_drawdown: -15.0
      
  range_rebalancer_agent:
    agno:
      primary_model: "gpt-4o"
      use_reasoning: true
      debug_mode: false
    
    rebalance:
      efficiency_threshold: 0.7
      price_deviation_threshold: 0.15
      min_interval_hours: 4
      max_gas_cost_ratio: 0.02
      
  hedge_agent:
    agno:
      primary_model: "gpt-4o"
      use_reasoning: true
      debug_mode: false
    
    hedge:
      dca_trigger_gain_pct: 20.0
      dca_step_size_pct: 10.0
      max_dca_orders: 10
      min_dca_interval_minutes: 30
      hedge_threshold_pct: 50.0
      stop_loss_pct: -8.0
      
  portfolio_agent:
    agno:
      primary_model: "gpt-4o"
      use_reasoning: true
      debug_mode: false
    
    portfolio:
      target_allocations:
        delta_neutral: 0.4
        ladder_ss: 0.3
        passive_high_tvl: 0.2
        cash: 0.1
      rebalance_threshold: 0.05
      max_position_size: 0.15
      
  scorer_v2_agno:
    agno:
      primary_model: "gpt-4o"
      use_reasoning: true
      debug_mode: false
    
    scoring:
      min_tvl_usd: 20000
      max_fdv_usd: 1000000
      max_circulating_ratio: 0.05
      min_sentiment_score: 0.6

# ========== Strategy Configuration ==========
strategy:
  name: "low_float_meme_coin_lp"
  
  # Discovery filters
  discovery:
    min_tvl_usd: 20000
    max_fdv_usd: 1000000
    max_circulating_ratio: 0.05
    min_volume_24h_usd: 50000
    
  # LP parameters
  lp:
    tick_range_percentage: 12.5
    single_sided_enabled: true
    auto_harvest_threshold: 0.005  # 0.5% NAV
    harvest_interval_hours: 4
    
  # DCA exit parameters
  dca:
    trigger_gain_percentage: 20.0
    step_size_percentage: 10.0
    max_orders: 10
    interval_minutes: 30
    
  # Risk management
  risk:
    il_fuse_threshold: -8.0
    var_fuse_threshold: 4.0
    max_position_size_percentage: 15.0
    stop_loss_percentage: -8.0
    max_slippage_bps: 100

# ========== Monitoring Configuration ==========
monitoring:
  prometheus:
    enabled: true
    port: 8000
    metrics_path: "/metrics"
    
  grafana:
    enabled: true
    dashboard_url: "${GRAFANA_DASHBOARD_URL}"
    
  alerting:
    telegram:
      enabled: true
      bot_token: "${TELEGRAM_BOT_TOKEN}"
      chat_id: "${TELEGRAM_CHAT_ID}"
    
    email:
      enabled: false
      smtp_server: "${SMTP_SERVER}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"

# ========== Security Configuration ==========
security:
  wallet:
    encryption_enabled: true
    key_derivation: "pbkdf2"
    iterations: 100000
    
  api:
    rate_limiting:
      enabled: true
      requests_per_minute: 60
      
  secrets:
    vault_enabled: false
    vault_url: "${VAULT_URL}"
    vault_token: "${VAULT_TOKEN}"

# ========== Development Configuration ==========
development:
  mock_mode: false
  dry_run: false
  test_mode: false
  
  # Test wallets (DO NOT USE IN PRODUCTION)
  test_wallets:
    bsc: "${TEST_BSC_PRIVATE_KEY}"
    solana: "${TEST_SOLANA_PRIVATE_KEY}"
