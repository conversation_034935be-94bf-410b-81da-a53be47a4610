#!/usr/bin/env python3
"""
DyFlow 统一配置管理
整合所有配置文件和环境变量管理
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
import structlog

logger = structlog.get_logger(__name__)

@dataclass
class DatabaseConfig:
    """数据库配置"""
    type: str = "supabase"
    url: str = ""
    key: str = ""
    service_role_key: str = ""
    max_connections: int = 10
    connection_timeout: int = 30
    retry_attempts: int = 3

@dataclass
class ChainConfig:
    """区块链配置"""
    name: str
    rpc_url: str
    explorer_api: str = ""
    explorer_key: str = ""
    protocols: list = field(default_factory=list)

@dataclass
class APIConfig:
    """API配置"""
    base_url: str
    api_key: str = ""
    timeout: int = 30
    rate_limit: int = 100

@dataclass
class AgentConfig:
    """Agent配置"""
    enabled: bool = True
    model_provider: str = "ollama"
    model_name: str = "qwen2.5:3b"
    enable_reasoning: bool = True
    enable_memory: bool = True
    max_retries: int = 3
    timeout: int = 60

@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    enabled: bool = True
    risk_level: str = "MEDIUM"
    min_capital: float = 100.0
    max_capital: float = 10000.0
    parameters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UnifiedConfig:
    """统一配置类"""
    # 基本信息
    app_name: str = "DyFlow"
    app_version: str = "3.1"
    environment: str = "development"
    debug: bool = True
    log_level: str = "INFO"
    
    # 数据库配置
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    
    # 区块链配置
    chains: Dict[str, ChainConfig] = field(default_factory=dict)
    
    # API配置
    apis: Dict[str, APIConfig] = field(default_factory=dict)
    
    # Agent配置
    agents: Dict[str, AgentConfig] = field(default_factory=dict)
    
    # 策略配置
    strategies: Dict[str, StrategyConfig] = field(default_factory=dict)
    
    # 其他配置
    web_ui: Dict[str, Any] = field(default_factory=dict)
    monitoring: Dict[str, Any] = field(default_factory=dict)
    security: Dict[str, Any] = field(default_factory=dict)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Union[str, Path] = None):
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent
        self.config: Optional[UnifiedConfig] = None
        self._env_vars = {}
        
    def load_environment_variables(self):
        """加载环境变量"""
        env_mappings = {
            # 数据库
            "SUPABASE_URL": "database.url",
            "SUPABASE_ANON_KEY": "database.key", 
            "SUPABASE_SERVICE_ROLE_KEY": "database.service_role_key",
            
            # BSC
            "BSC_RPC_URL": "chains.bsc.rpc_url",
            "BSCSCAN_API_KEY": "chains.bsc.explorer_key",
            
            # Solana
            "SOLANA_RPC_URL": "chains.solana.rpc_url",
            
            # APIs
            "JUPITER_API_URL": "apis.jupiter.base_url",
            "METEORA_API_URL": "apis.meteora.base_url",
            "PANCAKESWAP_API_KEY": "apis.pancakeswap.api_key",
            
            # 其他
            "LOG_LEVEL": "log_level",
            "ENVIRONMENT": "environment"
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._env_vars[config_path] = value
    
    def _set_nested_value(self, obj: Any, path: str, value: Any):
        """设置嵌套属性值"""
        keys = path.split('.')
        current = obj
        
        for key in keys[:-1]:
            if not hasattr(current, key):
                setattr(current, key, type('obj', (object,), {})())
            current = getattr(current, key)
        
        setattr(current, keys[-1], value)
    
    def load_from_yaml(self, file_path: Union[str, Path]) -> UnifiedConfig:
        """从YAML文件加载配置"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.warning("config_file_not_found", path=str(file_path))
            return self._create_default_config()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)
            
            config = self._parse_yaml_to_config(yaml_data)
            logger.info("config_loaded_from_yaml", path=str(file_path))
            return config
            
        except Exception as e:
            logger.error("config_load_failed", path=str(file_path), error=str(e))
            return self._create_default_config()
    
    def _parse_yaml_to_config(self, yaml_data: Dict[str, Any]) -> UnifiedConfig:
        """解析YAML数据为配置对象"""
        config = UnifiedConfig()
        
        # 基本配置
        app_data = yaml_data.get('app', {})
        config.app_name = app_data.get('name', 'DyFlow')
        config.app_version = app_data.get('version', '3.1')
        config.environment = app_data.get('environment', 'development')
        config.debug = app_data.get('debug', True)
        config.log_level = app_data.get('log_level', 'INFO')
        
        # 数据库配置
        db_data = yaml_data.get('database', {})
        config.database = DatabaseConfig(
            type=db_data.get('type', 'supabase'),
            url=db_data.get('url', ''),
            key=db_data.get('key', ''),
            service_role_key=db_data.get('service_role_key', ''),
            max_connections=db_data.get('max_connections', 10),
            connection_timeout=db_data.get('connection_timeout', 30),
            retry_attempts=db_data.get('retry_attempts', 3)
        )
        
        # 区块链配置
        chains_data = yaml_data.get('chains', {})
        for chain_name, chain_config in chains_data.items():
            config.chains[chain_name] = ChainConfig(
                name=chain_name,
                rpc_url=chain_config.get('rpc_url', ''),
                explorer_api=chain_config.get('explorer_api', ''),
                explorer_key=chain_config.get('explorer_key', ''),
                protocols=chain_config.get('protocols', [])
            )
        
        # API配置
        apis_data = yaml_data.get('apis', {})
        for api_name, api_config in apis_data.items():
            config.apis[api_name] = APIConfig(
                base_url=api_config.get('base_url', ''),
                api_key=api_config.get('api_key', ''),
                timeout=api_config.get('timeout', 30),
                rate_limit=api_config.get('rate_limit', 100)
            )
        
        # Agent配置
        agents_data = yaml_data.get('agents', {})
        for agent_name, agent_config in agents_data.items():
            config.agents[agent_name] = AgentConfig(
                enabled=agent_config.get('enabled', True),
                model_provider=agent_config.get('model_provider', 'ollama'),
                model_name=agent_config.get('model_name', 'qwen2.5:3b'),
                enable_reasoning=agent_config.get('enable_reasoning', True),
                enable_memory=agent_config.get('enable_memory', True),
                max_retries=agent_config.get('max_retries', 3),
                timeout=agent_config.get('timeout', 60)
            )
        
        # 策略配置
        strategies_data = yaml_data.get('strategies', {})
        for strategy_name, strategy_config in strategies_data.items():
            config.strategies[strategy_name] = StrategyConfig(
                name=strategy_name,
                enabled=strategy_config.get('enabled', True),
                risk_level=strategy_config.get('risk_level', 'MEDIUM'),
                min_capital=strategy_config.get('min_capital', 100.0),
                max_capital=strategy_config.get('max_capital', 10000.0),
                parameters=strategy_config.get('parameters', {})
            )
        
        # 其他配置
        config.web_ui = yaml_data.get('web_ui', {})
        config.monitoring = yaml_data.get('monitoring', {})
        config.security = yaml_data.get('security', {})
        
        return config
    
    def _create_default_config(self) -> UnifiedConfig:
        """创建默认配置"""
        config = UnifiedConfig()
        
        # 设置默认的区块链配置
        config.chains['bsc'] = ChainConfig(
            name='bsc',
            rpc_url='https://bsc-dataseed.binance.org/',
            explorer_api='https://api.bscscan.com/api',
            protocols=['pancakeswap_v3']
        )
        
        config.chains['solana'] = ChainConfig(
            name='solana',
            rpc_url='https://api.mainnet-beta.solana.com',
            protocols=['meteora', 'orca', 'raydium']
        )
        
        # 设置默认的API配置
        config.apis['jupiter'] = APIConfig(
            base_url='https://quote-api.jup.ag/v6'
        )
        
        config.apis['meteora'] = APIConfig(
            base_url='https://dlmm-api.meteora.ag'
        )
        
        # 设置默认的Agent配置
        default_agent_config = AgentConfig()
        for agent_name in ['planner', 'risk_sentinel', 'scorer', 'trading_executor']:
            config.agents[agent_name] = default_agent_config
        
        logger.info("default_config_created")
        return config
    
    def apply_environment_overrides(self, config: UnifiedConfig) -> UnifiedConfig:
        """应用环境变量覆盖"""
        self.load_environment_variables()
        
        for config_path, value in self._env_vars.items():
            try:
                self._set_nested_value(config, config_path, value)
                logger.debug("env_override_applied", path=config_path, value=value)
            except Exception as e:
                logger.warning("env_override_failed", path=config_path, error=str(e))
        
        return config
    
    def load_config(self, config_file: str = "config.yaml") -> UnifiedConfig:
        """加载完整配置"""
        config_path = self.config_dir / config_file
        
        # 从YAML加载基础配置
        config = self.load_from_yaml(config_path)
        
        # 应用环境变量覆盖
        config = self.apply_environment_overrides(config)
        
        self.config = config
        logger.info("unified_config_loaded", 
                   environment=config.environment,
                   chains=list(config.chains.keys()),
                   agents=list(config.agents.keys()))
        
        return config
    
    def save_config(self, config: UnifiedConfig, file_path: Union[str, Path]):
        """保存配置到文件"""
        # 这里可以实现配置保存功能
        # 暂时不实现，避免意外覆盖配置文件
        pass
    
    def get_config(self) -> Optional[UnifiedConfig]:
        """获取当前配置"""
        return self.config

# 全局配置管理器实例
config_manager = ConfigManager()

def load_unified_config(config_file: str = "config.yaml") -> UnifiedConfig:
    """加载统一配置的便捷函数"""
    return config_manager.load_config(config_file)

def get_current_config() -> Optional[UnifiedConfig]:
    """获取当前配置的便捷函数"""
    return config_manager.get_config()
