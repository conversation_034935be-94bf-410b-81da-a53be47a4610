#!/usr/bin/env python3
"""
測試 Agno Framework 架構 - 驗證 Agent 間通訊
證明 Agno 可以替代 NATS 消息總線
"""

import asyncio
from datetime import datetime

def test_agno_availability():
    """測試 Agno Framework 可用性"""
    print("🔍 檢查 Agno Framework...")
    
    try:
        import agno
        print("✅ Agno Framework: 已安裝")
        
        from agno.agent import Agent
        from agno.team import Team
        from agno.workflow import Workflow
        from agno.models.ollama import Ollama
        print("✅ Agno 核心組件: 可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ Agno Framework: 未安裝 ({e})")
        return False

def test_ollama_connection():
    """測試 Ollama 連接"""
    print("\n🔍 檢查 Ollama 服務器...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            print("✅ Ollama 服務器: 運行中")
            return True
        else:
            print("❌ Ollama 服務器: 無響應")
            return False
    except Exception as e:
        print(f"❌ Ollama 服務器: 連接失敗 ({e})")
        return False

def test_agent_communication():
    """測試 Agno Agent 間通訊"""
    print("\n🧪 測試 Agno Agent 通訊...")
    
    try:
        from agno.agent import Agent
        from agno.team import Team
        from agno.models.ollama import Ollama
        
        # 創建市場分析 Agent
        market_agent = Agent(
            name="MarketAnalyst",
            role="Market data analyzer",
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=[
                "You are a DeFi market analyst.",
                "Analyze market conditions and provide insights.",
                "Keep responses concise and actionable."
            ]
        )
        
        # 創建策略 Agent
        strategy_agent = Agent(
            name="StrategyPlanner",
            role="LP strategy planner", 
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            instructions=[
                "You are a DeFi strategy planner.",
                "Create LP strategies based on market analysis.",
                "Focus on risk-adjusted returns."
            ]
        )
        
        # 創建協調 Team
        dyflow_team = Team(
            name="DyFlow Strategy Team",
            mode="coordinate",  # 協調模式
            model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
            members=[market_agent, strategy_agent],
            instructions=[
                "You coordinate DeFi LP strategy development.",
                "First get market analysis, then create strategies.",
                "Ensure strategies align with market conditions."
            ],
            show_members_responses=True
        )
        
        print("✅ Agno Agents 創建成功")
        
        # 測試 Team 協調
        print("🔄 測試 Team 協調...")
        
        test_prompt = """
        Analyze current DeFi market conditions and create an optimal LP strategy.
        
        Focus on:
        1. Market sentiment and volatility
        2. Best LP opportunities 
        3. Risk management approach
        
        Keep the analysis brief but actionable.
        """
        
        # 執行協調
        response = dyflow_team.run(test_prompt)
        
        if response and response.content:
            print("✅ Agent 通訊測試成功!")
            print(f"📝 Team 響應長度: {len(response.content)} 字符")
            print(f"📄 響應預覽: {response.content[:200]}...")
            return True
        else:
            print("❌ Agent 通訊測試失敗 - 無響應")
            return False
            
    except Exception as e:
        print(f"❌ Agent 通訊測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_concept():
    """測試 Workflow 概念"""
    print("\n🧪 測試 Agno Workflow 概念...")
    
    try:
        from agno.workflow import Workflow, RunResponse, RunEvent
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        from typing import Iterator
        
        class SimpleWorkflow(Workflow):
            """簡單的測試 Workflow"""
            
            description = "Test workflow for DyFlow architecture validation"
            
            def __init__(self):
                super().__init__(session_id=f"test-{int(datetime.now().timestamp())}")
                
                # 創建測試 Agent
                self.test_agent = Agent(
                    name="TestAgent",
                    model=Ollama(id="qwen3:7b", host="http://localhost:11434"),
                    instructions=["You are a test agent. Respond with workflow status."]
                )
            
            def run(self, message: str) -> Iterator[RunResponse]:
                """執行測試 workflow"""
                yield RunResponse(
                    content="🔄 Workflow 開始執行...",
                    event=RunEvent.run_started
                )
                
                # 使用 Agent 處理
                agent_response = self.test_agent.run(message)
                
                if agent_response and agent_response.content:
                    yield RunResponse(
                        content=f"✅ Agent 響應: {agent_response.content}",
                        event=RunEvent.run_completed
                    )
                else:
                    yield RunResponse(
                        content="❌ Agent 無響應",
                        event=RunEvent.run_completed
                    )
                
                yield RunResponse(
                    content="🎉 Workflow 執行完成",
                    event=RunEvent.workflow_completed
                )
        
        # 測試 Workflow
        workflow = SimpleWorkflow()
        print("✅ Workflow 創建成功")
        
        # 執行測試
        print("🔄 執行 Workflow...")
        responses = list(workflow.run("Test workflow execution"))
        
        print(f"✅ Workflow 測試完成 - {len(responses)} 個響應")
        for i, response in enumerate(responses):
            print(f"  {i+1}. {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("  🧪 DyFlow Agno 架構驗證測試")
    print("  驗證 Agno Framework 可以替代 NATS 消息總線")
    print("=" * 60)
    
    # 執行測試
    tests = [
        ("Agno Framework 可用性", test_agno_availability),
        ("Ollama 服務器連接", test_ollama_connection),
        ("Agent 間通訊", test_agent_communication),
        ("Workflow 概念", test_workflow_concept)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("  📊 測試結果總結")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總體結果: {passed}/{len(results)} 測試通過")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！Agno Framework 可以完全替代 NATS 消息總線")
        print("✅ 建議使用 Agno Teams 和 Workflows 進行 Agent 協調")
    else:
        print(f"\n⚠️  {len(results) - passed} 個測試失敗")
        print("建議檢查 Agno 安裝和 Ollama 服務器狀態")
    
    print("\n💡 Agno 架構優勢:")
    print("  - 內建 Agent 通訊機制")
    print("  - 無需外部消息總線")
    print("  - 結構化輸出和狀態管理")
    print("  - 純 Python 工作流程控制")

if __name__ == "__main__":
    main()
