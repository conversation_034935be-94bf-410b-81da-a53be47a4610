#!/usr/bin/env python3
"""
DyFlow Agno Framework and Workflow Status Checker
檢查 Agno Framework 和工作流程的完整狀態
"""

import sys
import os
import json
import asyncio
from datetime import datetime
from pathlib import Path

def print_header(title):
    """打印標題"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """打印章節"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

def check_agno_framework():
    """檢查 Agno Framework 狀態"""
    print_section("Agno Framework Status")
    
    try:
        import agno
        try:
            version = agno.__version__
        except AttributeError:
            version = "installed"
        print(f"✅ Agno Framework: v{version}")

        # 檢查核心組件
        components = {
            'Agent': 'agno.agent.Agent',
            'OpenAI Model': 'agno.models.openai.OpenAIChat',
            'Ollama Model': 'agno.models.ollama.Ollama',
            'Anthropic Model': 'agno.models.anthropic.Claude',
            'Reasoning Tools': 'agno.tools.reasoning.ReasoningTools',
            'DuckDuckGo Tools': 'agno.tools.duckduckgo.DuckDuckGoTools',
            'SQLite Storage': 'agno.storage.sqlite.SqliteStorage',
        }
        
        for name, module_path in components.items():
            try:
                parts = module_path.split('.')
                module = __import__('.'.join(parts[:-1]), fromlist=[parts[-1]])
                getattr(module, parts[-1])
                print(f"✅ {name}: Available")
            except ImportError as e:
                print(f"❌ {name}: Not available ({e})")
            except AttributeError as e:
                print(f"❌ {name}: Import error ({e})")
                
    except ImportError as e:
        print(f"❌ Agno Framework: Not installed ({e})")
        return False
    
    return True

def check_ollama_connection():
    """檢查 Ollama 連接"""
    print_section("Ollama Connection")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            version_data = response.json()
            print(f"✅ Ollama Server: Running (v{version_data.get('version', 'unknown')})")
            
            # 檢查可用模型
            models_response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if models_response.status_code == 200:
                models_data = models_response.json()
                models = [model['name'] for model in models_data.get('models', [])]
                print(f"✅ Available Models: {', '.join(models) if models else 'None'}")
                
                # 檢查 qwen3 模型
                qwen_models = [m for m in models if 'qwen' in m.lower()]
                if qwen_models:
                    print(f"✅ Qwen Models: {', '.join(qwen_models)}")
                else:
                    print("❌ Qwen Models: Not found")
            else:
                print("❌ Models List: Failed to retrieve")
        else:
            print(f"❌ Ollama Server: Not responding (status: {response.status_code})")
    except Exception as e:
        print(f"❌ Ollama Server: Connection failed ({e})")

def check_nats_support():
    """檢查 NATS 支持"""
    print_section("NATS Message Bus")
    
    try:
        import nats
        print(f"✅ NATS Client: Available")
        
        # 檢查 NATS 服務器連接（如果運行）
        try:
            import asyncio
            async def test_nats():
                try:
                    nc = await nats.connect("nats://localhost:4222", connect_timeout=2)
                    await nc.close()
                    return True
                except Exception:
                    return False
            
            if asyncio.run(test_nats()):
                print("✅ NATS Server: Running")
            else:
                print("❌ NATS Server: Not running")
        except Exception as e:
            print(f"❌ NATS Server: Connection test failed ({e})")
            
    except ImportError:
        print("❌ NATS Client: Not installed")

def check_workflow_config():
    """檢查工作流程配置"""
    print_section("Workflow Configuration")
    
    workflow_file = Path("workflows/dyflow_v33_workflow.yaml")
    if workflow_file.exists():
        print(f"✅ Workflow Config: {workflow_file}")
        
        try:
            import yaml
            with open(workflow_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 檢查關鍵配置
            if 'channels' in config:
                channels = config['channels']
                print(f"✅ NATS Channels: {len(channels)} defined")
                for channel in channels[:3]:  # 顯示前3個
                    print(f"   - {channel['name']}: {channel['description']}")
                if len(channels) > 3:
                    print(f"   ... and {len(channels) - 3} more")
            
            if 'agents' in config:
                agents = config['agents']
                print(f"✅ Agents Config: {len(agents)} agents defined")
                for agent in agents[:3]:  # 顯示前3個
                    print(f"   - {agent['id']}: {agent['class']}")
                if len(agents) > 3:
                    print(f"   ... and {len(agents) - 3} more")
                    
        except Exception as e:
            print(f"❌ Workflow Config: Parse error ({e})")
    else:
        print(f"❌ Workflow Config: Not found ({workflow_file})")

def check_agno_agents():
    """檢查 Agno Agents 實現"""
    print_section("Agno Agents Implementation")
    
    agent_files = [
        "src/agents/planner_agno.py",
        "src/agents/risk_sentinel_agno.py", 
        "src/agents/scorer_v2_agno.py",
        "src/agents/trading_executor_agent.py",
        "src/core/dyflow_agno_scheduler.py"
    ]
    
    for agent_file in agent_files:
        if Path(agent_file).exists():
            print(f"✅ {Path(agent_file).name}: Implemented")
        else:
            print(f"❌ {Path(agent_file).name}: Missing")

def check_test_results():
    """檢查測試結果"""
    print_section("Test Results")
    
    test_file = Path("data/test_reports/agno_agent_test_test_1749812008.json")
    if test_file.exists():
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                test_data = json.load(f)
            
            print(f"✅ Test Report: {test_file.name}")
            print(f"   Total Tests: {test_data.get('total_tests', 0)}")
            print(f"   Passed: {test_data.get('passed_tests', 0)}")
            print(f"   Failed: {test_data.get('failed_tests', 0)}")
            print(f"   Success Rate: {test_data.get('success_rate', 0)}%")
            
            if 'results' in test_data:
                for test_name, result in test_data['results'].items():
                    status = "✅" if result['status'] == 'success' else "❌"
                    print(f"   {status} {test_name}: {result['message']}")
                    
        except Exception as e:
            print(f"❌ Test Report: Parse error ({e})")
    else:
        print(f"❌ Test Report: Not found")

def main():
    """主函數"""
    print_header("DyFlow Agno Framework & Workflow Status Check")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Python: {sys.version}")
    print(f"Working Directory: {os.getcwd()}")
    
    # 執行所有檢查
    agno_ok = check_agno_framework()
    check_ollama_connection()
    check_nats_support()
    check_workflow_config()
    check_agno_agents()
    check_test_results()
    
    # 總結
    print_header("Summary & Recommendations")
    
    if agno_ok:
        print("✅ Agno Framework is properly installed and functional")
    else:
        print("❌ Agno Framework needs to be installed or fixed")
    
    print("\n📋 Next Steps:")
    print("1. Install missing dependencies if any")
    print("2. Start Ollama server if using local LLM")
    print("3. Install and start NATS server for message bus")
    print("4. Run workflow tests to verify integration")
    print("5. Check agent communication and coordination")

if __name__ == "__main__":
    main()
