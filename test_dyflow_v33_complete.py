#!/usr/bin/env python3
"""
DyFlow v3.3 完整系統測試
測試 Agno Workflow API 和 Agent 協作
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
import structlog

logger = structlog.get_logger(__name__)

class DyFlowV33Tester:
    """DyFlow v3.3 系統測試器"""
    
    def __init__(self):
        self.base_url = "http://localhost:8001"
        self.test_results = []
        
    def print_banner(self):
        """打印測試橫幅"""
        print("""
╔══════════════════════════════════════════════════════════════╗
║                DyFlow v3.3 Complete System Test             ║
║              測試 7 個 Agents + 8-phase 啟動序列             ║
╚══════════════════════════════════════════════════════════════╝
        """)
    
    async def test_api_health(self):
        """測試 API 健康狀態"""
        print("🔍 測試 API 健康狀態...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/system/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"  ✅ API 健康 - 狀態: {data.get('overall_status')}")
                        print(f"  📊 當前階段: {data.get('current_phase')}")
                        print(f"  🤖 活躍 Agents: {len(data.get('active_agents', []))}")
                        return True
                    else:
                        print(f"  ❌ API 響應異常: {response.status}")
                        return False
        except Exception as e:
            print(f"  ❌ API 連接失敗: {e}")
            return False
    
    async def test_agents_status(self):
        """測試 Agents 狀態"""
        print("\n🤖 測試 Agents 狀態...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/agents") as response:
                    if response.status == 200:
                        data = await response.json()
                        agents = data.get('agents', [])
                        
                        print(f"  📊 總 Agents: {len(agents)}")
                        
                        expected_agents = [
                            "SupervisorAgent", "HealthGuardAgent", "MarketIntelAgent",
                            "PortfolioManagerAgent", "StrategyAgent", "ExecutionAgent", "RiskSentinelAgent"
                        ]
                        
                        for agent_info in agents:
                            name = agent_info.get('name')
                            status = agent_info.get('status')
                            phase = agent_info.get('phase')
                            
                            if name in expected_agents:
                                print(f"  ✅ {name} - Phase {phase} - 狀態: {status}")
                                expected_agents.remove(name)
                            else:
                                print(f"  ⚠️ 未預期的 Agent: {name}")
                        
                        for missing in expected_agents:
                            print(f"  ❌ 缺失 Agent: {missing}")
                        
                        return len(expected_agents) == 0
                    else:
                        print(f"  ❌ Agents API 響應異常: {response.status}")
                        return False
        except Exception as e:
            print(f"  ❌ Agents API 連接失敗: {e}")
            return False
    
    async def test_pools_data(self):
        """測試池子數據"""
        print("\n🏊 測試池子數據...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/pools") as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data.get('pools', [])
                        
                        print(f"  📊 掃描到池子: {len(pools)}")
                        
                        for pool in pools[:3]:  # 顯示前 3 個池子
                            pair = pool.get('pair')
                            chain = pool.get('chain')
                            tvl = pool.get('tvl_usd', 0)
                            apr = pool.get('apr', 0)
                            strategy = pool.get('strategy_recommendation', 'N/A')
                            
                            print(f"  🔹 {pair} ({chain.upper()}) - TVL: ${tvl:,.0f} - APR: {apr:.1f}% - 策略: {strategy}")
                        
                        return len(pools) > 0
                    else:
                        print(f"  ❌ 池子 API 響應異常: {response.status}")
                        return False
        except Exception as e:
            print(f"  ❌ 池子 API 連接失敗: {e}")
            return False
    
    async def test_workflow_start(self):
        """測試工作流程啟動"""
        print("\n🚀 測試工作流程啟動...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/api/workflow/start") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"  ✅ 工作流程啟動成功")
                        print(f"  📝 消息: {data.get('message')}")
                        print(f"  📊 總階段: {data.get('total_phases')}")
                        print(f"  🤖 活躍 Agents: {data.get('active_agents')}")
                        return True
                    else:
                        print(f"  ❌ 工作流程啟動失敗: {response.status}")
                        return False
        except Exception as e:
            print(f"  ❌ 工作流程啟動連接失敗: {e}")
            return False
    
    async def test_workflow_progress(self):
        """測試工作流程進度"""
        print("\n⏳ 監控工作流程進度...")
        
        max_wait = 30  # 最多等待 30 秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.base_url}/api/system/status") as response:
                        if response.status == 200:
                            data = await response.json()
                            current_phase = data.get('current_phase', 0)
                            progress = data.get('progress_percentage', 0)
                            overall_status = data.get('overall_status')
                            
                            print(f"  📊 階段 {current_phase}/9 - 進度: {progress:.1f}% - 狀態: {overall_status}")
                            
                            if overall_status == "completed":
                                print("  ✅ 工作流程完成!")
                                return True
                            elif overall_status == "failed":
                                print("  ❌ 工作流程失敗!")
                                return False
                            
                            await asyncio.sleep(2)
                        else:
                            print(f"  ❌ 狀態查詢失敗: {response.status}")
                            return False
            except Exception as e:
                print(f"  ❌ 進度監控失敗: {e}")
                return False
        
        print("  ⏰ 工作流程監控超時")
        return False
    
    async def test_trading_mode(self):
        """測試交易模式設置"""
        print("\n⚙️ 測試交易模式設置...")
        
        modes = ["paused", "exit_only", "active"]
        
        for mode in modes:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/api/trading_mode",
                        json={"mode": mode}
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f"  ✅ 交易模式設置為: {mode}")
                        else:
                            print(f"  ❌ 設置交易模式失敗: {response.status}")
                            return False
            except Exception as e:
                print(f"  ❌ 交易模式設置連接失敗: {e}")
                return False
        
        return True
    
    async def run_complete_test(self):
        """運行完整測試"""
        self.print_banner()
        
        tests = [
            ("API 健康檢查", self.test_api_health),
            ("Agents 狀態檢查", self.test_agents_status),
            ("池子數據檢查", self.test_pools_data),
            ("交易模式測試", self.test_trading_mode),
            ("工作流程啟動", self.test_workflow_start),
            ("工作流程進度", self.test_workflow_progress),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*60}")
            print(f"測試: {test_name}")
            print('='*60)
            
            try:
                result = await test_func()
                if result:
                    passed += 1
                    self.test_results.append({"test": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"  ❌ 測試異常: {e}")
                self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
        
        # 測試總結
        print(f"\n{'='*60}")
        print("🎯 測試總結")
        print('='*60)
        print(f"總測試: {total}")
        print(f"通過: {passed}")
        print(f"失敗: {total - passed}")
        print(f"成功率: {(passed/total)*100:.1f}%")
        
        print("\n📋 詳細結果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"  {status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"    錯誤: {result['error']}")
        
        # 保存測試結果
        test_report = {
            "test_summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": total - passed,
                "success_rate": f"{(passed/total)*100:.1f}%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat(),
            "dyflow_version": "v3.3",
            "architecture": "Complete_Agno_Framework"
        }
        
        with open("dyflow_v33_complete_test_results.json", "w", encoding="utf-8") as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 測試報告已保存: dyflow_v33_complete_test_results.json")
        
        return passed == total

async def main():
    """主函數"""
    tester = DyFlowV33Tester()
    success = await tester.run_complete_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
