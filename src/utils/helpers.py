from datetime import datetime, timezone
import structlog
import logging
import os


def get_utc_timestamp() -> str:
    """Return the current UTC timestamp as ISO formatted string."""
    return datetime.now(timezone.utc).isoformat()


def setup_logging():
    """設置結構化日誌"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()

    # 配置標準日誌
    logging.basicConfig(
        level=getattr(logging, log_level),
        format="%(message)s"
    )

    # 配置 structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
