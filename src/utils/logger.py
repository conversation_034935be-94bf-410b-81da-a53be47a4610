#!/usr/bin/env python3
"""
DyFlow 統一日誌配置
提供一致的日誌格式和級別管理
"""

import logging
import structlog
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

# 日誌級別映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# 表情符號映射
EMOJI_MAP = {
    'DEBUG': '🔍',
    'INFO': 'ℹ️',
    'SUCCESS': '✅',
    'WARNING': '⚠️',
    'ERROR': '❌',
    'CRITICAL': '🚨',
    'API': '🌐',
    'DATA': '📊',
    'PROCESS': '🔄',
    'AGENT': '🤖',
    'STRATEGY': '📈',
    'POOL': '🏊',
    'TRADE': '💱',
    'WALLET': '💰',
    'HEALTH': '🏥',
    'RISK': '⚡',
    'WORKFLOW': '⚙️'
}

class DyFlowFormatter(logging.Formatter):
    """DyFlow 自定義日誌格式器"""
    
    def __init__(self, use_emoji: bool = True, use_colors: bool = True):
        self.use_emoji = use_emoji
        self.use_colors = use_colors
        
        # 顏色代碼
        self.colors = {
            'DEBUG': '\033[36m',    # 青色
            'INFO': '\033[37m',     # 白色
            'SUCCESS': '\033[32m',  # 綠色
            'WARNING': '\033[33m',  # 黃色
            'ERROR': '\033[31m',    # 紅色
            'CRITICAL': '\033[35m', # 紫色
            'RESET': '\033[0m'      # 重置
        }
        
        super().__init__()
    
    def format(self, record):
        # 獲取時間戳
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # 獲取日誌級別
        level = record.levelname
        
        # 獲取表情符號
        emoji = EMOJI_MAP.get(level, EMOJI_MAP.get('INFO', 'ℹ️')) if self.use_emoji else ''
        
        # 獲取顏色
        color = self.colors.get(level, self.colors['INFO']) if self.use_colors else ''
        reset = self.colors['RESET'] if self.use_colors else ''
        
        # 獲取模組名稱
        module = record.name.split('.')[-1] if '.' in record.name else record.name
        
        # 構建基本消息
        message = record.getMessage()
        
        # 處理額外的結構化數據
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info']:
                extra_data[key] = value
        
        # 格式化額外數據
        extra_str = ''
        if extra_data:
            extra_parts = []
            for key, value in extra_data.items():
                if isinstance(value, (int, float)):
                    extra_parts.append(f"{key}={value}")
                elif isinstance(value, str):
                    extra_parts.append(f"{key}='{value}'")
                else:
                    extra_parts.append(f"{key}={value}")
            if extra_parts:
                extra_str = f" [{', '.join(extra_parts)}]"
        
        # 構建最終消息
        formatted_message = f"{color}{emoji} {timestamp} [{level:8}] {module:15} | {message}{extra_str}{reset}"
        
        return formatted_message

def setup_logging(
    level: str = 'INFO',
    log_file: Optional[str] = None,
    use_emoji: bool = True,
    use_colors: bool = True,
    structured: bool = True
) -> logging.Logger:
    """
    設置統一的日誌配置
    
    Args:
        level: 日誌級別 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日誌文件路徑 (可選)
        use_emoji: 是否使用表情符號
        use_colors: 是否使用顏色
        structured: 是否使用結構化日誌
    
    Returns:
        配置好的 logger
    """
    
    # 設置根日誌級別
    logging.basicConfig(level=LOG_LEVELS.get(level.upper(), logging.INFO))
    
    # 創建根 logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # 清除現有處理器
    
    # 創建控制台處理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(LOG_LEVELS.get(level.upper(), logging.INFO))
    
    # 設置格式器
    formatter = DyFlowFormatter(use_emoji=use_emoji, use_colors=use_colors)
    console_handler.setFormatter(formatter)
    
    root_logger.addHandler(console_handler)
    
    # 如果指定了日誌文件，添加文件處理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(LOG_LEVELS.get(level.upper(), logging.INFO))
        
        # 文件日誌不使用顏色和表情符號
        file_formatter = DyFlowFormatter(use_emoji=False, use_colors=False)
        file_handler.setFormatter(file_formatter)
        
        root_logger.addHandler(file_handler)
    
    # 配置 structlog
    if structured:
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    return root_logger

def get_logger(name: str) -> structlog.BoundLogger:
    """
    獲取結構化 logger
    
    Args:
        name: logger 名稱
    
    Returns:
        結構化 logger
    """
    return structlog.get_logger(name)

def log_api_call(logger, method: str, url: str, status: Optional[int] = None, 
                 duration: Optional[float] = None, **kwargs):
    """
    記錄 API 調用
    
    Args:
        logger: logger 實例
        method: HTTP 方法
        url: API URL
        status: HTTP 狀態碼
        duration: 請求持續時間（秒）
        **kwargs: 額外參數
    """
    emoji = EMOJI_MAP['API']
    if status:
        if 200 <= status < 300:
            level = 'info'
            emoji = EMOJI_MAP['SUCCESS']
        elif 400 <= status < 500:
            level = 'warning'
            emoji = EMOJI_MAP['WARNING']
        else:
            level = 'error'
            emoji = EMOJI_MAP['ERROR']
    else:
        level = 'info'
    
    log_data = {
        'method': method,
        'url': url,
        'status': status,
        'duration_ms': round(duration * 1000, 2) if duration else None,
        **kwargs
    }
    
    getattr(logger, level)(f"{emoji} API {method} {url}", **log_data)

def log_pool_data(logger, pool_name: str, tvl: float, apr: float, 
                  chain: str, **kwargs):
    """
    記錄池子數據
    
    Args:
        logger: logger 實例
        pool_name: 池子名稱
        tvl: TVL
        apr: APR
        chain: 區塊鏈
        **kwargs: 額外參數
    """
    emoji = EMOJI_MAP['POOL']
    logger.info(f"{emoji} Pool {pool_name}", 
                tvl=tvl, apr=apr, chain=chain, **kwargs)

def log_agent_action(logger, agent_name: str, action: str, 
                     status: str = 'started', **kwargs):
    """
    記錄 Agent 動作
    
    Args:
        logger: logger 實例
        agent_name: Agent 名稱
        action: 動作描述
        status: 狀態 (started, completed, failed)
        **kwargs: 額外參數
    """
    emoji = EMOJI_MAP['AGENT']
    if status == 'completed':
        emoji = EMOJI_MAP['SUCCESS']
        level = 'info'
    elif status == 'failed':
        emoji = EMOJI_MAP['ERROR']
        level = 'error'
    else:
        level = 'info'
    
    getattr(logger, level)(f"{emoji} {agent_name} {action}", 
                          status=status, **kwargs)

# 預設 logger 實例
default_logger = get_logger(__name__)
