from dataclasses import dataclass, field
from typing import Any, Dict, List
from enum import Enum


class StrategyType(str, Enum):
    ACTIVE = "active"
    PASSIVE = "passive"
    HEDGE = "hedge"


class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class PoolRaw:
    pool_id: str
    chain: str
    protocol: str
    token0: Dict[str, Any]
    token1: Dict[str, Any]
    fee_tier: float
    tvl_usd: float
    volume_24h: float
    fee_24h: float
    extra: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PoolScore:
    pool_id: str
    score: float
    risk_level: str
    timestamp: str
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Plan:
    pool_id: str
    strategy_type: StrategyType
    allocation_amount: float
    priority: int
    expected_apy: float
    risk_level: str
    reasoning: str


@dataclass
class AgentResult:
    agent_name: str
    data: Any
    timestamp: str
    status: str = "success"
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskAlert:
    level: RiskLevel
    message: str
    affected_pools: List[str]
    recommended_action: str
    timestamp: str
