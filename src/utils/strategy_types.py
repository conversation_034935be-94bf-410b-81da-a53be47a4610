"""
DyFlow DLMM LP 策略类型定义
支持四种主要的流动性提供策略
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import structlog

logger = structlog.get_logger(__name__)

class DLMMStrategyType(Enum):
    """DLMM 策略类型枚举"""
    SPOT_BALANCED = "spot_balanced"           # 对称流动性
    CURVE_BALANCED = "curve_balanced"         # 曲线分布
    BID_ASK_BALANCED = "bid_ask_balanced"     # 买卖价差
    SPOT_IMBALANCED = "spot_imbalanced"       # 单边流动性

@dataclass
class StrategyParameters:
    """策略参数基类"""
    strategy_type: DLMMStrategyType
    token_amount: float
    token_mint: str
    min_bin_id: Optional[int] = None
    max_bin_id: Optional[int] = None
    auto_fill: bool = True
    
@dataclass
class SpotBalancedParams(StrategyParameters):
    """对称流动性策略参数"""
    range_width: int = 20  # ±20 bins around current price
    distribution_type: str = "uniform"  # uniform, normal, exponential

    def __init__(self, token_amount: float, token_mint: str,
                 range_width: int = 20, distribution_type: str = "uniform", **kwargs):
        super().__init__(
            strategy_type=DLMMStrategyType.SPOT_BALANCED,
            token_amount=token_amount,
            token_mint=token_mint,
            **kwargs
        )
        self.range_width = range_width
        self.distribution_type = distribution_type

@dataclass
class CurveBalancedParams(StrategyParameters):
    """曲线分布策略参数"""
    curve_steepness: float = 2.0  # 曲线陡峭度
    range_width: int = 50  # ±50 bins for wider distribution
    concentration_factor: float = 0.7  # 中心集中度

    def __init__(self, token_amount: float, token_mint: str,
                 curve_steepness: float = 2.0, range_width: int = 50,
                 concentration_factor: float = 0.7, **kwargs):
        super().__init__(
            strategy_type=DLMMStrategyType.CURVE_BALANCED,
            token_amount=token_amount,
            token_mint=token_mint,
            **kwargs
        )
        self.curve_steepness = curve_steepness
        self.range_width = range_width
        self.concentration_factor = concentration_factor

@dataclass
class BidAskBalancedParams(StrategyParameters):
    """买卖价差策略参数"""
    bid_range: int = 15  # 买单范围 (bins below current price)
    ask_range: int = 15  # 卖单范围 (bins above current price)
    bid_weight: float = 0.5  # 买单权重
    ask_weight: float = 0.5  # 卖单权重
    spread_multiplier: float = 1.2  # 价差倍数

    def __init__(self, token_amount: float, token_mint: str,
                 bid_range: int = 15, ask_range: int = 15,
                 bid_weight: float = 0.5, ask_weight: float = 0.5,
                 spread_multiplier: float = 1.2, **kwargs):
        super().__init__(
            strategy_type=DLMMStrategyType.BID_ASK_BALANCED,
            token_amount=token_amount,
            token_mint=token_mint,
            **kwargs
        )
        self.bid_range = bid_range
        self.ask_range = ask_range
        self.bid_weight = bid_weight
        self.ask_weight = ask_weight
        self.spread_multiplier = spread_multiplier

@dataclass
class SpotImbalancedParams(StrategyParameters):
    """单边流动性策略参数"""
    direction: str = "long"  # long (看涨) or short (看跌)
    range_width: int = 30  # 单边范围宽度
    concentration_bins: int = 5  # 集中投放的 bin 数量
    tail_distribution: float = 0.3  # 尾部分布比例

    def __init__(self, token_amount: float, token_mint: str,
                 direction: str = "long", range_width: int = 30,
                 concentration_bins: int = 5, tail_distribution: float = 0.3, **kwargs):
        super().__init__(
            strategy_type=DLMMStrategyType.SPOT_IMBALANCED,
            token_amount=token_amount,
            token_mint=token_mint,
            **kwargs
        )
        self.direction = direction
        self.range_width = range_width
        self.concentration_bins = concentration_bins
        self.tail_distribution = tail_distribution

class StrategyFactory:
    """策略工厂类"""
    
    @staticmethod
    def create_strategy_params(strategy_type: str, **kwargs) -> StrategyParameters:
        """创建策略参数"""
        strategy_map = {
            "spot_balanced": SpotBalancedParams,
            "curve_balanced": CurveBalancedParams,
            "bid_ask_balanced": BidAskBalancedParams,
            "spot_imbalanced": SpotImbalancedParams
        }
        
        if strategy_type not in strategy_map:
            raise ValueError(f"Unsupported strategy type: {strategy_type}")
        
        strategy_class = strategy_map[strategy_type]
        return strategy_class(**kwargs)
    
    @staticmethod
    def get_strategy_description(strategy_type: DLMMStrategyType) -> str:
        """获取策略描述"""
        descriptions = {
            DLMMStrategyType.SPOT_BALANCED: "对称流动性策略：在当前价格周围均匀分布流动性，适合稳定市场",
            DLMMStrategyType.CURVE_BALANCED: "曲线分布策略：按曲线分布流动性，中心集中，适合波动市场",
            DLMMStrategyType.BID_ASK_BALANCED: "买卖价差策略：分别在买卖两侧提供流动性，适合做市",
            DLMMStrategyType.SPOT_IMBALANCED: "单边流动性策略：主要在一个方向提供流动性，适合趋势市场"
        }
        return descriptions.get(strategy_type, "未知策略")

@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    description: str
    risk_level: str  # LOW, MEDIUM, HIGH
    min_capital: float  # 最小资金要求
    expected_apr: float  # 预期年化收益率
    max_impermanent_loss: float  # 最大无常损失
    rebalance_frequency: str  # 重平衡频率
    
# 预定义策略配置
STRATEGY_CONFIGS = {
    DLMMStrategyType.SPOT_BALANCED: StrategyConfig(
        name="对称流动性",
        description="在当前价格周围均匀分布流动性",
        risk_level="LOW",
        min_capital=100.0,
        expected_apr=15.0,
        max_impermanent_loss=5.0,
        rebalance_frequency="daily"
    ),
    DLMMStrategyType.CURVE_BALANCED: StrategyConfig(
        name="曲线分布",
        description="按曲线分布流动性，中心集中",
        risk_level="MEDIUM",
        min_capital=200.0,
        expected_apr=25.0,
        max_impermanent_loss=8.0,
        rebalance_frequency="weekly"
    ),
    DLMMStrategyType.BID_ASK_BALANCED: StrategyConfig(
        name="买卖价差",
        description="分别在买卖两侧提供流动性",
        risk_level="MEDIUM",
        min_capital=500.0,
        expected_apr=30.0,
        max_impermanent_loss=10.0,
        rebalance_frequency="daily"
    ),
    DLMMStrategyType.SPOT_IMBALANCED: StrategyConfig(
        name="单边流动性",
        description="主要在一个方向提供流动性",
        risk_level="HIGH",
        min_capital=300.0,
        expected_apr=40.0,
        max_impermanent_loss=15.0,
        rebalance_frequency="hourly"
    )
}

def get_strategy_config(strategy_type: DLMMStrategyType) -> StrategyConfig:
    """获取策略配置"""
    return STRATEGY_CONFIGS.get(strategy_type)

def validate_strategy_params(params: StrategyParameters) -> bool:
    """验证策略参数"""
    try:
        # 基础验证
        if params.token_amount <= 0:
            logger.error("Invalid token amount", amount=params.token_amount)
            return False
        
        if not params.token_mint:
            logger.error("Token mint is required")
            return False
        
        # 策略特定验证
        if isinstance(params, SpotBalancedParams):
            if params.range_width <= 0:
                logger.error("Invalid range width", width=params.range_width)
                return False
                
        elif isinstance(params, CurveBalancedParams):
            if params.curve_steepness <= 0:
                logger.error("Invalid curve steepness", steepness=params.curve_steepness)
                return False
                
        elif isinstance(params, BidAskBalancedParams):
            if params.bid_range <= 0 or params.ask_range <= 0:
                logger.error("Invalid bid/ask range", bid=params.bid_range, ask=params.ask_range)
                return False
                
        elif isinstance(params, SpotImbalancedParams):
            if params.direction not in ["long", "short"]:
                logger.error("Invalid direction", direction=params.direction)
                return False
        
        return True
        
    except Exception as e:
        logger.error("Strategy params validation failed", error=str(e))
        return False
