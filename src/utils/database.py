"""
DyFlow 数据库管理模块
提供数据持久化和状态管理功能
"""

import asyncio
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, Float, DateTime, JSON, Boolean, Integer, Text
from sqlalchemy.sql import func

from .exceptions import StateManagerException


class Base(DeclarativeBase):
    """数据库模型基类"""
    pass


class PoolMetricsRecord(Base):
    """池子指标记录表"""
    __tablename__ = "pool_metrics"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    pool_address: Mapped[str] = mapped_column(String(100), nullable=False)
    chain: Mapped[str] = mapped_column(String(20), nullable=False)
    token_a: Mapped[str] = mapped_column(String(20), nullable=False)
    token_b: Mapped[str] = mapped_column(String(20), nullable=False)
    apr: Mapped[float] = mapped_column(Float, nullable=False)
    il_risk: Mapped[float] = mapped_column(Float, nullable=False)
    tvl: Mapped[float] = mapped_column(Float, nullable=False)
    volume_24h: Mapped[float] = mapped_column(Float, nullable=False)
    fee_rate: Mapped[float] = mapped_column(Float, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    raw_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True)


class TransactionRecord(Base):
    """交易记录表"""
    __tablename__ = "transactions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    tx_hash: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    chain: Mapped[str] = mapped_column(String(20), nullable=False)
    action: Mapped[str] = mapped_column(String(20), nullable=False)  # enter, exit, rebalance
    pool_address: Mapped[str] = mapped_column(String(100), nullable=False)
    amount: Mapped[float] = mapped_column(Float, nullable=False)
    gas_used: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    gas_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    status: Mapped[str] = mapped_column(String(20), nullable=False)  # pending, confirmed, failed
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    confirmed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)


class StrategyDecisionRecord(Base):
    """策略决策记录表"""
    __tablename__ = "strategy_decisions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    action: Mapped[str] = mapped_column(String(20), nullable=False)
    source_chain: Mapped[str] = mapped_column(String(20), nullable=False)
    target_chain: Mapped[str] = mapped_column(String(20), nullable=False)
    source_pool: Mapped[str] = mapped_column(String(100), nullable=False)
    target_pool: Mapped[str] = mapped_column(String(100), nullable=False)
    amount: Mapped[float] = mapped_column(Float, nullable=False)
    confidence: Mapped[float] = mapped_column(Float, nullable=False)
    estimated_gas: Mapped[float] = mapped_column(Float, nullable=False)
    expected_return: Mapped[float] = mapped_column(Float, nullable=False)
    executed: Mapped[bool] = mapped_column(Boolean, default=False)
    execution_tx_hash: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=func.now())


class RiskAlertRecord(Base):
    """风险警报记录表"""
    __tablename__ = "risk_alerts"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    level: Mapped[str] = mapped_column(String(10), nullable=False)  # low, medium, high, critical
    message: Mapped[str] = mapped_column(Text, nullable=False)
    affected_pools: Mapped[List[str]] = mapped_column(JSON, nullable=False)
    recommended_action: Mapped[str] = mapped_column(String(50), nullable=False)
    resolved: Mapped[bool] = mapped_column(Boolean, default=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=func.now())


class AppStateRecord(Base):
    """应用状态记录表"""
    __tablename__ = "app_state"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    key: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    value: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())


class Database:
    """数据库管理器"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.async_session = None
        
    async def initialize(self):
        """初始化数据库连接"""
        try:
            self.engine = create_async_engine(
                self.database_url,
                echo=False,
                pool_timeout=30,
                pool_recycle=3600
            )
            
            self.async_session = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 创建所有表
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
                
        except Exception as e:
            raise StateManagerException(f"数据库初始化失败: {e}")
    
    async def close(self):
        """关闭数据库连接"""
        if self.engine:
            await self.engine.dispose()
    
    async def save_pool_metrics(self, metrics_data: Dict[str, Any]):
        """保存池子指标数据"""
        async with self.async_session() as session:
            try:
                record = PoolMetricsRecord(
                    pool_address=metrics_data["pool_address"],
                    chain=metrics_data["chain"],
                    token_a=metrics_data["token_a"],
                    token_b=metrics_data["token_b"],
                    apr=metrics_data["apr"],
                    il_risk=metrics_data["il_risk"],
                    tvl=metrics_data["tvl"],
                    volume_24h=metrics_data["volume_24h"],
                    fee_rate=metrics_data["fee_rate"],
                    timestamp=metrics_data.get("timestamp", datetime.now()),
                    raw_data=metrics_data.get("raw_data")
                )
                session.add(record)
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"保存池子指标失败: {e}")
    
    async def save_transaction(self, tx_data: Dict[str, Any]):
        """保存交易记录"""
        async with self.async_session() as session:
            try:
                record = TransactionRecord(
                    tx_hash=tx_data["tx_hash"],
                    chain=tx_data["chain"],
                    action=tx_data["action"],
                    pool_address=tx_data["pool_address"],
                    amount=tx_data["amount"],
                    gas_used=tx_data.get("gas_used"),
                    gas_price=tx_data.get("gas_price"),
                    status=tx_data["status"],
                    error_message=tx_data.get("error_message")
                )
                session.add(record)
                await session.commit()
                return record.id
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"保存交易记录失败: {e}")
    
    async def update_transaction_status(self, tx_hash: str, status: str, 
                                      gas_used: Optional[float] = None,
                                      error_message: Optional[str] = None):
        """更新交易状态"""
        async with self.async_session() as session:
            try:
                result = await session.get(TransactionRecord, {"tx_hash": tx_hash})
                if result:
                    result.status = status
                    if gas_used is not None:
                        result.gas_used = gas_used
                    if error_message:
                        result.error_message = error_message
                    if status == "confirmed":
                        result.confirmed_at = datetime.now()
                    await session.commit()
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"更新交易状态失败: {e}")
    
    async def save_strategy_decision(self, decision_data: Dict[str, Any]):
        """保存策略决策"""
        async with self.async_session() as session:
            try:
                record = StrategyDecisionRecord(
                    action=decision_data["action"],
                    source_chain=decision_data["source_chain"],
                    target_chain=decision_data["target_chain"],
                    source_pool=decision_data["source_pool"],
                    target_pool=decision_data["target_pool"],
                    amount=decision_data["amount"],
                    confidence=decision_data["confidence"],
                    estimated_gas=decision_data["estimated_gas"],
                    expected_return=decision_data["expected_return"]
                )
                session.add(record)
                await session.commit()
                return record.id
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"保存策略决策失败: {e}")
    
    async def save_risk_alert(self, alert_data: Dict[str, Any]):
        """保存风险警报"""
        async with self.async_session() as session:
            try:
                record = RiskAlertRecord(
                    level=alert_data["level"],
                    message=alert_data["message"],
                    affected_pools=alert_data["affected_pools"],
                    recommended_action=alert_data["recommended_action"]
                )
                session.add(record)
                await session.commit()
                return record.id
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"保存风险警报失败: {e}")
    
    async def get_latest_pool_metrics(self, pool_address: str, chain: str) -> Optional[Dict[str, Any]]:
        """获取最新的池子指标"""
        async with self.async_session() as session:
            try:
                from sqlalchemy import select, desc
                stmt = select(PoolMetricsRecord).where(
                    PoolMetricsRecord.pool_address == pool_address,
                    PoolMetricsRecord.chain == chain
                ).order_by(desc(PoolMetricsRecord.timestamp)).limit(1)
                
                result = await session.execute(stmt)
                record = result.scalar_one_or_none()
                
                if record:
                    return {
                        "pool_address": record.pool_address,
                        "chain": record.chain,
                        "token_a": record.token_a,
                        "token_b": record.token_b,
                        "apr": record.apr,
                        "il_risk": record.il_risk,
                        "tvl": record.tvl,
                        "volume_24h": record.volume_24h,
                        "fee_rate": record.fee_rate,
                        "timestamp": record.timestamp,
                        "raw_data": record.raw_data
                    }
                return None
            except Exception as e:
                raise StateManagerException(f"获取池子指标失败: {e}")
    
    async def save_app_state(self, key: str, value: Dict[str, Any]):
        """保存应用状态"""
        async with self.async_session() as session:
            try:
                from sqlalchemy import select
                stmt = select(AppStateRecord).where(AppStateRecord.key == key)
                result = await session.execute(stmt)
                record = result.scalar_one_or_none()
                
                if record:
                    record.value = value
                    record.updated_at = datetime.now()
                else:
                    record = AppStateRecord(key=key, value=value)
                    session.add(record)
                
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise StateManagerException(f"保存应用状态失败: {e}")
    
    async def get_app_state(self, key: str) -> Optional[Dict[str, Any]]:
        """获取应用状态"""
        async with self.async_session() as session:
            try:
                from sqlalchemy import select
                stmt = select(AppStateRecord).where(AppStateRecord.key == key)
                result = await session.execute(stmt)
                record = result.scalar_one_or_none()
                return record.value if record else None
            except Exception as e:
                raise StateManagerException(f"获取应用状态失败: {e}")