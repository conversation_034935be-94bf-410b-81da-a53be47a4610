"""
WalletProbeTool - 錢包探測工具
用於 Phase 3 的 MPC 簽名和 nonce 測試
"""

import asyncio
import structlog
from typing import Dict, Any, Optional
from datetime import datetime
import hashlib

logger = structlog.get_logger(__name__)

class WalletProbeTool:
    """
    錢包探測工具
    負責測試 MPC 簽名功能和 nonce 管理
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.test_timeout = config.get('test_timeout', 30)
        self.mpc_threshold = config.get('mpc_threshold', '2/3')
        
        # 測試錢包配置
        self.test_wallets = {
            'bsc': {
                'primary': '******************************************',
                'backup1': '******************************************',
                'backup2': '******************************************'
            },
            'solana': {
                'primary': 'So11111111111111111111111111111111111111112',
                'backup1': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                'backup2': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
            }
        }
        
        # nonce 追蹤
        self.nonce_state = {
            'bsc': {'current': 0, 'pending': []},
            'solana': {'current': 0, 'pending': []}
        }
    
    async def test_mpc_signing(self) -> Dict[str, Any]:
        """
        測試 MPC 簽名功能
        這是 Phase 3 的關鍵檢查點
        """
        logger.info("testing_mpc_signing", threshold=self.mpc_threshold)
        
        try:
            test_results = {
                'bsc': await self._test_chain_mpc_signing('bsc'),
                'solana': await self._test_chain_mpc_signing('solana')
            }
            
            # 檢查整體結果
            all_passed = all(result['success'] for result in test_results.values())
            
            overall_result = {
                'success': all_passed,
                'test_results': test_results,
                'mpc_threshold': self.mpc_threshold,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            if all_passed:
                logger.info("mpc_signing_test_passed")
            else:
                logger.error("mpc_signing_test_failed", results=test_results)
            
            return overall_result
            
        except Exception as e:
            logger.error("mpc_signing_test_error", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _test_chain_mpc_signing(self, chain: str) -> Dict[str, Any]:
        """測試特定鏈的 MPC 簽名"""
        try:
            logger.info("testing_chain_mpc", chain=chain)
            
            # 創建測試交易
            test_transaction = {
                'id': f'test_mpc_{chain}_{int(datetime.utcnow().timestamp())}',
                'type': 'mpc_test',
                'chain': chain,
                'amount': 0.001,  # 小額測試
                'to': self.test_wallets[chain]['backup1'],
                'data': 'MPC signing test',
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # 執行簽名測試
            signing_results = await self._perform_test_signing(test_transaction, chain)
            
            # 驗證簽名結果
            validation_result = await self._validate_signatures(signing_results, chain)
            
            return {
                'success': validation_result['valid'],
                'chain': chain,
                'signatures_collected': len(signing_results['signatures']),
                'threshold_met': validation_result['threshold_met'],
                'signers': signing_results['signers'],
                'test_transaction_id': test_transaction['id'],
                'details': validation_result
            }
            
        except Exception as e:
            logger.error("chain_mpc_test_failed", chain=chain, error=str(e))
            return {
                'success': False,
                'chain': chain,
                'error': str(e)
            }
    
    async def _perform_test_signing(self, transaction: Dict[str, Any], chain: str) -> Dict[str, Any]:
        """執行測試簽名"""
        try:
            wallets = self.test_wallets[chain]
            signatures = []
            signers = []
            
            # 並發測試所有錢包簽名
            signing_tasks = []
            for wallet_name, wallet_address in wallets.items():
                task = asyncio.create_task(
                    self._test_wallet_signing(transaction, wallet_name, wallet_address, chain)
                )
                signing_tasks.append((wallet_name, task))
            
            # 等待所有簽名完成
            for wallet_name, task in signing_tasks:
                try:
                    signature = await asyncio.wait_for(task, timeout=10)
                    if signature:
                        signatures.append(signature)
                        signers.append(wallet_name)
                        logger.info("wallet_signature_success", wallet=wallet_name, chain=chain)
                    else:
                        logger.warning("wallet_signature_failed", wallet=wallet_name, chain=chain)
                except asyncio.TimeoutError:
                    logger.error("wallet_signature_timeout", wallet=wallet_name, chain=chain)
                except Exception as e:
                    logger.error("wallet_signature_error", wallet=wallet_name, chain=chain, error=str(e))
            
            return {
                'signatures': signatures,
                'signers': signers,
                'total_wallets': len(wallets),
                'successful_signatures': len(signatures)
            }
            
        except Exception as e:
            logger.error("test_signing_failed", chain=chain, error=str(e))
            return {
                'signatures': [],
                'signers': [],
                'error': str(e)
            }
    
    async def _test_wallet_signing(self, transaction: Dict[str, Any], wallet_name: str, wallet_address: str, chain: str) -> Optional[str]:
        """測試單個錢包簽名"""
        try:
            # 模擬簽名過程
            tx_data = f"{transaction['id']}:{wallet_address}:{chain}"
            signature = hashlib.sha256(tx_data.encode()).hexdigest()
            
            # 模擬簽名延遲
            await asyncio.sleep(0.1)
            
            return signature
            
        except Exception as e:
            logger.error("wallet_signing_test_failed", 
                        wallet=wallet_name, 
                        chain=chain, 
                        error=str(e))
            return None
    
    async def _validate_signatures(self, signing_results: Dict[str, Any], chain: str) -> Dict[str, Any]:
        """驗證簽名結果"""
        try:
            signatures = signing_results['signatures']
            signers = signing_results['signers']
            total_wallets = signing_results['total_wallets']
            
            # 解析 MPC 閾值
            threshold_parts = self.mpc_threshold.split('/')
            required_signatures = int(threshold_parts[0])
            total_possible = int(threshold_parts[1])
            
            # 檢查是否滿足閾值
            threshold_met = len(signatures) >= required_signatures
            
            # 驗證簽名格式
            valid_signatures = []
            for i, signature in enumerate(signatures):
                if self._is_valid_signature_format(signature):
                    valid_signatures.append(signature)
                else:
                    logger.warning("invalid_signature_format", 
                                 signer=signers[i] if i < len(signers) else 'unknown',
                                 signature_preview=signature[:16])
            
            validation_result = {
                'valid': threshold_met and len(valid_signatures) >= required_signatures,
                'threshold_met': threshold_met,
                'required_signatures': required_signatures,
                'collected_signatures': len(signatures),
                'valid_signatures': len(valid_signatures),
                'total_wallets': total_wallets,
                'success_rate': len(signatures) / total_wallets if total_wallets > 0 else 0
            }
            
            logger.info("signature_validation_completed", 
                       chain=chain,
                       **validation_result)
            
            return validation_result
            
        except Exception as e:
            logger.error("signature_validation_failed", chain=chain, error=str(e))
            return {
                'valid': False,
                'error': str(e)
            }
    
    def _is_valid_signature_format(self, signature: str) -> bool:
        """檢查簽名格式是否有效"""
        try:
            # 檢查是否為有效的十六進制字符串
            if not signature or len(signature) != 64:
                return False
            
            # 嘗試解析為十六進制
            int(signature, 16)
            return True
            
        except ValueError:
            return False
    
    async def test_nonce_management(self) -> Dict[str, Any]:
        """
        測試 nonce 管理功能
        確保交易順序正確
        """
        logger.info("testing_nonce_management")
        
        try:
            test_results = {}
            
            for chain in ['bsc', 'solana']:
                chain_result = await self._test_chain_nonce(chain)
                test_results[chain] = chain_result
            
            # 檢查整體結果
            all_passed = all(result['success'] for result in test_results.values())
            
            return {
                'success': all_passed,
                'test_results': test_results,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("nonce_management_test_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _test_chain_nonce(self, chain: str) -> Dict[str, Any]:
        """測試特定鏈的 nonce 管理"""
        try:
            # 獲取當前 nonce
            current_nonce = await self._get_current_nonce(chain)
            
            # 模擬多個並發交易
            test_transactions = []
            for i in range(3):
                tx = {
                    'id': f'nonce_test_{chain}_{i}',
                    'chain': chain,
                    'nonce': current_nonce + i
                }
                test_transactions.append(tx)
            
            # 測試 nonce 分配
            nonce_results = []
            for tx in test_transactions:
                nonce_result = await self._allocate_nonce(tx, chain)
                nonce_results.append(nonce_result)
            
            # 驗證 nonce 順序
            nonces = [result['allocated_nonce'] for result in nonce_results if result['success']]
            nonces_valid = self._validate_nonce_sequence(nonces)
            
            return {
                'success': nonces_valid,
                'chain': chain,
                'current_nonce': current_nonce,
                'test_transactions': len(test_transactions),
                'successful_allocations': len([r for r in nonce_results if r['success']]),
                'nonce_sequence_valid': nonces_valid,
                'allocated_nonces': nonces
            }
            
        except Exception as e:
            logger.error("chain_nonce_test_failed", chain=chain, error=str(e))
            return {
                'success': False,
                'chain': chain,
                'error': str(e)
            }
    
    async def _get_current_nonce(self, chain: str) -> int:
        """獲取當前 nonce"""
        try:
            # 模擬從區塊鏈獲取 nonce
            current_nonce = self.nonce_state[chain]['current']
            logger.info("current_nonce_retrieved", chain=chain, nonce=current_nonce)
            return current_nonce
            
        except Exception as e:
            logger.error("nonce_retrieval_failed", chain=chain, error=str(e))
            return 0
    
    async def _allocate_nonce(self, transaction: Dict[str, Any], chain: str) -> Dict[str, Any]:
        """分配 nonce"""
        try:
            # 模擬 nonce 分配邏輯
            allocated_nonce = self.nonce_state[chain]['current']
            self.nonce_state[chain]['current'] += 1
            self.nonce_state[chain]['pending'].append(allocated_nonce)
            
            return {
                'success': True,
                'transaction_id': transaction['id'],
                'allocated_nonce': allocated_nonce
            }
            
        except Exception as e:
            logger.error("nonce_allocation_failed", 
                        transaction_id=transaction.get('id'),
                        chain=chain,
                        error=str(e))
            return {
                'success': False,
                'transaction_id': transaction.get('id'),
                'error': str(e)
            }
    
    def _validate_nonce_sequence(self, nonces: List[int]) -> bool:
        """驗證 nonce 序列是否正確"""
        try:
            if not nonces:
                return True
            
            # 檢查是否為連續序列
            sorted_nonces = sorted(nonces)
            for i in range(1, len(sorted_nonces)):
                if sorted_nonces[i] != sorted_nonces[i-1] + 1:
                    logger.warning("nonce_sequence_gap_detected", 
                                 expected=sorted_nonces[i-1] + 1,
                                 actual=sorted_nonces[i])
                    return False
            
            return True
            
        except Exception as e:
            logger.error("nonce_sequence_validation_failed", error=str(e))
            return False
    
    def get_wallet_status(self) -> Dict[str, Any]:
        """獲取錢包狀態"""
        return {
            'test_wallets': self.test_wallets,
            'nonce_state': self.nonce_state,
            'mpc_threshold': self.mpc_threshold,
            'test_timeout': self.test_timeout
        }
