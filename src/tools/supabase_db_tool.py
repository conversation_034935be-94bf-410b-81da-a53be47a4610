"""
SupabaseDbTool - Supabase数据库操作工具
支持Supabase数据读写操作，提供统一数据访问接口
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional, Literal, Union
from datetime import datetime
import structlog

try:
    from agno.sdk import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 如果Agno不可用，创建一个基础的Tool类作为fallback
    class Tool:
        name = ""
        description = ""
        
        async def run(self, *args, **kwargs):
            raise NotImplementedError("Agno Framework not available")

logger = structlog.get_logger(__name__)


class SupabaseDbTool(Tool):
    """Supabase数据库操作工具"""
    
    name = "supabase_db"
    description = "Supabase数据库操作工具，支持数据的增删改查操作"
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        
        # 数据表映射
        self.table_mapping = {
            'pools': 'pool_metrics',
            'transactions': 'transactions',
            'decisions': 'strategy_decisions',
            'alerts': 'risk_alerts',
            'state': 'app_state',
            'positions': 'positions',
            'hedges': 'hedges',
            'earnings': 'earnings'
        }
        
        # 字段映射和验证规则
        self.table_schemas = {
            'pool_metrics': {
                'required_fields': ['pool_address', 'chain', 'token_a', 'token_b', 'apr', 'tvl'],
                'optional_fields': ['il_risk', 'volume_24h', 'fee_rate', 'timestamp', 'raw_data'],
                'id_field': 'id'
            },
            'transactions': {
                'required_fields': ['tx_hash', 'chain', 'action', 'pool_address', 'amount', 'status'],
                'optional_fields': ['gas_used', 'gas_price', 'error_message', 'created_at', 'confirmed_at'],
                'id_field': 'id'
            },
            'strategy_decisions': {
                'required_fields': ['action', 'source_chain', 'target_chain', 'source_pool', 'target_pool', 'amount', 'confidence'],
                'optional_fields': ['estimated_gas', 'expected_return', 'executed', 'execution_tx_hash', 'timestamp'],
                'id_field': 'id'
            },
            'risk_alerts': {
                'required_fields': ['level', 'message', 'affected_pools', 'recommended_action'],
                'optional_fields': ['resolved', 'timestamp'],
                'id_field': 'id'
            },
            'app_state': {
                'required_fields': ['key', 'value'],
                'optional_fields': ['updated_at'],
                'id_field': 'id'
            }
        }
    
    async def run(self, operation: str, table: str, data: Dict = None, filters: Dict = None) -> Dict[str, Any]:
        """
        执行数据库操作
        
        Args:
            operation: 操作类型 ('insert', 'update', 'select', 'delete', 'upsert')
            table: 目标表名 (支持简化名称或完整表名)
            data: 操作数据 (insert/update/upsert时必需)
            filters: 查询过滤条件 (select/update/delete时使用)
                - 格式: {"field": "value"} 或 {"field": {"op": "eq", "value": "value"}}
                - 支持的操作符: eq, neq, gt, gte, lt, lte, like, ilike, in, is
                
        Returns:
            Dict包含：
                - result: 操作结果数据
                - affected_rows: 影响行数
                - operation_info: 操作信息
        """
        try:
            if not AGNO_AVAILABLE:
                return {
                    "error": "Agno Framework not available",
                    "result": [],
                    "affected_rows": 0
                }
            
            # 验证配置
            if not self.supabase_url or not self.supabase_key:
                return {
                    "error": "缺少Supabase配置信息",
                    "result": [],
                    "affected_rows": 0
                }
            
            # 验证参数
            validation_result = self._validate_operation_params(operation, table, data, filters)
            if not validation_result['valid']:
                return {
                    "error": f"参数验证失败: {validation_result['reason']}",
                    "result": [],
                    "affected_rows": 0
                }
            
            # 获取实际表名
            actual_table = self.table_mapping.get(table, table)
            
            logger.info("supabase_operation_started",
                       operation=operation,
                       table=actual_table,
                       has_data=data is not None,
                       has_filters=filters is not None)
            
            # 根据操作类型执行相应方法
            if operation == 'select':
                result = await self._select_data(actual_table, filters)
            elif operation == 'insert':
                result = await self._insert_data(actual_table, data)
            elif operation == 'update':
                result = await self._update_data(actual_table, data, filters)
            elif operation == 'delete':
                result = await self._delete_data(actual_table, filters)
            elif operation == 'upsert':
                result = await self._upsert_data(actual_table, data)
            else:
                return {
                    "error": f"不支持的操作类型: {operation}",
                    "result": [],
                    "affected_rows": 0
                }
            
            logger.info("supabase_operation_completed",
                       operation=operation,
                       table=actual_table,
                       success=result.get('success', False),
                       affected_rows=result.get('affected_rows', 0))
            
            return result
            
        except Exception as e:
            logger.error("supabase_db_tool_failed", 
                        operation=operation, 
                        table=table, 
                        error=str(e))
            return {
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    def _validate_operation_params(self, operation: str, table: str, 
                                 data: Optional[Dict], filters: Optional[Dict]) -> Dict[str, Any]:
        """验证操作参数"""
        valid_operations = ['select', 'insert', 'update', 'delete', 'upsert']
        
        if operation not in valid_operations:
            return {'valid': False, 'reason': f'不支持的操作: {operation}，支持的操作: {", ".join(valid_operations)}'}
        
        if not table:
            return {'valid': False, 'reason': '表名不能为空'}
        
        # 获取实际表名和schema
        actual_table = self.table_mapping.get(table, table)
        table_schema = self.table_schemas.get(actual_table)
        
        # 验证需要data的操作
        if operation in ['insert', 'update', 'upsert']:
            if not data:
                return {'valid': False, 'reason': f'{operation}操作需要提供data参数'}
            
            if table_schema:
                # 验证必需字段
                if operation == 'insert':
                    missing_fields = []
                    for field in table_schema['required_fields']:
                        if field not in data:
                            missing_fields.append(field)
                    if missing_fields:
                        return {'valid': False, 'reason': f'缺少必需字段: {", ".join(missing_fields)}'}
        
        # 验证需要filters的操作
        if operation in ['update', 'delete']:
            if not filters:
                return {'valid': False, 'reason': f'{operation}操作需要提供filters参数以避免误操作'}
        
        return {'valid': True, 'reason': 'validation_passed'}
    
    async def _select_data(self, table: str, filters: Optional[Dict]) -> Dict[str, Any]:
        """查询数据"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json"
            }
            
            params = {}
            
            # 构建查询参数
            if filters:
                for field, condition in filters.items():
                    if isinstance(condition, dict):
                        # 复杂条件 {"op": "eq", "value": "value"}
                        op = condition.get('op', 'eq')
                        value = condition.get('value')
                        if op == 'eq':
                            params[field] = f"eq.{value}"
                        elif op == 'neq':
                            params[field] = f"neq.{value}"
                        elif op == 'gt':
                            params[field] = f"gt.{value}"
                        elif op == 'gte':
                            params[field] = f"gte.{value}"
                        elif op == 'lt':
                            params[field] = f"lt.{value}"
                        elif op == 'lte':
                            params[field] = f"lte.{value}"
                        elif op == 'like':
                            params[field] = f"like.{value}"
                        elif op == 'ilike':
                            params[field] = f"ilike.{value}"
                        elif op == 'in':
                            if isinstance(value, list):
                                params[field] = f"in.({','.join(map(str, value))})"
                            else:
                                params[field] = f"in.({value})"
                        elif op == 'is':
                            params[field] = f"is.{value}"
                    else:
                        # 简单条件，默认为等于
                        params[field] = f"eq.{condition}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params,
                                     timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "result": data,
                            "affected_rows": len(data),
                            "operation_info": {
                                "operation": "select",
                                "table": table,
                                "filters": filters,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"查询失败: {response.status} - {error_text}",
                            "result": [],
                            "affected_rows": 0
                        }
                        
        except Exception as e:
            logger.error("select_data_failed", table=table, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    async def _insert_data(self, table: str, data: Dict) -> Dict[str, Any]:
        """插入数据"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"
            }
            
            # 处理时间戳字段
            processed_data = self._process_timestamps(data)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, 
                                      json=processed_data,
                                      timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 201:
                        result_data = await response.json()
                        return {
                            "success": True,
                            "result": result_data,
                            "affected_rows": len(result_data) if isinstance(result_data, list) else 1,
                            "operation_info": {
                                "operation": "insert",
                                "table": table,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"插入失败: {response.status} - {error_text}",
                            "result": [],
                            "affected_rows": 0
                        }
                        
        except Exception as e:
            logger.error("insert_data_failed", table=table, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    async def _update_data(self, table: str, data: Dict, filters: Dict) -> Dict[str, Any]:
        """更新数据"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"
            }
            
            # 构建查询参数
            params = {}
            for field, condition in filters.items():
                if isinstance(condition, dict):
                    op = condition.get('op', 'eq')
                    value = condition.get('value')
                    params[field] = f"{op}.{value}"
                else:
                    params[field] = f"eq.{condition}"
            
            # 处理时间戳字段
            processed_data = self._process_timestamps(data)
            
            async with aiohttp.ClientSession() as session:
                async with session.patch(url, headers=headers, params=params,
                                       json=processed_data,
                                       timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        result_data = await response.json()
                        return {
                            "success": True,
                            "result": result_data,
                            "affected_rows": len(result_data) if isinstance(result_data, list) else 1,
                            "operation_info": {
                                "operation": "update",
                                "table": table,
                                "filters": filters,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"更新失败: {response.status} - {error_text}",
                            "result": [],
                            "affected_rows": 0
                        }
                        
        except Exception as e:
            logger.error("update_data_failed", table=table, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    async def _delete_data(self, table: str, filters: Dict) -> Dict[str, Any]:
        """删除数据"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"
            }
            
            # 构建查询参数
            params = {}
            for field, condition in filters.items():
                if isinstance(condition, dict):
                    op = condition.get('op', 'eq')
                    value = condition.get('value')
                    params[field] = f"{op}.{value}"
                else:
                    params[field] = f"eq.{condition}"
            
            async with aiohttp.ClientSession() as session:
                async with session.delete(url, headers=headers, params=params,
                                        timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        result_data = await response.json()
                        return {
                            "success": True,
                            "result": result_data,
                            "affected_rows": len(result_data) if isinstance(result_data, list) else 1,
                            "operation_info": {
                                "operation": "delete",
                                "table": table,
                                "filters": filters,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"删除失败: {response.status} - {error_text}",
                            "result": [],
                            "affected_rows": 0
                        }
                        
        except Exception as e:
            logger.error("delete_data_failed", table=table, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    async def _upsert_data(self, table: str, data: Dict) -> Dict[str, Any]:
        """插入或更新数据"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation,resolution=merge-duplicates"
            }
            
            # 处理时间戳字段
            processed_data = self._process_timestamps(data)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, 
                                      json=processed_data,
                                      timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status in [200, 201]:
                        result_data = await response.json()
                        return {
                            "success": True,
                            "result": result_data,
                            "affected_rows": len(result_data) if isinstance(result_data, list) else 1,
                            "operation_info": {
                                "operation": "upsert",
                                "table": table,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"Upsert失败: {response.status} - {error_text}",
                            "result": [],
                            "affected_rows": 0
                        }
                        
        except Exception as e:
            logger.error("upsert_data_failed", table=table, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    def _process_timestamps(self, data: Dict) -> Dict[str, Any]:
        """处理时间戳字段"""
        processed_data = data.copy()
        
        # 自动添加或更新时间戳
        timestamp_fields = ['timestamp', 'created_at', 'updated_at']
        current_time = datetime.utcnow().isoformat()
        
        for field in timestamp_fields:
            if field in processed_data:
                # 如果字段存在但值为None或空字符串，设置为当前时间
                if not processed_data[field]:
                    processed_data[field] = current_time
                # 如果是datetime对象，转换为ISO格式字符串
                elif isinstance(processed_data[field], datetime):
                    processed_data[field] = processed_data[field].isoformat()
        
        return processed_data
    
    # 便捷方法
    async def save_pool_metrics(self, pool_data: Dict) -> Dict[str, Any]:
        """保存池子指标数据的便捷方法"""
        return await self.run('insert', 'pools', pool_data)
    
    async def get_pool_metrics(self, filters: Dict = None) -> Dict[str, Any]:
        """获取池子指标数据的便捷方法"""
        return await self.run('select', 'pools', filters=filters)
    
    async def save_transaction(self, tx_data: Dict) -> Dict[str, Any]:
        """保存交易记录的便捷方法"""
        return await self.run('insert', 'transactions', tx_data)
    
    async def update_transaction_status(self, tx_hash: str, status: str, 
                                      additional_data: Dict = None) -> Dict[str, Any]:
        """更新交易状态的便捷方法"""
        update_data = {'status': status}
        if additional_data:
            update_data.update(additional_data)
        
        filters = {'tx_hash': tx_hash}
        return await self.run('update', 'transactions', update_data, filters)
    
    async def save_risk_alert(self, alert_data: Dict) -> Dict[str, Any]:
        """保存风险警报的便捷方法"""
        return await self.run('insert', 'alerts', alert_data)
    
    async def get_active_alerts(self) -> Dict[str, Any]:
        """获取未解决的风险警报"""
        filters = {'resolved': False}
        return await self.run('select', 'alerts', filters=filters)
    
    async def save_app_state(self, key: str, value: Dict) -> Dict[str, Any]:
        """保存应用状态的便捷方法"""
        state_data = {'key': key, 'value': value}
        return await self.run('upsert', 'state', state_data)
    
    async def get_app_state(self, key: str) -> Dict[str, Any]:
        """获取应用状态的便捷方法"""
        filters = {'key': key}
        return await self.run('select', 'state', filters=filters)


# 为了向后兼容，如果Agno不可用，提供同步版本
class SupabaseDbToolSync:
    """SupabaseDbTool的同步版本，用于非Agno环境"""
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        self.tool = SupabaseDbTool(supabase_url, supabase_key)
    
    def execute_operation(self, operation: str, table: str, 
                         data: Dict = None, filters: Dict = None) -> Dict[str, Any]:
        """同步版本的数据库操作"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.tool.run(operation, table, data, filters))
            finally:
                loop.close()
        except Exception as e:
            logger.error("sync_supabase_db_failed", error=str(e))
            return {
                "error": str(e),
                "result": [],
                "affected_rows": 0
            }
    
    # 同步版本的便捷方法
    def save_pool_metrics(self, pool_data: Dict) -> Dict[str, Any]:
        return self.execute_operation('insert', 'pools', pool_data)
    
    def get_pool_metrics(self, filters: Dict = None) -> Dict[str, Any]:
        return self.execute_operation('select', 'pools', filters=filters)
    
    def save_transaction(self, tx_data: Dict) -> Dict[str, Any]:
        return self.execute_operation('insert', 'transactions', tx_data)
    
    def update_transaction_status(self, tx_hash: str, status: str, 
                                additional_data: Dict = None) -> Dict[str, Any]:
        update_data = {'status': status}
        if additional_data:
            update_data.update(additional_data)
        filters = {'tx_hash': tx_hash}
        return self.execute_operation('update', 'transactions', update_data, filters)


# 导出标准接口
__all__ = ['SupabaseDbTool', 'SupabaseDbToolSync', 'AGNO_AVAILABLE']