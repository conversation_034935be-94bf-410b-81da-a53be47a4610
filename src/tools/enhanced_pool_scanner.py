"""
Enhanced Pool Scanner Tool
真實的池子掃描工具，連接 BSC PancakeSwap v3 和 Solana Meteora DLMM v2
"""

import asyncio
import aiohttp
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

logger = structlog.get_logger(__name__)

class EnhancedPoolScanner:
    """增強的池子掃描工具"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # API 配置
        self.pancake_subgraph_url = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
        self.pancake_api_key = "9731921233db132a98c2325878e6c153"
        self.meteora_api_url = "https://dammv2-api.meteora.ag"
        
        # 過濾條件
        self.filters = {
            'bsc': {
                'min_tvl': 10_000_000,  # $10M
                'max_created_days': 2,
                'min_fee_tvl_ratio': 0.05,  # 5%
                'min_fees_24h': 5
            },
            'solana': {
                'min_tvl': 10_000_000,  # $10M
                'max_created_days': 2,
                'min_fee_tvl_ratio': 0.05,  # 5%
                'min_fees_24h': 5
            }
        }
        
        # 緩存
        self.last_scan_time = None
        self.cached_pools = {'bsc': [], 'solana': []}
    
    async def scan_all_pools(self) -> Dict[str, List[Dict[str, Any]]]:
        """掃描所有鏈的池子"""
        logger.info("enhanced_pool_scan_started")
        
        try:
            # 並發掃描 BSC 和 Solana
            tasks = [
                self.scan_bsc_pools(),
                self.scan_solana_pools()
            ]
            
            bsc_pools, solana_pools = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 處理異常
            if isinstance(bsc_pools, Exception):
                logger.error("bsc_scan_failed", error=str(bsc_pools))
                bsc_pools = []
            
            if isinstance(solana_pools, Exception):
                logger.error("solana_scan_failed", error=str(solana_pools))
                solana_pools = []
            
            # 更新緩存
            self.cached_pools = {
                'bsc': bsc_pools,
                'solana': solana_pools
            }
            self.last_scan_time = datetime.now()
            
            total_pools = len(bsc_pools) + len(solana_pools)
            logger.info("enhanced_pool_scan_completed",
                       bsc_pools=len(bsc_pools),
                       solana_pools=len(solana_pools),
                       total_pools=total_pools)
            
            return self.cached_pools
            
        except Exception as e:
            logger.error("enhanced_pool_scan_failed", error=str(e))
            return {'bsc': [], 'solana': []}
    
    async def scan_bsc_pools(self) -> List[Dict[str, Any]]:
        """掃描 BSC PancakeSwap v3 池子"""
        logger.info("scanning_bsc_pools")
        
        try:
            # GraphQL 查詢
            query = """
            query GetPools($first: Int!, $skip: Int!) {
              pools(
                first: $first
                skip: $skip
                orderBy: totalValueLockedUSD
                orderDirection: desc
                where: {
                  totalValueLockedUSD_gte: "10000000"
                  createdAtTimestamp_gte: "%s"
                }
              ) {
                id
                token0 {
                  id
                  symbol
                  name
                }
                token1 {
                  id
                  symbol
                  name
                }
                feeTier
                totalValueLockedUSD
                volumeUSD
                feesUSD
                createdAtTimestamp
                poolDayData(first: 1, orderBy: date, orderDirection: desc) {
                  volumeUSD
                  feesUSD
                  tvlUSD
                }
              }
            }
            """ % int((datetime.now() - timedelta(days=2)).timestamp())
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.pancake_api_key}'
                }
                
                payload = {
                    'query': query,
                    'variables': {
                        'first': 100,
                        'skip': 0
                    }
                }
                
                async with session.post(
                    self.pancake_subgraph_url,
                    json=payload,
                    headers=headers,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data.get('data', {}).get('pools', [])
                        
                        # 處理和過濾池子
                        filtered_pools = []
                        for pool in pools:
                            processed_pool = self._process_bsc_pool(pool)
                            if processed_pool and self._filter_bsc_pool(processed_pool):
                                filtered_pools.append(processed_pool)
                        
                        logger.info("bsc_pools_scanned", 
                                   total=len(pools),
                                   filtered=len(filtered_pools))
                        return filtered_pools
                    else:
                        logger.error("bsc_api_error", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("bsc_scan_failed", error=str(e))
            return []
    
    async def scan_solana_pools(self) -> List[Dict[str, Any]]:
        """掃描 Solana Meteora DLMM v2 池子"""
        logger.info("scanning_solana_pools")
        
        try:
            async with aiohttp.ClientSession() as session:
                # 獲取所有 DLMM 池子
                async with session.get(
                    f"{self.meteora_api_url}/pair/all",
                    timeout=30
                ) as response:
                    if response.status == 200:
                        pools = await response.json()
                        
                        # 處理和過濾池子
                        filtered_pools = []
                        for pool in pools:
                            processed_pool = self._process_solana_pool(pool)
                            if processed_pool and self._filter_solana_pool(processed_pool):
                                filtered_pools.append(processed_pool)
                        
                        logger.info("solana_pools_scanned",
                                   total=len(pools),
                                   filtered=len(filtered_pools))
                        return filtered_pools
                    else:
                        logger.error("solana_api_error", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("solana_scan_failed", error=str(e))
            return []
    
    def _process_bsc_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 BSC 池子數據"""
        try:
            tvl_usd = float(pool.get('totalValueLockedUSD', 0))
            volume_24h = float(pool.get('volumeUSD', 0))
            fees_24h = float(pool.get('feesUSD', 0))
            
            # 計算 APR
            apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
            
            # 計算 Fee/TVL 比率
            fee_tvl_ratio = (fees_24h / tvl_usd) if tvl_usd > 0 else 0
            
            return {
                'id': pool['id'],
                'chain': 'bsc',
                'protocol': 'pancakeswap_v3',
                'token0': pool['token0']['symbol'],
                'token1': pool['token1']['symbol'],
                'pair': f"{pool['token0']['symbol']}/{pool['token1']['symbol']}",
                'fee_tier': int(pool.get('feeTier', 0)),
                'tvl_usd': tvl_usd,
                'volume_24h_usd': volume_24h,
                'fees_24h_usd': fees_24h,
                'apr': apr,
                'fee_tvl_ratio': fee_tvl_ratio,
                'created_at': int(pool.get('createdAtTimestamp', 0)),
                'url': f"https://pancakeswap.finance/v3/pools/{pool['id']}",
                'scan_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("bsc_pool_processing_failed", pool_id=pool.get('id'), error=str(e))
            return None
    
    def _process_solana_pool(self, pool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """處理 Solana 池子數據"""
        try:
            tvl_usd = float(pool.get('liquidity', 0))
            volume_24h = float(pool.get('volume_24h', 0))
            fees_24h = float(pool.get('fees_24h', 0))
            
            # 計算 APR
            apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
            
            # 計算 Fee/TVL 比率
            fee_tvl_ratio = (fees_24h / tvl_usd) if tvl_usd > 0 else 0
            
            return {
                'id': pool['address'],
                'chain': 'solana',
                'protocol': 'meteora_dlmm_v2',
                'token0': pool.get('mint_x', {}).get('symbol', 'Unknown'),
                'token1': pool.get('mint_y', {}).get('symbol', 'Unknown'),
                'pair': f"{pool.get('mint_x', {}).get('symbol', 'Unknown')}/{pool.get('mint_y', {}).get('symbol', 'Unknown')}",
                'bin_step': pool.get('bin_step', 0),
                'tvl_usd': tvl_usd,
                'volume_24h_usd': volume_24h,
                'fees_24h_usd': fees_24h,
                'apr': apr,
                'fee_tvl_ratio': fee_tvl_ratio,
                'created_at': pool.get('created_at', 0),
                'url': f"https://app.meteora.ag/dlmm/{pool['address']}",
                'scan_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("solana_pool_processing_failed", pool_id=pool.get('address'), error=str(e))
            return None
    
    def _filter_bsc_pool(self, pool: Dict[str, Any]) -> bool:
        """過濾 BSC 池子"""
        filters = self.filters['bsc']
        
        # TVL 檢查
        if pool['tvl_usd'] < filters['min_tvl']:
            return False
        
        # 創建時間檢查
        created_days_ago = (datetime.now().timestamp() - pool['created_at']) / 86400
        if created_days_ago > filters['max_created_days']:
            return False
        
        # Fee/TVL 比率檢查
        if pool['fee_tvl_ratio'] < filters['min_fee_tvl_ratio']:
            return False
        
        # 24h 手續費檢查
        if pool['fees_24h_usd'] < filters['min_fees_24h']:
            return False
        
        return True
    
    def _filter_solana_pool(self, pool: Dict[str, Any]) -> bool:
        """過濾 Solana 池子"""
        filters = self.filters['solana']
        
        # TVL 檢查
        if pool['tvl_usd'] < filters['min_tvl']:
            return False
        
        # 創建時間檢查 (如果有的話)
        if pool['created_at'] > 0:
            created_days_ago = (datetime.now().timestamp() - pool['created_at']) / 86400
            if created_days_ago > filters['max_created_days']:
                return False
        
        # Fee/TVL 比率檢查
        if pool['fee_tvl_ratio'] < filters['min_fee_tvl_ratio']:
            return False
        
        # 24h 手續費檢查
        if pool['fees_24h_usd'] < filters['min_fees_24h']:
            return False
        
        return True
    
    def get_cached_pools(self) -> Dict[str, List[Dict[str, Any]]]:
        """獲取緩存的池子數據"""
        return self.cached_pools
    
    def get_scan_stats(self) -> Dict[str, Any]:
        """獲取掃描統計"""
        return {
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'bsc_pools_count': len(self.cached_pools['bsc']),
            'solana_pools_count': len(self.cached_pools['solana']),
            'total_pools_count': len(self.cached_pools['bsc']) + len(self.cached_pools['solana']),
            'filters': self.filters
        }
