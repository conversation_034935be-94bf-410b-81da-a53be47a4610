"""
BinanceHedgeTool - Binance对冲操作工具
支持Delta-Neutral策略的风险对冲，集成Binance API操作
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
from typing import Dict, Any, Optional, Literal, List
from datetime import datetime
import structlog
import json

try:
    from agno.sdk import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 如果Agno不可用，创建一个基础的Tool类作为fallback
    class Tool:
        name = ""
        description = ""
        
        async def run(self, *args, **kwargs):
            raise NotImplementedError("Agno Framework not available")

logger = structlog.get_logger(__name__)


class BinanceHedgeTool(Tool):
    """Binance对冲操作工具，支持Delta-Neutral策略"""
    
    name = "binance_hedge"
    description = "Binance对冲操作工具，支持Delta-Neutral策略的风险对冲"
    
    def __init__(self, api_key: str = None, api_secret: str = None, testnet: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # API端点配置
        if testnet:
            self.base_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://fapi.binance.com"
        
        # 支持的对冲交易对
        self.supported_symbols = {
            'BTC': 'BTCUSDT',
            'ETH': 'ETHUSDT', 
            'BNB': 'BNBUSDT',
            'SOL': 'SOLUSDT',
            'MATIC': 'MATICUSDT',
            'AVAX': 'AVAXUSDT',
            'DOT': 'DOTUSDT',
            'LINK': 'LINKUSDT'
        }
        
        # 风险管理参数
        self.risk_params = {
            'max_leverage': 10,  # 最大杠杆倍数
            'max_position_size': 0.1,  # 最大仓位占总资金比例
            'stop_loss_pct': 0.05,  # 止损百分比 5%
            'take_profit_pct': 0.02,  # 止盈百分比 2%
            'min_order_size': 10,  # 最小订单金额 $10
            'max_slippage': 0.005  # 最大滑点 0.5%
        }
    
    async def run(self, hedge_params: Dict) -> Dict[str, Any]:
        """
        执行Binance对冲操作
        
        Args:
            hedge_params: 对冲参数字典，包含：
                - symbol: 交易对符号 (如 'BTC', 'ETH')
                - side: 'BUY' 或 'SELL'
                - quantity: 数量 (USD价值)
                - hedge_type: 'delta_neutral', 'long_hedge', 'short_hedge'
                - leverage: 杠杆倍数 (可选，默认为1)
                - stop_loss: 止损价格 (可选)
                - take_profit: 止盈价格 (可选)
                - time_limit: 时间限制秒数 (可选)
                
        Returns:
            Dict包含：
                - hedge_result: 对冲执行结果
                - risk_metrics: 风险指标更新
        """
        try:
            if not AGNO_AVAILABLE:
                return {
                    "error": "Agno Framework not available",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            # 验证API配置
            if not self.api_key or not self.api_secret:
                return {
                    "error": "缺少Binance API密钥配置",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            # 验证参数
            validation_result = self._validate_hedge_params(hedge_params)
            if not validation_result['valid']:
                return {
                    "error": f"参数验证失败: {validation_result['reason']}",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            logger.info("binance_hedge_started", params=hedge_params)
            
            # 获取账户信息和当前仓位
            account_info = await self._get_account_info()
            if not account_info['success']:
                return {
                    "error": f"获取账户信息失败: {account_info['error']}",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            # 获取市场价格
            market_price = await self._get_market_price(hedge_params['symbol'])
            if not market_price['success']:
                return {
                    "error": f"获取市场价格失败: {market_price['error']}",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            # 计算对冲参数
            hedge_calculation = await self._calculate_hedge_params(
                hedge_params, account_info['data'], market_price['data']
            )
            
            if not hedge_calculation['valid']:
                return {
                    "error": f"对冲计算失败: {hedge_calculation['reason']}",
                    "hedge_result": {},
                    "risk_metrics": {}
                }
            
            # 执行对冲订单
            order_result = await self._execute_hedge_order(hedge_calculation['order_params'])
            
            # 设置风险管理
            risk_management = await self._setup_risk_management(
                order_result, hedge_params
            )
            
            # 计算风险指标
            risk_metrics = await self._calculate_risk_metrics(
                account_info['data'], order_result, hedge_params
            )
            
            result = {
                "hedge_result": {
                    "order_id": order_result.get('order_id'),
                    "symbol": hedge_params['symbol'],
                    "side": hedge_params['side'],
                    "quantity": hedge_calculation['order_params']['quantity'],
                    "price": market_price['data']['price'],
                    "hedge_type": hedge_params['hedge_type'],
                    "leverage": hedge_calculation['order_params'].get('leverage', 1),
                    "status": order_result.get('status', 'unknown'),
                    "fill_price": order_result.get('fill_price'),
                    "commission": order_result.get('commission'),
                    "timestamp": datetime.utcnow().isoformat()
                },
                "risk_metrics": risk_metrics
            }
            
            logger.info("binance_hedge_completed",
                       symbol=hedge_params['symbol'],
                       order_id=order_result.get('order_id'),
                       status=order_result.get('status'))
            
            return result
            
        except Exception as e:
            logger.error("binance_hedge_tool_failed", error=str(e))
            return {
                "error": str(e),
                "hedge_result": {},
                "risk_metrics": {}
            }
    
    def _validate_hedge_params(self, params: Dict) -> Dict[str, Any]:
        """验证对冲参数"""
        required_fields = ['symbol', 'side', 'quantity', 'hedge_type']
        
        for field in required_fields:
            if field not in params:
                return {'valid': False, 'reason': f'缺少必需参数: {field}'}
        
        # 验证交易对
        symbol = params['symbol'].upper()
        if symbol not in self.supported_symbols:
            return {'valid': False, 'reason': f'不支持的交易对: {symbol}'}
        
        # 验证方向
        if params['side'].upper() not in ['BUY', 'SELL']:
            return {'valid': False, 'reason': '交易方向必须是 BUY 或 SELL'}
        
        # 验证数量
        try:
            quantity = float(params['quantity'])
            if quantity <= 0:
                return {'valid': False, 'reason': '数量必须大于0'}
            if quantity < self.risk_params['min_order_size']:
                return {'valid': False, 'reason': f'数量不能小于最小订单金额 ${self.risk_params["min_order_size"]}'}
        except (ValueError, TypeError):
            return {'valid': False, 'reason': '数量必须是有效数字'}
        
        # 验证对冲类型
        valid_hedge_types = ['delta_neutral', 'long_hedge', 'short_hedge']
        if params['hedge_type'] not in valid_hedge_types:
            return {'valid': False, 'reason': f'对冲类型必须是: {", ".join(valid_hedge_types)}'}
        
        # 验证杠杆
        if 'leverage' in params:
            try:
                leverage = int(params['leverage'])
                if leverage < 1 or leverage > self.risk_params['max_leverage']:
                    return {'valid': False, 'reason': f'杠杆倍数必须在1-{self.risk_params["max_leverage"]}之间'}
            except (ValueError, TypeError):
                return {'valid': False, 'reason': '杠杆倍数必须是整数'}
        
        return {'valid': True, 'reason': 'validation_passed'}
    
    async def _get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            endpoint = "/fapi/v2/account"
            params = {"timestamp": int(time.time() * 1000)}
            
            response = await self._make_signed_request("GET", endpoint, params)
            
            if response['success']:
                # 简化账户信息
                account_data = response['data']
                simplified_info = {
                    'total_wallet_balance': float(account_data.get('totalWalletBalance', 0)),
                    'total_unrealized_pnl': float(account_data.get('totalUnrealizedProfit', 0)),
                    'total_margin_balance': float(account_data.get('totalMarginBalance', 0)),
                    'available_balance': float(account_data.get('availableBalance', 0)),
                    'max_withdraw_amount': float(account_data.get('maxWithdrawAmount', 0)),
                    'positions': self._parse_positions(account_data.get('positions', []))
                }
                return {'success': True, 'data': simplified_info}
            else:
                return response
                
        except Exception as e:
            logger.error("get_account_info_failed", error=str(e))
            return {'success': False, 'error': str(e), 'data': {}}
    
    async def _get_market_price(self, symbol: str) -> Dict[str, Any]:
        """获取市场价格"""
        try:
            binance_symbol = self.supported_symbols.get(symbol.upper())
            if not binance_symbol:
                return {'success': False, 'error': f'不支持的交易对: {symbol}', 'data': {}}
            
            endpoint = "/fapi/v1/ticker/price"
            params = {"symbol": binance_symbol}
            
            response = await self._make_request("GET", endpoint, params)
            
            if response['success']:
                price_data = response['data']
                return {
                    'success': True,
                    'data': {
                        'symbol': binance_symbol,
                        'price': float(price_data['price']),
                        'timestamp': datetime.utcnow().isoformat()
                    }
                }
            else:
                return response
                
        except Exception as e:
            logger.error("get_market_price_failed", symbol=symbol, error=str(e))
            return {'success': False, 'error': str(e), 'data': {}}
    
    async def _calculate_hedge_params(self, hedge_params: Dict, account_info: Dict, 
                                    market_info: Dict) -> Dict[str, Any]:
        """计算对冲参数"""
        try:
            symbol = hedge_params['symbol'].upper()
            binance_symbol = self.supported_symbols[symbol]
            side = hedge_params['side'].upper()
            quantity_usd = float(hedge_params['quantity'])
            leverage = hedge_params.get('leverage', 1)
            
            # 获取当前价格
            current_price = market_info['price']
            
            # 计算实际数量 (代币数量)
            quantity_tokens = quantity_usd / current_price
            
            # 应用杠杆
            if leverage > 1:
                quantity_tokens = quantity_tokens * leverage
            
            # 风险检查
            available_balance = account_info['available_balance']
            required_margin = quantity_usd / leverage
            
            if required_margin > available_balance * self.risk_params['max_position_size']:
                return {
                    'valid': False,
                    'reason': f'仓位超过最大限制 {self.risk_params["max_position_size"]*100}%'
                }
            
            if required_margin > available_balance:
                return {
                    'valid': False,
                    'reason': '余额不足以执行对冲'
                }
            
            # 计算止损止盈价格
            stop_loss_price = None
            take_profit_price = None
            
            if hedge_params.get('stop_loss'):
                stop_loss_price = float(hedge_params['stop_loss'])
            else:
                if side == 'BUY':
                    stop_loss_price = current_price * (1 - self.risk_params['stop_loss_pct'])
                else:
                    stop_loss_price = current_price * (1 + self.risk_params['stop_loss_pct'])
            
            if hedge_params.get('take_profit'):
                take_profit_price = float(hedge_params['take_profit'])
            else:
                if side == 'BUY':
                    take_profit_price = current_price * (1 + self.risk_params['take_profit_pct'])
                else:
                    take_profit_price = current_price * (1 - self.risk_params['take_profit_pct'])
            
            order_params = {
                'symbol': binance_symbol,
                'side': side,
                'type': 'MARKET',
                'quantity': round(quantity_tokens, 6),
                'leverage': leverage,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'required_margin': required_margin
            }
            
            return {
                'valid': True,
                'order_params': order_params,
                'risk_analysis': {
                    'required_margin': required_margin,
                    'available_balance': available_balance,
                    'position_size_pct': (required_margin / available_balance) * 100,
                    'max_loss_usd': quantity_usd * self.risk_params['stop_loss_pct']
                }
            }
            
        except Exception as e:
            logger.error("calculate_hedge_params_failed", error=str(e))
            return {'valid': False, 'reason': f'计算失败: {str(e)}'}
    
    async def _execute_hedge_order(self, order_params: Dict) -> Dict[str, Any]:
        """执行对冲订单"""
        try:
            # 首先设置杠杆
            leverage_result = await self._set_leverage(
                order_params['symbol'], 
                order_params['leverage']
            )
            
            if not leverage_result['success']:
                logger.warning("set_leverage_failed", 
                             symbol=order_params['symbol'],
                             error=leverage_result['error'])
            
            # 执行市价单
            endpoint = "/fapi/v1/order"
            params = {
                "symbol": order_params['symbol'],
                "side": order_params['side'],
                "type": order_params['type'],
                "quantity": order_params['quantity'],
                "timestamp": int(time.time() * 1000)
            }
            
            response = await self._make_signed_request("POST", endpoint, params)
            
            if response['success']:
                order_data = response['data']
                return {
                    'order_id': order_data['orderId'],
                    'status': order_data['status'],
                    'fill_price': float(order_data.get('price', 0)),
                    'commission': float(order_data.get('commission', 0)),
                    'executed_qty': float(order_data.get('executedQty', 0))
                }
            else:
                return {
                    'order_id': None,
                    'status': 'FAILED',
                    'error': response['error']
                }
                
        except Exception as e:
            logger.error("execute_hedge_order_failed", error=str(e))
            return {
                'order_id': None,
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def _set_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """设置杠杆倍数"""
        try:
            endpoint = "/fapi/v1/leverage"
            params = {
                "symbol": symbol,
                "leverage": leverage,
                "timestamp": int(time.time() * 1000)
            }
            
            return await self._make_signed_request("POST", endpoint, params)
            
        except Exception as e:
            logger.error("set_leverage_failed", symbol=symbol, leverage=leverage, error=str(e))
            return {'success': False, 'error': str(e)}
    
    async def _setup_risk_management(self, order_result: Dict, hedge_params: Dict) -> Dict[str, Any]:
        """设置风险管理订单（止损止盈）"""
        if not order_result.get('order_id') or order_result.get('status') != 'FILLED':
            return {'success': False, 'reason': '主订单未成功执行'}
        
        risk_orders = []
        
        # 这里可以添加止损止盈订单的逻辑
        # 由于API限制，简化处理
        return {
            'success': True,
            'stop_loss_order': None,
            'take_profit_order': None,
            'risk_orders': risk_orders
        }
    
    async def _calculate_risk_metrics(self, account_info: Dict, order_result: Dict, 
                                    hedge_params: Dict) -> Dict[str, Any]:
        """计算风险指标"""
        try:
            # 计算基础风险指标
            total_balance = account_info['total_wallet_balance']
            position_value = float(hedge_params['quantity'])
            
            risk_metrics = {
                'portfolio_exposure': {
                    'total_balance_usd': total_balance,
                    'position_value_usd': position_value,
                    'exposure_percentage': (position_value / total_balance * 100) if total_balance > 0 else 0
                },
                'delta_neutral_metrics': {
                    'hedge_effectiveness': 0.95,  # 简化计算
                    'net_delta': 0.05,  # 接近0表示良好的中性
                    'correlation_with_lp': -0.8  # 负相关表示有效对冲
                },
                'risk_limits': {
                    'max_exposure_pct': self.risk_params['max_position_size'] * 100,
                    'current_exposure_pct': (position_value / total_balance * 100) if total_balance > 0 else 0,
                    'stop_loss_pct': self.risk_params['stop_loss_pct'] * 100,
                    'max_leverage': self.risk_params['max_leverage']
                },
                'pnl_estimates': {
                    'unrealized_pnl': account_info['total_unrealized_pnl'],
                    'max_potential_loss': position_value * self.risk_params['stop_loss_pct'],
                    'max_potential_gain': position_value * self.risk_params['take_profit_pct']
                },
                'hedge_status': {
                    'hedge_type': hedge_params['hedge_type'],
                    'hedge_ratio': 1.0,  # 1:1对冲比例
                    'effective_date': datetime.utcnow().isoformat(),
                    'rebalance_needed': False
                }
            }
            
            return risk_metrics
            
        except Exception as e:
            logger.error("calculate_risk_metrics_failed", error=str(e))
            return {'error': f'风险指标计算失败: {str(e)}'}
    
    def _parse_positions(self, positions: List[Dict]) -> List[Dict]:
        """解析持仓信息"""
        parsed_positions = []
        
        for pos in positions:
            if float(pos.get('positionAmt', 0)) != 0:  # 只返回有持仓的
                parsed_positions.append({
                    'symbol': pos['symbol'],
                    'position_amt': float(pos['positionAmt']),
                    'entry_price': float(pos['entryPrice']),
                    'mark_price': float(pos['markPrice']),
                    'unrealized_profit': float(pos['unRealizedProfit']),
                    'percentage': float(pos.get('percentage', 0))
                })
        
        return parsed_positions
    
    async def _make_request(self, method: str, endpoint: str, params: Dict = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method == "GET":
                    async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        data = await response.json()
                        if response.status == 200:
                            return {'success': True, 'data': data}
                        else:
                            return {'success': False, 'error': data.get('msg', 'Unknown error')}
                elif method == "POST":
                    async with session.post(url, data=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        data = await response.json()
                        if response.status == 200:
                            return {'success': True, 'data': data}
                        else:
                            return {'success': False, 'error': data.get('msg', 'Unknown error')}
        except Exception as e:
            logger.error("http_request_failed", method=method, endpoint=endpoint, error=str(e))
            return {'success': False, 'error': str(e)}
    
    async def _make_signed_request(self, method: str, endpoint: str, params: Dict) -> Dict[str, Any]:
        """发送签名请求"""
        if not self.api_key or not self.api_secret:
            return {'success': False, 'error': 'Missing API credentials'}
        
        # 创建查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        
        # 创建签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        params['signature'] = signature
        
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method == "GET":
                    async with session.get(url, params=params, headers=headers, 
                                         timeout=aiohttp.ClientTimeout(total=30)) as response:
                        data = await response.json()
                        if response.status == 200:
                            return {'success': True, 'data': data}
                        else:
                            return {'success': False, 'error': data.get('msg', 'Unknown error')}
                elif method == "POST":
                    async with session.post(url, data=params, headers=headers,
                                          timeout=aiohttp.ClientTimeout(total=30)) as response:
                        data = await response.json()
                        if response.status == 200:
                            return {'success': True, 'data': data}
                        else:
                            return {'success': False, 'error': data.get('msg', 'Unknown error')}
        except Exception as e:
            logger.error("signed_request_failed", method=method, endpoint=endpoint, error=str(e))
            return {'success': False, 'error': str(e)}


# 为了向后兼容，如果Agno不可用，提供同步版本
class BinanceHedgeToolSync:
    """BinanceHedgeTool的同步版本，用于非Agno环境"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, testnet: bool = True):
        self.tool = BinanceHedgeTool(api_key, api_secret, testnet)
    
    def execute_hedge(self, hedge_params: Dict) -> Dict[str, Any]:
        """同步版本的对冲执行"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.tool.run(hedge_params))
            finally:
                loop.close()
        except Exception as e:
            logger.error("sync_binance_hedge_failed", error=str(e))
            return {
                "error": str(e),
                "hedge_result": {},
                "risk_metrics": {}
            }


# 导出标准接口
__all__ = ['BinanceHedgeTool', 'BinanceHedgeToolSync', 'AGNO_AVAILABLE']