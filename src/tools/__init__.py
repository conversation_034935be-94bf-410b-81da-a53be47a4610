"""
Agno Tools - DeFi工具集
为Agno Framework提供标准化的DeFi工具集，包括池子扫描、评分、对冲和数据库操作
"""

from .pool_scanner_tool import PoolScannerTool, PoolScannerToolSync
from .pool_scoring_tool import PoolScoringTool, PoolScoringToolSync
from .binance_hedge_tool import BinanceHedgeTool, BinanceHedgeToolSync
from .supabase_db_tool import SupabaseDbTool, SupabaseDbToolSync

# 检查Agno Framework可用性
try:
    from agno.agent import Agent
    from agno.tools import Toolkit
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

__all__ = [
    # 主要工具类 (Agno Framework版本)
    'PoolScannerTool',
    'PoolScoringTool',
    'BinanceHedgeTool',
    'SupabaseDbTool',
    
    # 同步版本 (向后兼容)
    'PoolScannerToolSync',
    'PoolScoringToolSync',
    'BinanceHedgeToolSync',
    'SupabaseDbToolSync',
    
    # 工具状态
    'AGNO_AVAILABLE'
]

__version__ = "1.1.0"

# 工具集描述
TOOLS_INFO = {
    'PoolScannerTool': {
        'description': 'DeFi池子扫描工具，支持BSC PancakeSwap和Solana Meteora',
        'category': 'data_collection',
        'chains': ['BSC', 'Solana'],
        'agno_native': True
    },
    'PoolScoringTool': {
        'description': '基于6因子算法的DeFi池子动态评分工具',
        'category': 'analysis',
        'chains': ['BSC', 'Solana'],
        'agno_native': True
    },
    'BinanceHedgeTool': {
        'description': 'Binance对冲操作工具，支持Delta-Neutral策略',
        'category': 'trading',
        'chains': ['Binance_Futures'],
        'agno_native': True
    },
    'SupabaseDbTool': {
        'description': 'Supabase数据库操作工具，提供统一数据访问接口',
        'category': 'database',
        'chains': ['N/A'],
        'agno_native': True
    }
}

def get_available_tools():
    """获取可用工具列表及其状态"""
    tools_status = {}
    
    for tool_name in ['PoolScannerTool', 'PoolScoringTool', 'BinanceHedgeTool', 'SupabaseDbTool']:
        tools_status[tool_name] = {
            'available': True,
            'agno_framework': AGNO_AVAILABLE,
            'sync_version': f"{tool_name}Sync",
            **TOOLS_INFO[tool_name]
        }
    
    return {
        'agno_framework_available': AGNO_AVAILABLE,
        'total_tools': len(tools_status),
        'tools': tools_status
    }

def create_tool_suite(supabase_url=None, supabase_key=None,
                     binance_api_key=None, binance_api_secret=None,
                     binance_testnet=True):
    """
    创建完整的工具套件实例
    
    Args:
        supabase_url: Supabase数据库URL
        supabase_key: Supabase API密钥
        binance_api_key: Binance API密钥
        binance_api_secret: Binance API密钥
        binance_testnet: 是否使用Binance测试网
        
    Returns:
        dict: 包含所有工具实例的字典
    """
    tools = {}
    
    # 创建工具实例
    tools['scanner'] = PoolScannerTool()
    tools['scorer'] = PoolScoringTool()
    
    if binance_api_key and binance_api_secret:
        tools['hedge'] = BinanceHedgeTool(
            api_key=binance_api_key,
            api_secret=binance_api_secret,
            testnet=binance_testnet
        )
    
    if supabase_url and supabase_key:
        tools['database'] = SupabaseDbTool(
            supabase_url=supabase_url,
            supabase_key=supabase_key
        )
    
    # 如果Agno不可用，创建同步版本
    if not AGNO_AVAILABLE:
        tools['scanner_sync'] = PoolScannerToolSync()
        tools['scorer_sync'] = PoolScoringToolSync()
        
        if binance_api_key and binance_api_secret:
            tools['hedge_sync'] = BinanceHedgeToolSync(
                api_key=binance_api_key,
                api_secret=binance_api_secret,
                testnet=binance_testnet
            )
        
        if supabase_url and supabase_key:
            tools['database_sync'] = SupabaseDbToolSync(
                supabase_url=supabase_url,
                supabase_key=supabase_key
            )
    
    return tools