"""
PancakeSwap Subgraph Tool - DyFlow v3 + Agno Framework
基於 Agno Framework 的 PancakeSwap V3 Subgraph 查詢工具
支持池子發現、Zap 功能、LP 管理等
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass

# Agno Framework imports
try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        pass

# Web3 imports
try:
    from web3 import Web3
    try:
        # 尝试新版本的导入路径
        from web3.middleware.geth_poa import geth_poa_middleware
    except ImportError:
        try:
            # 尝试旧版本的导入路径
            from web3.middleware import geth_poa_middleware
        except ImportError:
            # 如果都失败，创建一个空的中间件
            def geth_poa_middleware(make_request, web3):
                return make_request

    WEB3_AVAILABLE = True
    print("✅ Web3导入成功")
except ImportError as e:
    WEB3_AVAILABLE = False
    Web3 = None
    geth_poa_middleware = None
    print(f"❌ Web3导入失败: {e}")

# HTTP imports
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None

from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

# ========== Data Models ==========

@dataclass
class PoolInfo:
    """池子信息"""
    id: str
    token0: Dict[str, Any]
    token1: Dict[str, Any]
    fee_tier: int
    sqrt_price: str
    tick: int
    liquidity: str
    volume_usd: float
    tvl_usd: float
    token0_price: float
    token1_price: float
    fee_growth_global0_x128: str
    fee_growth_global1_x128: str
    apr: float
    created_at_timestamp: int

@dataclass
class ZapParams:
    """Zap 參數"""
    pool_address: str
    token_in: str
    amount_in: float
    token_out_0: str
    token_out_1: str
    tick_lower: int
    tick_upper: int
    slippage: float = 0.5  # 0.5%

@dataclass
class LPPosition:
    """LP 持倉"""
    id: str
    owner: str
    pool: str
    token0: str
    token1: str
    tick_lower: int
    tick_upper: int
    liquidity: str
    deposited_token0: float
    deposited_token1: float
    withdrawn_token0: float
    withdrawn_token1: float
    collected_fees_token0: float
    collected_fees_token1: float

# ========== PancakeSwap Subgraph Tool ==========

class PancakeSubgraphTool(Tool if AGNO_AVAILABLE else object):
    """PancakeSwap V3 Subgraph 查詢工具"""
    
    name = "pancake_subgraph"
    description = "PancakeSwap V3 Subgraph 查詢工具，支持池子發現、Zap、LP 管理"
    
    def __init__(self, config: Dict[str, Any]):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.config = config
        self.subgraph_url = config.get(
            'subgraph_url', 
            'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ'
        )
        self.rpc_url = config.get('rpc_url', 'https://bsc-dataseed1.binance.org/')
        self.api_key = config.get('api_key', '9731921233db132a98c2325878e6c153')
        
        # Web3 客戶端
        self.w3: Optional[Web3] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 合約地址
        self.router_v3 = config.get('router_v3', '0x13f4EA83D0bd40E75C8222255bc855a974568Dd4')
        self.factory_v3 = config.get('factory_v3', '0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865')
        self.quoter = config.get('quoter', '0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997')
        
        # 緩存
        self._pool_cache: Dict[str, PoolInfo] = {}
        self._cache_ttl = timedelta(minutes=5)
        self._last_cache_update: Dict[str, datetime] = {}
        
        logger.info("pancake_subgraph_tool_initialized", 
                   subgraph_url=self.subgraph_url,
                   rpc_url=self.rpc_url)
    
    async def initialize(self) -> None:
        """初始化工具"""
        try:
            # 初始化 HTTP 會話
            if AIOHTTP_AVAILABLE:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=30),
                    headers={
                        'Content-Type': 'application/json',
                        'Authorization': f'Bearer {self.api_key}',
                        'User-Agent': 'DyFlow-Pancake-Tool/3.0'
                    }
                )
            else:
                raise DyFlowException("aiohttp not available")

            # 如果Web3可用，初始化Web3客戶端
            if WEB3_AVAILABLE and Web3 is not None:
                try:
                    self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
                    self.w3.middleware_onion.inject(geth_poa_middleware, layer=0)

                    # 測試連接
                    is_connected = self.w3.is_connected()
                    logger.info("pancake_subgraph_tool_connected", connected=is_connected)

                    if not is_connected:
                        logger.warning("web3_connection_failed", rpc_url=self.rpc_url)
                        self.w3 = None
                except Exception as e:
                    logger.warning("web3_initialization_failed", error=str(e))
                    self.w3 = None
            else:
                logger.info("web3_not_available_using_subgraph_only")
                self.w3 = None

            # 测试Subgraph连接
            try:
                test_query = """
                query TestConnection {
                    factories(first: 1) {
                        id
                        poolCount
                    }
                }
                """
                test_result = await self._execute_query(test_query, {})
                if test_result:
                    logger.info("subgraph_connection_successful")
                else:
                    logger.warning("subgraph_connection_failed")
            except Exception as e:
                logger.warning("subgraph_test_failed", error=str(e))
            
            # 初始化 Web3 客戶端
            self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
            self.w3.middleware_onion.inject(geth_poa_middleware, layer=0)
            
            # 初始化 HTTP 會話
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_key}',
                    'User-Agent': 'DyFlow-Pancake-Tool/3.0'
                }
            )
            
            # 測試連接
            is_connected = self.w3.is_connected()
            logger.info("pancake_subgraph_tool_connected", connected=is_connected)
            
            if not is_connected:
                raise DyFlowException("Failed to connect to BSC RPC")
            
        except Exception as e:
            logger.error("pancake_subgraph_tool_initialization_failed", error=str(e))
            raise DyFlowException(f"PancakeSwap subgraph tool initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.session:
                await self.session.close()
            logger.info("pancake_subgraph_tool_cleanup_completed")
        except Exception as e:
            logger.error("pancake_subgraph_tool_cleanup_failed", error=str(e))

    # ========== Pool Discovery ==========

    async def get_trending_pools(self, limit: int = 10) -> List[PoolInfo]:
        """獲取熱門池子 - 简化版本"""
        try:
            # 使用get_top_pools作为trending_pools的实现
            return await self.get_top_pools(limit=limit, min_tvl=10000)
        except Exception as e:
            logger.error("get_trending_pools_failed", error=str(e))
            return []

    async def get_top_pools(self, limit: int = 50, min_tvl: float = 50000) -> List[PoolInfo]:
        """獲取頂級池子"""
        try:
            query = """
            query getTopPools($limit: Int!, $minTvl: String!) {
              pools(
                first: $limit
                orderBy: totalValueLockedUSD
                orderDirection: desc
                where: { totalValueLockedUSD_gt: $minTvl }
              ) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                sqrtPrice
                tick
                liquidity
                volumeUSD
                totalValueLockedUSD
                token0Price
                token1Price
                feeGrowthGlobal0X128
                feeGrowthGlobal1X128
                createdAtTimestamp
              }
            }
            """
            
            variables = {
                "limit": limit,
                "minTvl": str(int(min_tvl))
            }
            
            result = await self._execute_query(query, variables)
            pools = result.get('data', {}).get('pools', [])
            
            pool_infos = []
            for pool_data in pools:
                pool_info = self._parse_pool_data(pool_data)
                if pool_info:
                    pool_infos.append(pool_info)
                    # 更新緩存
                    self._pool_cache[pool_info.id] = pool_info
                    self._last_cache_update[pool_info.id] = datetime.now()
            
            logger.info("top_pools_retrieved", count=len(pool_infos))
            return pool_infos
            
        except Exception as e:
            logger.error("get_top_pools_failed", error=str(e))
            return []
    
    async def get_pool_info(self, pool_address: str, use_cache: bool = True) -> Optional[PoolInfo]:
        """獲取特定池子信息"""
        try:
            # 檢查緩存
            if use_cache and pool_address in self._pool_cache:
                last_update = self._last_cache_update.get(pool_address)
                if last_update and datetime.now() - last_update < self._cache_ttl:
                    return self._pool_cache[pool_address]
            
            query = """
            query getPool($poolId: String!) {
              pool(id: $poolId) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                sqrtPrice
                tick
                liquidity
                volumeUSD
                totalValueLockedUSD
                token0Price
                token1Price
                feeGrowthGlobal0X128
                feeGrowthGlobal1X128
                createdAtTimestamp
              }
            }
            """
            
            variables = {"poolId": pool_address.lower()}
            
            result = await self._execute_query(query, variables)
            pool_data = result.get('data', {}).get('pool')
            
            if not pool_data:
                return None
            
            pool_info = self._parse_pool_data(pool_data)
            if pool_info:
                # 更新緩存
                self._pool_cache[pool_address] = pool_info
                self._last_cache_update[pool_address] = datetime.now()
            
            return pool_info
            
        except Exception as e:
            logger.error("get_pool_info_failed", pool=pool_address, error=str(e))
            return None
    
    def _parse_pool_data(self, data: Dict[str, Any]) -> Optional[PoolInfo]:
        """解析池子數據"""
        try:
            # 計算 APR (簡化計算)
            volume_24h = float(data.get('volumeUSD', 0))
            tvl = float(data.get('totalValueLockedUSD', 0))
            fee_tier = int(data.get('feeTier', 0))
            
            if tvl > 0:
                daily_fees = volume_24h * (fee_tier / 1000000)  # fee_tier is in hundredths of a bip
                apr = (daily_fees / tvl) * 365 * 100
            else:
                apr = 0.0
            
            return PoolInfo(
                id=data['id'],
                token0=data['token0'],
                token1=data['token1'],
                fee_tier=fee_tier,
                sqrt_price=data.get('sqrtPrice', '0'),
                tick=int(data.get('tick', 0)),
                liquidity=data.get('liquidity', '0'),
                volume_usd=volume_24h,
                tvl_usd=tvl,
                token0_price=float(data.get('token0Price', 0)),
                token1_price=float(data.get('token1Price', 0)),
                fee_growth_global0_x128=data.get('feeGrowthGlobal0X128', '0'),
                fee_growth_global1_x128=data.get('feeGrowthGlobal1X128', '0'),
                apr=apr,
                created_at_timestamp=int(data.get('createdAtTimestamp', 0))
            )
        except Exception as e:
            logger.error("parse_pool_data_failed", error=str(e))
            return None
    
    async def _execute_query(self, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """執行 GraphQL 查詢"""
        try:
            payload = {
                "query": query,
                "variables": variables
            }
            
            async with self.session.post(self.subgraph_url, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise DyFlowException(f"GraphQL request failed: {response.status} - {error_text}")
                
                result = await response.json()
                
                if 'errors' in result:
                    errors = result['errors']
                    raise DyFlowException(f"GraphQL query errors: {errors}")
                
                return result
                
        except Exception as e:
            logger.error("execute_query_failed", error=str(e))
            raise DyFlowException(f"Failed to execute GraphQL query: {e}")

    # ========== Zap Operations ==========
    
    async def execute_zap(self, params: ZapParams) -> Dict[str, Any]:
        """執行 Zap 操作 (單幣 -> LP)"""
        try:
            logger.info("pancake_zap_started", 
                       pool=params.pool_address,
                       token_in=params.token_in,
                       amount_in=params.amount_in)
            
            # 1. 獲取池子信息
            pool_info = await self.get_pool_info(params.pool_address)
            if not pool_info:
                raise DyFlowException(f"Pool not found: {params.pool_address}")
            
            # 2. 計算最優分配
            allocation = await self._calculate_optimal_allocation(params, pool_info)
            
            # 3. 構建 Zap 交易
            zap_tx = await self._build_zap_transaction(params, allocation)
            
            # 4. 發送交易 (這裡需要實際的錢包集成)
            # result = await self._send_transaction(zap_tx)
            
            logger.info("pancake_zap_completed", 
                       pool=params.pool_address,
                       allocation=allocation)
            
            return {
                "success": True,
                "pool_address": params.pool_address,
                "allocation": allocation,
                "timestamp": get_utc_timestamp().isoformat()
            }
            
        except Exception as e:
            logger.error("pancake_zap_failed", pool=params.pool_address, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "pool_address": params.pool_address,
                "timestamp": get_utc_timestamp().isoformat()
            }
    
    async def _calculate_optimal_allocation(self, params: ZapParams, 
                                          pool_info: PoolInfo) -> Dict[str, float]:
        """計算最優分配"""
        try:
            # 簡化的分配計算
            # 實際需要根據當前價格和範圍計算最優比例
            
            current_price = pool_info.token0_price
            
            # 假設 50-50 分配 (簡化)
            token0_amount = params.amount_in * 0.5
            token1_amount = params.amount_in * 0.5
            
            return {
                "token0_amount": token0_amount,
                "token1_amount": token1_amount,
                "current_price": current_price,
                "tick_lower": params.tick_lower,
                "tick_upper": params.tick_upper
            }
            
        except Exception as e:
            logger.error("calculate_optimal_allocation_failed", error=str(e))
            return {}
    
    async def _build_zap_transaction(self, params: ZapParams, 
                                   allocation: Dict[str, float]) -> Dict[str, Any]:
        """構建 Zap 交易"""
        try:
            # 這裡需要構建實際的 PancakeSwap V3 交易
            # 包括：
            # 1. 交換部分代幣
            # 2. 添加流動性
            # 3. 設置範圍
            
            transaction = {
                "to": self.router_v3,
                "data": "0x",  # 實際需要編碼的交易數據
                "value": "0",
                "gas": "300000"
            }
            
            logger.info("zap_transaction_built", allocation=allocation)
            return transaction
            
        except Exception as e:
            logger.error("build_zap_transaction_failed", error=str(e))
            raise DyFlowException(f"Failed to build zap transaction: {e}")

    # ========== Position Management ==========
    
    async def get_user_positions(self, wallet_address: str) -> List[LPPosition]:
        """獲取用戶 LP 持倉"""
        try:
            query = """
            query getUserPositions($owner: String!) {
              positions(
                where: { owner: $owner }
                orderBy: liquidity
                orderDirection: desc
              ) {
                id
                owner
                pool {
                  id
                  token0 {
                    id
                    symbol
                  }
                  token1 {
                    id
                    symbol
                  }
                }
                tickLower {
                  tickIdx
                }
                tickUpper {
                  tickIdx
                }
                liquidity
                depositedToken0
                depositedToken1
                withdrawnToken0
                withdrawnToken1
                collectedFeesToken0
                collectedFeesToken1
              }
            }
            """
            
            variables = {"owner": wallet_address.lower()}
            
            result = await self._execute_query(query, variables)
            positions_data = result.get('data', {}).get('positions', [])
            
            positions = []
            for pos_data in positions_data:
                position = self._parse_position_data(pos_data)
                if position:
                    positions.append(position)
            
            logger.info("user_positions_retrieved", 
                       wallet=wallet_address, 
                       count=len(positions))
            return positions
            
        except Exception as e:
            logger.error("get_user_positions_failed", wallet=wallet_address, error=str(e))
            return []
    
    def _parse_position_data(self, data: Dict[str, Any]) -> Optional[LPPosition]:
        """解析持倉數據"""
        try:
            return LPPosition(
                id=data['id'],
                owner=data['owner'],
                pool=data['pool']['id'],
                token0=data['pool']['token0']['id'],
                token1=data['pool']['token1']['id'],
                tick_lower=int(data['tickLower']['tickIdx']),
                tick_upper=int(data['tickUpper']['tickIdx']),
                liquidity=data['liquidity'],
                deposited_token0=float(data.get('depositedToken0', 0)),
                deposited_token1=float(data.get('depositedToken1', 0)),
                withdrawn_token0=float(data.get('withdrawnToken0', 0)),
                withdrawn_token1=float(data.get('withdrawnToken1', 0)),
                collected_fees_token0=float(data.get('collectedFeesToken0', 0)),
                collected_fees_token1=float(data.get('collectedFeesToken1', 0))
            )
        except Exception as e:
            logger.error("parse_position_data_failed", error=str(e))
            return None
