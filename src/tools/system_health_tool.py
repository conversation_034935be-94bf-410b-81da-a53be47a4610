"""
System Health Tool - 系統健康檢查工具
實現PRD中的系統監控和健康檢查功能
"""

import asyncio
import time
import psutil
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
from enum import Enum

try:
    from agno.sdk import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        name = ""
        description = ""

# DyFlow imports
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

class HealthStatus(Enum):
    """健康狀態"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """健康檢查項目"""
    name: str
    status: HealthStatus
    value: float
    threshold: float
    unit: str
    message: str
    timestamp: datetime

@dataclass
class SystemMetrics:
    """系統指標"""
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: float
    network_latency_ms: float
    active_connections: int
    uptime_seconds: float

class SystemHealthTool(Tool):
    """系統健康檢查工具 - 實現PRD系統監控"""
    
    name = "system_health"
    description = "執行系統健康檢查和性能監控"
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 健康檢查配置
        self.check_config = {
            'rpc_endpoints': {
                'solana': ['https://api.mainnet-beta.solana.com'],
                'bsc': ['https://bsc-dataseed1.binance.org/', 'https://bsc-dataseed2.binance.org/']
            },
            'api_endpoints': {
                'meteora': 'https://dlmm-api.meteora.ag/pair/all',
                'pancakeswap': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
                'coingecko': 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd'
            },
            'thresholds': {
                'rpc_latency_ms': 5000,
                'api_latency_ms': 3000,
                'cpu_usage_percent': 80,
                'memory_usage_percent': 85,
                'disk_usage_percent': 90,
                'error_rate_percent': 10
            }
        }
        
        # 更新配置
        if 'thresholds' in self.config:
            self.check_config['thresholds'].update(self.config['thresholds'])
        
        # 健康檢查歷史
        self.health_history: List[Dict[str, Any]] = []
        self.last_check_time: Optional[datetime] = None
        
    async def run(self, check_type: str = "full") -> Dict[str, Any]:
        """執行健康檢查"""
        try:
            start_time = time.time()
            
            if check_type == "full":
                return await self._full_health_check()
            elif check_type == "rpc":
                return await self._check_rpc_connectivity()
            elif check_type == "api":
                return await self._check_api_latency()
            elif check_type == "system":
                return await self._check_system_resources()
            else:
                return {
                    "success": False,
                    "error": f"不支持的檢查類型: {check_type}",
                    "checks": []
                }
                
        except Exception as e:
            logger.error("system_health_check_failed", error=str(e))
            return {
                "success": False,
                "error": str(e),
                "checks": []
            }
    
    async def _full_health_check(self) -> Dict[str, Any]:
        """完整健康檢查"""
        start_time = time.time()
        all_checks = []
        
        try:
            # 1. RPC連接檢查
            rpc_checks = await self._check_rpc_connectivity()
            all_checks.extend(rpc_checks.get('checks', []))
            
            # 2. API延遲檢查
            api_checks = await self._check_api_latency()
            all_checks.extend(api_checks.get('checks', []))
            
            # 3. 系統資源檢查
            system_checks = await self._check_system_resources()
            all_checks.extend(system_checks.get('checks', []))
            
            # 4. 錯誤率檢查
            error_checks = await self._check_error_rates()
            all_checks.extend(error_checks.get('checks', []))
            
            # 計算整體健康狀態
            overall_status = self._calculate_overall_status(all_checks)
            
            # 生成健康報告
            health_report = {
                "success": True,
                "overall_status": overall_status.value,
                "total_checks": len(all_checks),
                "healthy_checks": len([c for c in all_checks if c['status'] == HealthStatus.HEALTHY.value]),
                "warning_checks": len([c for c in all_checks if c['status'] == HealthStatus.WARNING.value]),
                "critical_checks": len([c for c in all_checks if c['status'] == HealthStatus.CRITICAL.value]),
                "checks": all_checks,
                "check_duration_seconds": time.time() - start_time,
                "timestamp": datetime.now().isoformat()
            }
            
            # 記錄歷史
            self._record_health_history(health_report)
            
            logger.info("full_health_check_completed",
                       overall_status=overall_status.value,
                       total_checks=len(all_checks),
                       duration=health_report['check_duration_seconds'])
            
            return health_report
            
        except Exception as e:
            logger.error("full_health_check_failed", error=str(e))
            return {
                "success": False,
                "error": str(e),
                "checks": all_checks
            }
    
    async def _check_rpc_connectivity(self) -> Dict[str, Any]:
        """檢查RPC連接性"""
        checks = []
        
        for chain, endpoints in self.check_config['rpc_endpoints'].items():
            for endpoint in endpoints:
                try:
                    start_time = time.time()
                    
                    # 測試RPC連接
                    async with aiohttp.ClientSession() as session:
                        if chain == 'solana':
                            # Solana RPC健康檢查
                            payload = {
                                "jsonrpc": "2.0",
                                "id": 1,
                                "method": "getHealth"
                            }
                        else:
                            # BSC RPC健康檢查
                            payload = {
                                "jsonrpc": "2.0",
                                "id": 1,
                                "method": "eth_blockNumber",
                                "params": []
                            }
                        
                        async with session.post(
                            endpoint,
                            json=payload,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            latency_ms = (time.time() - start_time) * 1000
                            
                            if response.status == 200:
                                data = await response.json()
                                if 'result' in data or 'error' not in data:
                                    status = HealthStatus.HEALTHY if latency_ms < self.check_config['thresholds']['rpc_latency_ms'] else HealthStatus.WARNING
                                    message = f"RPC響應正常，延遲 {latency_ms:.0f}ms"
                                else:
                                    status = HealthStatus.CRITICAL
                                    message = f"RPC錯誤: {data.get('error', 'Unknown error')}"
                            else:
                                status = HealthStatus.CRITICAL
                                message = f"RPC HTTP錯誤: {response.status}"
                            
                            checks.append({
                                "name": f"rpc_{chain}_{endpoint.split('/')[-1][:10]}",
                                "status": status.value,
                                "value": latency_ms,
                                "threshold": self.check_config['thresholds']['rpc_latency_ms'],
                                "unit": "ms",
                                "message": message,
                                "endpoint": endpoint,
                                "timestamp": datetime.now().isoformat()
                            })

                except Exception as e:
                    checks.append({
                        "name": f"rpc_{chain}_{endpoint.split('/')[-1][:10]}",
                        "status": HealthStatus.CRITICAL.value,
                        "value": 0,
                        "threshold": self.check_config['thresholds']['rpc_latency_ms'],
                        "unit": "ms",
                        "message": f"RPC連接失敗: {str(e)}",
                        "endpoint": endpoint,
                        "timestamp": datetime.now().isoformat()
                    })
        
        return {
            "success": True,
            "checks": checks
        }
    
    async def _check_api_latency(self) -> Dict[str, Any]:
        """檢查API延遲"""
        checks = []
        
        for api_name, endpoint in self.check_config['api_endpoints'].items():
            try:
                start_time = time.time()
                
                async with aiohttp.ClientSession() as session:
                    headers = {}
                    
                    # 為PancakeSwap添加API密鑰
                    if api_name == 'pancakeswap':
                        headers['Authorization'] = 'Bearer 9731921233db132a98c2325878e6c153'
                        headers['Content-Type'] = 'application/json'
                        # 使用簡單的GraphQL查詢
                        payload = {
                            "query": "{ factories(first: 1) { id } }"
                        }
                        async with session.post(
                            endpoint,
                            json=payload,
                            headers=headers,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            latency_ms = (time.time() - start_time) * 1000
                            status_code = response.status
                    else:
                        async with session.get(
                            endpoint,
                            headers=headers,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            latency_ms = (time.time() - start_time) * 1000
                            status_code = response.status
                    
                    if status_code == 200:
                        status = HealthStatus.HEALTHY if latency_ms < self.check_config['thresholds']['api_latency_ms'] else HealthStatus.WARNING
                        message = f"API響應正常，延遲 {latency_ms:.0f}ms"
                    else:
                        status = HealthStatus.CRITICAL
                        message = f"API HTTP錯誤: {status_code}"
                    
                    checks.append({
                        "name": f"api_{api_name}",
                        "status": status.value,
                        "value": latency_ms,
                        "threshold": self.check_config['thresholds']['api_latency_ms'],
                        "unit": "ms",
                        "message": message,
                        "endpoint": endpoint,
                        "timestamp": datetime.now().isoformat()
                    })

            except Exception as e:
                checks.append({
                    "name": f"api_{api_name}",
                    "status": HealthStatus.CRITICAL.value,
                    "value": 0,
                    "threshold": self.check_config['thresholds']['api_latency_ms'],
                    "unit": "ms",
                    "message": f"API連接失敗: {str(e)}",
                    "endpoint": endpoint,
                    "timestamp": datetime.now().isoformat()
                })
        
        return {
            "success": True,
            "checks": checks
        }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """檢查系統資源"""
        checks = []
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = HealthStatus.HEALTHY
            if cpu_percent > self.check_config['thresholds']['cpu_usage_percent']:
                cpu_status = HealthStatus.CRITICAL
            elif cpu_percent > self.check_config['thresholds']['cpu_usage_percent'] * 0.8:
                cpu_status = HealthStatus.WARNING
            
            checks.append({
                "name": "system_cpu_usage",
                "status": cpu_status.value,
                "value": cpu_percent,
                "threshold": self.check_config['thresholds']['cpu_usage_percent'],
                "unit": "%",
                "message": f"CPU使用率 {cpu_percent:.1f}%",
                "timestamp": datetime.now().isoformat()
            })

            # 內存使用率
            memory = psutil.virtual_memory()
            memory_status = HealthStatus.HEALTHY
            if memory.percent > self.check_config['thresholds']['memory_usage_percent']:
                memory_status = HealthStatus.CRITICAL
            elif memory.percent > self.check_config['thresholds']['memory_usage_percent'] * 0.8:
                memory_status = HealthStatus.WARNING

            checks.append({
                "name": "system_memory_usage",
                "status": memory_status.value,
                "value": memory.percent,
                "threshold": self.check_config['thresholds']['memory_usage_percent'],
                "unit": "%",
                "message": f"內存使用率 {memory.percent:.1f}%",
                "timestamp": datetime.now().isoformat()
            })

            # 磁盤使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_status = HealthStatus.HEALTHY
            if disk_percent > self.check_config['thresholds']['disk_usage_percent']:
                disk_status = HealthStatus.CRITICAL
            elif disk_percent > self.check_config['thresholds']['disk_usage_percent'] * 0.8:
                disk_status = HealthStatus.WARNING

            checks.append({
                "name": "system_disk_usage",
                "status": disk_status.value,
                "value": disk_percent,
                "threshold": self.check_config['thresholds']['disk_usage_percent'],
                "unit": "%",
                "message": f"磁盤使用率 {disk_percent:.1f}%",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error("system_resource_check_failed", error=str(e))
        
        return {
            "success": True,
            "checks": checks
        }
    
    async def _check_error_rates(self) -> Dict[str, Any]:
        """檢查錯誤率"""
        checks = []
        
        try:
            # 這裡應該從日誌或監控系統獲取錯誤率
            # 簡化實現，返回模擬數據
            error_rate = 2.5  # 2.5% 錯誤率
            
            status = HealthStatus.HEALTHY
            if error_rate > self.check_config['thresholds']['error_rate_percent']:
                status = HealthStatus.CRITICAL
            elif error_rate > self.check_config['thresholds']['error_rate_percent'] * 0.5:
                status = HealthStatus.WARNING
            
            checks.append({
                "name": "system_error_rate",
                "status": status.value,
                "value": error_rate,
                "threshold": self.check_config['thresholds']['error_rate_percent'],
                "unit": "%",
                "message": f"系統錯誤率 {error_rate:.1f}%",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error("error_rate_check_failed", error=str(e))
        
        return {
            "success": True,
            "checks": checks
        }
    
    def _calculate_overall_status(self, checks: List[Dict[str, Any]]) -> HealthStatus:
        """計算整體健康狀態"""
        if not checks:
            return HealthStatus.UNKNOWN
        
        critical_count = len([c for c in checks if c['status'] == HealthStatus.CRITICAL.value])
        warning_count = len([c for c in checks if c['status'] == HealthStatus.WARNING.value])
        
        if critical_count > 0:
            return HealthStatus.CRITICAL
        elif warning_count > 0:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY
    
    def _record_health_history(self, health_report: Dict[str, Any]) -> None:
        """記錄健康檢查歷史"""
        self.health_history.append(health_report)
        self.last_check_time = datetime.now()
        
        # 只保留最近100次檢查記錄
        if len(self.health_history) > 100:
            self.health_history = self.health_history[-100:]
    
    def get_health_summary(self) -> Dict[str, Any]:
        """獲取健康狀態摘要"""
        if not self.health_history:
            return {"status": "no_data", "message": "尚未執行健康檢查"}
        
        latest = self.health_history[-1]
        
        return {
            "current_status": latest.get('overall_status'),
            "last_check_time": self.last_check_time.isoformat() if self.last_check_time else None,
            "total_checks": latest.get('total_checks', 0),
            "critical_issues": latest.get('critical_checks', 0),
            "warning_issues": latest.get('warning_checks', 0),
            "uptime_status": "healthy" if latest.get('critical_checks', 0) == 0 else "degraded"
        }
