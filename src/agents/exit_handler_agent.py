"""
ExitHandler Agent - 緊急退出處理Agent
實現PRD中的緊急退出和風險熔斷功能
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog
from dataclasses import dataclass
from enum import Enum

# DyFlow imports
from .base_agent import BaseAgent
from ..tools.meteora_dlmm_tool import MeteoraDLMMTool
from ..tools.pancake_subgraph_tool import PancakeSubgraphTool
from ..tools.jupiter_swap_tool import Jupiter<PERSON>wapTool
from ..tools.oneinch_swap_tool import OneInchSwapTool
from ..tools.supabase_db_tool import SupabaseDbTool
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

class ExitStrategy(Enum):
    """退出策略"""
    IMMEDIATE = "immediate"      # 立即退出
    GRADUAL = "gradual"         # 漸進退出
    PARTIAL = "partial"         # 部分退出

class ExitReason(Enum):
    """退出原因"""
    IL_FUSE = "il_fuse"                    # IL熔斷
    VAR_FUSE = "var_fuse"                  # VaR熔斷
    MANUAL_TRIGGER = "manual_trigger"      # 手動觸發
    SYSTEM_ERROR = "system_error"          # 系統錯誤
    MARKET_CRASH = "market_crash"          # 市場崩盤

@dataclass
class ExitOrder:
    """退出訂單"""
    position_id: str
    chain: str
    protocol: str
    pool_address: str
    token_a: str
    token_b: str
    liquidity_amount: float
    exit_percentage: float
    strategy: ExitStrategy
    max_slippage: float
    priority: int  # 1=最高, 5=最低

@dataclass
class ExitReceipt:
    """退出收據"""
    order_id: str
    position_id: str
    chain: str
    exit_strategy: ExitStrategy
    exit_reason: ExitReason
    liquidity_removed: float
    tokens_received: Dict[str, float]
    swap_transactions: List[Dict[str, Any]]
    total_value_usd: float
    execution_time_seconds: float
    gas_cost_usd: float
    slippage_percent: float
    status: str  # success, partial, failed
    error_message: Optional[str]
    timestamp: datetime

class ExitHandlerAgent(BaseAgent):
    """緊急退出處理Agent - 實現PRD風險熔斷功能"""

    def __init__(self, config: Dict[str, Any]):
        # 創建基本的Agent配置
        agent_config = {
            'name': 'ExitHandler',
            'model_provider': 'ollama',
            'model_name': 'qwen2.5:3b'
        }
        super().__init__(agent_config)

        # 保存原始配置
        self.exit_config = config
        
        # 退出配置
        self.default_exit_strategy = ExitStrategy(config.get('default_exit_strategy', 'immediate'))
        self.max_slippage_emergency = config.get('max_slippage_emergency', 0.05)  # 5%
        self.max_slippage_normal = config.get('max_slippage_normal', 0.02)  # 2%
        self.parallel_execution = config.get('parallel_execution', True)
        self.max_concurrent_exits = config.get('max_concurrent_exits', 5)
        
        # 工具實例
        self.meteora_tool: Optional[MeteoraDLMMTool] = None
        self.pancake_tool: Optional[PancakeSubgraphTool] = None
        self.jupiter_swap: Optional[JupiterSwapTool] = None
        self.oneinch_swap: Optional[OneInchSwapTool] = None
        self.supabase_tool: Optional[SupabaseDbTool] = None
        
        # 執行狀態
        self.active_exits: Dict[str, ExitOrder] = {}
        self.exit_history: List[ExitReceipt] = []
        
    async def initialize(self) -> None:
        """初始化Agent和工具"""
        await super().initialize()
        
        try:
            # 初始化交易工具
            meteora_config = {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'api_base': 'https://dlmm-api.meteora.ag',
                'program_id': 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'
            }
            self.meteora_tool = MeteoraDLMMTool(meteora_config)
            await self.meteora_tool.initialize()
            
            pancake_config = {
                'subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
                'api_key': '9731921233db132a98c2325878e6c153',
                'rpc_url': 'https://bsc-dataseed1.binance.org/'
            }
            self.pancake_tool = PancakeSubgraphTool(pancake_config)
            await self.pancake_tool.initialize()
            
            # 初始化交換工具
            jupiter_config = {'rpc_url': 'https://api.mainnet-beta.solana.com'}
            self.jupiter_swap = JupiterSwapTool(jupiter_config)
            await self.jupiter_swap.initialize()
            
            oneinch_config = {'rpc_url': 'https://bsc-dataseed1.binance.org/'}
            self.oneinch_swap = OneInchSwapTool(oneinch_config)
            await self.oneinch_swap.initialize()
            
            # 初始化數據庫工具
            supabase_config = {
                'supabase_url': self.config.get('supabase_url'),
                'supabase_key': self.config.get('supabase_key')
            }
            self.supabase_tool = SupabaseDbTool(**supabase_config)
            
            logger.info("exit_handler_agent_initialized",
                       default_strategy=self.default_exit_strategy.value,
                       max_slippage_emergency=self.max_slippage_emergency,
                       parallel_execution=self.parallel_execution)
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error("exit_handler_agent_initialization_failed", error=str(e))
            raise DyFlowException(f"ExitHandler Agent初始化失敗: {e}")
    
    async def execute(self) -> Any:
        """執行退出處理 - 檢查是否有需要處理的退出請求"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("exit_handler_execution_started")
            
            # 1. 檢查風險熔斷觸發
            fuse_triggers = await self._check_fuse_triggers()
            
            # 2. 檢查手動退出請求
            manual_exits = await self._check_manual_exit_requests()
            
            # 3. 合併所有退出請求
            all_exit_requests = fuse_triggers + manual_exits
            
            if not all_exit_requests:
                logger.info("no_exit_requests_found")
                return self._create_agent_result([])
            
            # 4. 執行退出
            exit_receipts = await self._execute_exits(all_exit_requests)
            
            # 5. 記錄退出結果
            await self._record_exit_results(exit_receipts)
            
            logger.info("exit_handler_execution_completed",
                       total_exits=len(exit_receipts),
                       successful_exits=len([r for r in exit_receipts if r.status == 'success']))
            
            return self._create_agent_result(exit_receipts)
            
        except Exception as e:
            logger.error("exit_handler_execution_failed", error=str(e))
            return self._create_error_result(str(e))
    
    async def _check_fuse_triggers(self) -> List[ExitOrder]:
        """檢查風險熔斷觸發"""
        exit_orders = []
        
        try:
            # 從Supabase獲取最新風險告警
            alerts_result = await self.supabase_tool.run(
                operation='select',
                table='risk_alerts',
                filters={
                    'resolved': False,
                    'level': {'op': 'in', 'value': ['high', 'critical']}
                }
            )
            
            if not alerts_result.get('success'):
                return exit_orders
            
            alerts = alerts_result.get('result', [])
            
            for alert in alerts:
                # 檢查IL熔斷
                if 'il_fuse' in alert.get('message', '').lower():
                    affected_pools = alert.get('affected_pools', [])
                    for pool_id in affected_pools:
                        exit_order = await self._create_emergency_exit_order(
                            pool_id, ExitReason.IL_FUSE
                        )
                        if exit_order:
                            exit_orders.append(exit_order)
                
                # 檢查VaR熔斷
                elif 'var_fuse' in alert.get('message', '').lower():
                    affected_pools = alert.get('affected_pools', [])
                    for pool_id in affected_pools:
                        exit_order = await self._create_emergency_exit_order(
                            pool_id, ExitReason.VAR_FUSE
                        )
                        if exit_order:
                            exit_orders.append(exit_order)
            
            return exit_orders
            
        except Exception as e:
            logger.error("fuse_trigger_check_failed", error=str(e))
            return []
    
    async def _check_manual_exit_requests(self) -> List[ExitOrder]:
        """檢查手動退出請求"""
        exit_orders = []
        
        try:
            # 從Supabase獲取手動退出請求
            requests_result = await self.supabase_tool.run(
                operation='select',
                table='exit_requests',
                filters={'status': 'pending'}
            )
            
            if not requests_result.get('success'):
                return exit_orders
            
            requests = requests_result.get('result', [])
            
            for request in requests:
                exit_order = ExitOrder(
                    position_id=request['position_id'],
                    chain=request['chain'],
                    protocol=request['protocol'],
                    pool_address=request['pool_address'],
                    token_a=request['token_a'],
                    token_b=request['token_b'],
                    liquidity_amount=request['liquidity_amount'],
                    exit_percentage=request.get('exit_percentage', 1.0),
                    strategy=ExitStrategy(request.get('strategy', 'immediate')),
                    max_slippage=request.get('max_slippage', self.max_slippage_normal),
                    priority=request.get('priority', 3)
                )
                exit_orders.append(exit_order)
            
            return exit_orders
            
        except Exception as e:
            logger.error("manual_exit_request_check_failed", error=str(e))
            return []
    
    async def _create_emergency_exit_order(self, pool_id: str, reason: ExitReason) -> Optional[ExitOrder]:
        """創建緊急退出訂單"""
        try:
            # 從Supabase獲取持倉信息
            position_result = await self.supabase_tool.run(
                operation='select',
                table='positions',
                filters={'pool_address': pool_id, 'status': 'active'}
            )
            
            if not position_result.get('success') or not position_result.get('result'):
                return None
            
            position = position_result['result'][0]
            
            # 根據原因確定退出策略
            if reason in [ExitReason.IL_FUSE, ExitReason.VAR_FUSE]:
                strategy = ExitStrategy.IMMEDIATE
                max_slippage = self.max_slippage_emergency
                priority = 1  # 最高優先級
            else:
                strategy = self.default_exit_strategy
                max_slippage = self.max_slippage_normal
                priority = 2
            
            exit_order = ExitOrder(
                position_id=position['id'],
                chain=position['chain'],
                protocol=position['protocol'],
                pool_address=position['pool_address'],
                token_a=position['token_a'],
                token_b=position['token_b'],
                liquidity_amount=position['liquidity'],
                exit_percentage=1.0,  # 緊急情況全部退出
                strategy=strategy,
                max_slippage=max_slippage,
                priority=priority
            )
            
            return exit_order
            
        except Exception as e:
            logger.error("emergency_exit_order_creation_failed",
                        pool_id=pool_id, reason=reason.value, error=str(e))
            return None

    async def _execute_exits(self, exit_orders: List[ExitOrder]) -> List[ExitReceipt]:
        """執行退出訂單"""
        if not exit_orders:
            return []

        # 按優先級排序
        exit_orders.sort(key=lambda x: x.priority)

        exit_receipts = []

        if self.parallel_execution:
            # 並發執行 (限制並發數)
            semaphore = asyncio.Semaphore(self.max_concurrent_exits)

            async def execute_with_semaphore(order):
                async with semaphore:
                    return await self._execute_single_exit(order)

            tasks = [execute_with_semaphore(order) for order in exit_orders]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, Exception):
                    logger.error("parallel_exit_execution_failed", error=str(result))
                else:
                    exit_receipts.append(result)
        else:
            # 順序執行
            for order in exit_orders:
                try:
                    receipt = await self._execute_single_exit(order)
                    exit_receipts.append(receipt)
                except Exception as e:
                    logger.error("sequential_exit_execution_failed",
                                order_id=order.position_id, error=str(e))

        return exit_receipts

    async def _execute_single_exit(self, order: ExitOrder) -> ExitReceipt:
        """執行單個退出訂單"""
        start_time = datetime.now()
        order_id = f"exit_{order.position_id}_{int(start_time.timestamp())}"

        logger.info("executing_exit_order",
                   order_id=order_id,
                   position_id=order.position_id,
                   chain=order.chain,
                   strategy=order.strategy.value)

        try:
            # 1. 移除流動性
            liquidity_result = await self._remove_liquidity(order)

            if not liquidity_result['success']:
                raise Exception(f"流動性移除失敗: {liquidity_result['error']}")

            tokens_received = liquidity_result['tokens_received']

            # 2. 交換為基礎代幣 (如果需要)
            swap_transactions = []
            if order.strategy == ExitStrategy.IMMEDIATE:
                swap_result = await self._swap_to_base_token(order, tokens_received)
                swap_transactions = swap_result.get('transactions', [])
                tokens_received = swap_result.get('final_tokens', tokens_received)

            # 3. 計算總價值
            total_value_usd = await self._calculate_total_value(tokens_received, order.chain)

            # 4. 計算執行時間和成本
            execution_time = (datetime.now() - start_time).total_seconds()
            gas_cost_usd = liquidity_result.get('gas_cost_usd', 0) + sum(
                tx.get('gas_cost_usd', 0) for tx in swap_transactions
            )

            # 5. 計算滑點
            expected_value = order.liquidity_amount  # 簡化
            actual_value = total_value_usd
            slippage_percent = abs(expected_value - actual_value) / expected_value if expected_value > 0 else 0

            receipt = ExitReceipt(
                order_id=order_id,
                position_id=order.position_id,
                chain=order.chain,
                exit_strategy=order.strategy,
                exit_reason=ExitReason.MANUAL_TRIGGER,  # 默認，實際應從上下文獲取
                liquidity_removed=order.liquidity_amount * order.exit_percentage,
                tokens_received=tokens_received,
                swap_transactions=swap_transactions,
                total_value_usd=total_value_usd,
                execution_time_seconds=execution_time,
                gas_cost_usd=gas_cost_usd,
                slippage_percent=slippage_percent,
                status='success',
                error_message=None,
                timestamp=get_utc_timestamp()
            )

            logger.info("exit_order_completed",
                       order_id=order_id,
                       total_value_usd=total_value_usd,
                       execution_time=execution_time,
                       slippage_percent=slippage_percent)

            return receipt

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            receipt = ExitReceipt(
                order_id=order_id,
                position_id=order.position_id,
                chain=order.chain,
                exit_strategy=order.strategy,
                exit_reason=ExitReason.SYSTEM_ERROR,
                liquidity_removed=0,
                tokens_received={},
                swap_transactions=[],
                total_value_usd=0,
                execution_time_seconds=execution_time,
                gas_cost_usd=0,
                slippage_percent=0,
                status='failed',
                error_message=str(e),
                timestamp=get_utc_timestamp()
            )

            logger.error("exit_order_failed",
                        order_id=order_id,
                        error=str(e))

            return receipt
