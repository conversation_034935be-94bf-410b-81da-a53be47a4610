"""
Portfolio Agent - DyFlow v3 + Agno Framework
基於 Agno Framework 的投資組合管理 Agent
負責整體投資組合監控、NAV 計算和資產配置優化
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.ollama import Ollama
    from agno.tools.reasoning import ReasoningTools
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass
    class Ollama:
        pass
    class ReasoningTools:
        pass
    class BaseModel:
        pass
    class Field:
        pass

from .base_agent import BaseAgent
from ..utils.models_v3 import AgentResult, RiskLevel
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException
from ..algorithms.risk_calc import RiskCalculator

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class PortfolioAnalysis(BaseModel):
        """投資組合分析 - Agno 結構化輸出"""
        total_nav: float = Field(..., gt=0, description="總淨資產價值")
        total_pnl: float = Field(..., description="總盈虧")
        total_pnl_pct: float = Field(..., description="總盈虧百分比")
        
        # 策略分佈
        delta_neutral_allocation: float = Field(..., ge=0.0, le=1.0, description="Delta中性策略分配")
        ladder_ss_allocation: float = Field(..., ge=0.0, le=1.0, description="階梯單邊策略分配")
        passive_high_tvl_allocation: float = Field(..., ge=0.0, le=1.0, description="被動高TVL策略分配")
        cash_allocation: float = Field(..., ge=0.0, le=1.0, description="現金分配")
        
        # 風險指標
        portfolio_risk_score: float = Field(..., ge=0.0, le=100.0, description="投資組合風險評分")
        max_drawdown: float = Field(..., description="最大回撤")
        sharpe_ratio: float = Field(..., description="夏普比率")
        diversification_score: float = Field(..., ge=0.0, le=1.0, description="多樣化評分")
        
        # 表現指標
        daily_return: float = Field(..., description="日收益率")
        weekly_return: float = Field(..., description="週收益率")
        monthly_return: float = Field(..., description="月收益率")
        annualized_return: float = Field(..., description="年化收益率")
        
        # 建議
        rebalance_needed: bool = Field(..., description="是否需要重新平衡")
        risk_level: str = Field(..., description="風險級別", pattern="^(low|medium|high|critical)$")
        
        reasoning: str = Field(..., description="分析推理過程")

    class RebalanceRecommendation(BaseModel):
        """重新平衡建議 - Agno 結構化輸出"""
        action_required: bool = Field(..., description="是否需要行動")
        urgency: str = Field(..., description="緊急度", pattern="^(low|medium|high|immediate)$")
        
        # 目標配置
        target_delta_neutral: float = Field(..., ge=0.0, le=1.0, description="目標Delta中性配置")
        target_ladder_ss: float = Field(..., ge=0.0, le=1.0, description="目標階梯單邊配置")
        target_passive_high_tvl: float = Field(..., ge=0.0, le=1.0, description="目標被動高TVL配置")
        target_cash: float = Field(..., ge=0.0, le=1.0, description="目標現金配置")
        
        # 執行計劃
        positions_to_close: List[str] = Field(default_factory=list, description="需要關閉的持倉")
        positions_to_reduce: List[Dict[str, Any]] = Field(default_factory=list, description="需要減少的持倉")
        new_positions_to_open: List[Dict[str, Any]] = Field(default_factory=list, description="需要開啟的新持倉")
        
        # 風險管理
        max_position_size: float = Field(..., description="最大單一持倉規模")
        stop_loss_levels: Dict[str, float] = Field(default_factory=dict, description="止損水平")
        
        reasoning: str = Field(..., description="建議推理")

else:
    # 降級數據類
    @dataclass
    class PortfolioAnalysis:
        total_nav: float
        total_pnl_pct: float
        risk_level: str
        reasoning: str
        rebalance_needed: bool = False
        portfolio_risk_score: float = 0.0
        sharpe_ratio: float = 0.0
        max_drawdown: float = 0.0
        diversification_score: float = 0.0

    @dataclass
    class RebalanceRecommendation:
        action_required: bool
        urgency: str
        reasoning: str

# ========== Portfolio Agent ==========

class PortfolioAgent(BaseAgent):
    """投資組合管理 Agent"""
    
    def __init__(self, name: str = None, config = None):
        super().__init__(name, config)

        # 數據庫連接（可選）
        self.database = None  # 暫時不使用數據庫
        
        # Agno 相關配置
        self.agno_available = AGNO_AVAILABLE
        agent_specific_config = config or {}
        agno_config = agent_specific_config.get('agno', {})
        
        # 使用ollama配置而不是OpenAI
        self.primary_model = agno_config.get('primary_model', 'qwen3:7b')
        self.ollama_base_url = agno_config.get('base_url', 'http://localhost:11434')
        self.use_reasoning = agno_config.get('use_reasoning', False)  # 簡化推理避免複雜性
        self.debug_mode = agno_config.get('debug_mode', False)
        
        # Agno Agent 實例
        self.agno_portfolio_analyzer: Optional[Agent] = None
        self.agno_rebalance_advisor: Optional[Agent] = None
        
        # 風險計算器
        self.risk_calculator = RiskCalculator()
        
        # 投資組合配置
        self.portfolio_config = {
            'target_allocations': {
                'delta_neutral': 0.4,      # 40% Delta中性
                'ladder_ss': 0.3,          # 30% 階梯單邊
                'passive_high_tvl': 0.2,   # 20% 被動高TVL
                'cash': 0.1                # 10% 現金
            },
            'rebalance_threshold': 0.05,   # 5% 偏離閾值
            'max_position_size': 0.15,     # 最大單一持倉 15%
            'risk_budget': 0.02,           # 日風險預算 2%
            'target_sharpe': 1.5,          # 目標夏普比率
            'max_drawdown_limit': 0.15     # 最大回撤限制 15%
        }
        
        # 歷史數據追蹤
        self.nav_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {}
        self.last_rebalance_time: Optional[datetime] = None
        
        logger.info("portfolio_agent_initialized", 
                   agno_available=self.agno_available,
                   config=self.portfolio_config)
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        
        if not self.agno_available:
            logger.info("portfolio_agent_fallback_initialized")
            self.is_initialized = True
            return
        
        try:
            # 初始化投資組合分析 Agent
            self.agno_portfolio_analyzer = Agent(
                name="PortfolioAnalyzer",
                role="Analyze portfolio performance and risk metrics",
                agent_id="portfolio-analyzer",
                model=Ollama(id=self.primary_model, host=self.ollama_base_url),
                tools=[ReasoningTools(add_instructions=True)] if self.use_reasoning else [],
                reasoning=self.use_reasoning,
                response_model=PortfolioAnalysis,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a quantitative portfolio manager specializing in DeFi strategies.",
                    "Analyze portfolio performance, risk metrics, and allocation efficiency.",
                    "Consider market conditions, correlation effects, and risk-adjusted returns.",
                    "Provide comprehensive portfolio health assessments.",
                    "Focus on sustainable long-term performance optimization."
                ]
            )
            
            # 初始化重新平衡顧問 Agent
            self.agno_rebalance_advisor = Agent(
                name="RebalanceAdvisor",
                role="Provide strategic portfolio rebalancing recommendations",
                agent_id="rebalance-advisor",
                model=Ollama(id=self.primary_model, host=self.ollama_base_url),
                tools=[ReasoningTools(add_instructions=True)] if self.use_reasoning else [],
                reasoning=self.use_reasoning,
                response_model=RebalanceRecommendation,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a strategic asset allocation expert for DeFi portfolios.",
                    "Provide optimal rebalancing recommendations based on market conditions.",
                    "Consider transaction costs, market impact, and timing factors.",
                    "Optimize for risk-adjusted returns and portfolio efficiency.",
                    "Provide actionable execution plans with clear rationale."
                ]
            )
            
            logger.info("portfolio_agent_agno_initialized")
            self.is_initialized = True
            
        except Exception as e:
            logger.error("portfolio_agent_initialization_failed", error=str(e))
            self.agno_available = False
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行投資組合分析和管理"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("portfolio_agent_execution_started")
            
            # 1. 計算當前投資組合狀態
            portfolio_state = await self._calculate_portfolio_state()
            
            # 2. AI 投資組合分析
            portfolio_analysis = await self._analyze_portfolio_with_ai(portfolio_state)
            
            # 3. 檢查是否需要重新平衡
            rebalance_recommendation = None
            if portfolio_analysis and portfolio_analysis.rebalance_needed:
                rebalance_recommendation = await self._get_rebalance_recommendation_with_ai(
                    portfolio_state, portfolio_analysis
                )
            
            # 4. 更新歷史記錄
            self._update_portfolio_history(portfolio_state, portfolio_analysis)
            
            # 5. 計算表現指標
            performance_metrics = self._calculate_performance_metrics()
            
            logger.info("portfolio_agent_execution_completed", 
                       nav=portfolio_state.get('total_nav', 0),
                       pnl_pct=portfolio_analysis.total_pnl_pct if portfolio_analysis else 0,
                       risk_level=portfolio_analysis.risk_level if portfolio_analysis else 'unknown')
            
            return AgentResult(
                agent_name=self.name,
                data={
                    "portfolio_state": portfolio_state,
                    "portfolio_analysis": portfolio_analysis.model_dump() if portfolio_analysis and hasattr(portfolio_analysis, 'model_dump') else None,
                    "rebalance_recommendation": rebalance_recommendation.model_dump() if rebalance_recommendation and hasattr(rebalance_recommendation, 'model_dump') else None,
                    "performance_metrics": performance_metrics
                },
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "total_nav": portfolio_state.get('total_nav', 0),
                    "total_pnl_pct": portfolio_analysis.total_pnl_pct if portfolio_analysis else 0,
                    "risk_level": portfolio_analysis.risk_level if portfolio_analysis else 'unknown',
                    "rebalance_needed": portfolio_analysis.rebalance_needed if portfolio_analysis else False,
                    "agno_enhanced": self.agno_available
                }
            )
            
        except Exception as e:
            logger.error("portfolio_agent_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data={},
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )

    async def _calculate_portfolio_state(self) -> Dict[str, Any]:
        """計算當前投資組合狀態"""
        try:
            # 從數據庫獲取所有持倉
            positions = await self._get_all_positions()
            
            # 計算總 NAV 和分配
            total_nav = 0.0
            total_pnl = 0.0
            strategy_allocations = {
                'delta_neutral': 0.0,
                'ladder_ss': 0.0,
                'passive_high_tvl': 0.0,
                'cash': 0.0
            }
            
            for position in positions:
                nav = position.get('current_value', 0)
                pnl = position.get('unrealized_pnl', 0)
                strategy = position.get('strategy_type', 'unknown')
                
                total_nav += nav
                total_pnl += pnl
                
                if strategy in strategy_allocations:
                    strategy_allocations[strategy] += nav
            
            # 計算分配百分比
            if total_nav > 0:
                for strategy in strategy_allocations:
                    strategy_allocations[strategy] /= total_nav
            
            # 計算風險指標
            portfolio_risk = self._calculate_portfolio_risk(positions)
            
            return {
                "total_nav": total_nav,
                "total_pnl": total_pnl,
                "total_pnl_pct": (total_pnl / total_nav * 100) if total_nav > 0 else 0,
                "strategy_allocations": strategy_allocations,
                "portfolio_risk": portfolio_risk,
                "position_count": len(positions),
                "positions": positions,
                "timestamp": get_utc_timestamp().isoformat()
            }
            
        except Exception as e:
            logger.error("calculate_portfolio_state_failed", error=str(e))
            return {
                "total_nav": 0.0,
                "total_pnl": 0.0,
                "total_pnl_pct": 0.0,
                "strategy_allocations": {},
                "portfolio_risk": {},
                "position_count": 0,
                "positions": [],
                "error": str(e)
            }

    async def _get_all_positions(self) -> List[Dict[str, Any]]:
        """獲取所有持倉"""
        try:
            # 這裡需要實際的數據庫查詢邏輯
            # 簡化實現，返回示例數據
            
            sample_positions = [
                {
                    "id": "pos_001",
                    "pool_address": "0x1234567890abcdef",
                    "strategy_type": "delta_neutral",
                    "current_value": 10000.0,
                    "entry_value": 8000.0,
                    "unrealized_pnl": 2000.0,
                    "chain": "bsc"
                },
                {
                    "id": "pos_002",
                    "pool_address": "sol_pool_456",
                    "strategy_type": "ladder_ss",
                    "current_value": 5000.0,
                    "entry_value": 4000.0,
                    "unrealized_pnl": 1000.0,
                    "chain": "solana"
                },
                {
                    "id": "pos_003",
                    "pool_address": "0xabcdef1234567890",
                    "strategy_type": "passive_high_tvl",
                    "current_value": 3000.0,
                    "entry_value": 3000.0,
                    "unrealized_pnl": 0.0,
                    "chain": "bsc"
                }
            ]
            
            return sample_positions
            
        except Exception as e:
            logger.error("get_all_positions_failed", error=str(e))
            return []

    def _calculate_portfolio_risk(self, positions: List[Dict[str, Any]]) -> Dict[str, float]:
        """計算投資組合風險"""
        try:
            if not positions:
                return {"overall_risk": 0.0, "concentration_risk": 0.0}
            
            # 計算集中度風險
            total_value = sum(pos.get('current_value', 0) for pos in positions)
            max_position = max(pos.get('current_value', 0) for pos in positions)
            concentration_risk = (max_position / total_value) if total_value > 0 else 0
            
            # 計算整體風險評分 (簡化)
            avg_pnl_volatility = 0.15  # 假設 15% 波動率
            overall_risk = min(concentration_risk + avg_pnl_volatility, 1.0)
            
            return {
                "overall_risk": overall_risk,
                "concentration_risk": concentration_risk,
                "volatility_estimate": avg_pnl_volatility
            }
            
        except Exception as e:
            logger.error("calculate_portfolio_risk_failed", error=str(e))
            return {"overall_risk": 0.5, "concentration_risk": 0.5}

    async def _analyze_portfolio_with_ai(self, portfolio_state: Dict[str, Any]) -> Optional[PortfolioAnalysis]:
        """使用 AI 分析投資組合"""
        if not self.agno_portfolio_analyzer:
            return None
        
        try:
            analysis_prompt = f"""
            Analyze the current portfolio state for performance and risk assessment:
            
            Portfolio Overview:
            - Total NAV: ${portfolio_state['total_nav']:,.2f}
            - Total P&L: ${portfolio_state['total_pnl']:,.2f} ({portfolio_state['total_pnl_pct']:.2f}%)
            - Position Count: {portfolio_state['position_count']}
            
            Strategy Allocations:
            {portfolio_state['strategy_allocations']}
            
            Target Allocations:
            {self.portfolio_config['target_allocations']}
            
            Risk Metrics:
            {portfolio_state['portfolio_risk']}
            
            Please analyze:
            1. Overall portfolio performance and health
            2. Strategy allocation efficiency vs targets
            3. Risk-adjusted return assessment
            4. Diversification and concentration analysis
            5. Need for rebalancing actions
            
            Consider:
            - Market conditions and strategy performance
            - Risk management and capital preservation
            - Optimization opportunities
            - Sustainable growth strategies
            """
            
            response = await self.agno_portfolio_analyzer.arun(analysis_prompt)
            
            if response and hasattr(response, 'structured_content'):
                analysis = response.structured_content
                logger.info("portfolio_analysis_completed", 
                           nav=analysis.total_nav,
                           risk_level=analysis.risk_level,
                           rebalance_needed=analysis.rebalance_needed)
                return analysis
            
            return None
            
        except Exception as e:
            logger.error("portfolio_analysis_with_ai_failed", error=str(e))
            return None

    async def _get_rebalance_recommendation_with_ai(self, portfolio_state: Dict[str, Any], 
                                                  analysis: PortfolioAnalysis) -> Optional[RebalanceRecommendation]:
        """使用 AI 獲取重新平衡建議"""
        if not self.agno_rebalance_advisor:
            return None
        
        try:
            recommendation_prompt = f"""
            Provide strategic rebalancing recommendations based on portfolio analysis:
            
            Current Analysis:
            - Risk Level: {analysis.risk_level}
            - Sharpe Ratio: {analysis.sharpe_ratio:.2f}
            - Max Drawdown: {analysis.max_drawdown:.2%}
            - Diversification Score: {analysis.diversification_score:.2f}
            
            Current vs Target Allocations:
            Current: {portfolio_state['strategy_allocations']}
            Target: {self.portfolio_config['target_allocations']}
            
            Analysis Reasoning: {analysis.reasoning}
            
            Please recommend:
            1. Whether rebalancing action is required
            2. Optimal target allocations for current market
            3. Specific positions to adjust (close/reduce/open)
            4. Execution timing and priority
            5. Risk management measures
            
            Consider:
            - Transaction costs and market impact
            - Current market conditions
            - Risk budget and constraints
            - Opportunity costs
            """
            
            response = await self.agno_rebalance_advisor.arun(recommendation_prompt)
            
            if response and hasattr(response, 'structured_content'):
                recommendation = response.structured_content
                logger.info("rebalance_recommendation_completed", 
                           action_required=recommendation.action_required,
                           urgency=recommendation.urgency)
                return recommendation
            
            return None
            
        except Exception as e:
            logger.error("rebalance_recommendation_with_ai_failed", error=str(e))
            return None

    def _update_portfolio_history(self, portfolio_state: Dict[str, Any], 
                                analysis: Optional[PortfolioAnalysis]) -> None:
        """更新投資組合歷史記錄"""
        try:
            history_entry = {
                "timestamp": get_utc_timestamp(),
                "nav": portfolio_state['total_nav'],
                "pnl": portfolio_state['total_pnl'],
                "pnl_pct": portfolio_state['total_pnl_pct'],
                "allocations": portfolio_state['strategy_allocations'],
                "risk_score": analysis.portfolio_risk_score if analysis else 0,
                "sharpe_ratio": analysis.sharpe_ratio if analysis else 0
            }
            
            self.nav_history.append(history_entry)
            
            # 保留最近 30 天的歷史
            cutoff_time = get_utc_timestamp() - timedelta(days=30)
            self.nav_history = [
                entry for entry in self.nav_history 
                if entry["timestamp"] > cutoff_time
            ]
            
        except Exception as e:
            logger.error("update_portfolio_history_failed", error=str(e))

    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """計算表現指標"""
        try:
            if len(self.nav_history) < 2:
                return {}
            
            # 計算收益率
            returns = []
            for i in range(1, len(self.nav_history)):
                prev_nav = self.nav_history[i-1]['nav']
                curr_nav = self.nav_history[i]['nav']
                if prev_nav > 0:
                    returns.append((curr_nav - prev_nav) / prev_nav)
            
            if not returns:
                return {}
            
            # 計算統計指標
            avg_return = sum(returns) / len(returns)
            return_std = math.sqrt(sum((r - avg_return) ** 2 for r in returns) / len(returns))
            
            # 年化指標
            periods_per_year = 365  # 假設每日更新
            annualized_return = (1 + avg_return) ** periods_per_year - 1
            annualized_volatility = return_std * math.sqrt(periods_per_year)
            sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0
            
            # 最大回撤
            peak = self.nav_history[0]['nav']
            max_drawdown = 0
            for entry in self.nav_history:
                if entry['nav'] > peak:
                    peak = entry['nav']
                drawdown = (peak - entry['nav']) / peak
                max_drawdown = max(max_drawdown, drawdown)
            
            return {
                "annualized_return": annualized_return,
                "annualized_volatility": annualized_volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "total_return": (self.nav_history[-1]['nav'] / self.nav_history[0]['nav'] - 1) if self.nav_history[0]['nav'] > 0 else 0
            }
            
        except Exception as e:
            logger.error("calculate_performance_metrics_failed", error=str(e))
            return {}

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            # 保存歷史數據到數據庫
            # 這裡需要實際的數據庫保存邏輯
            
            logger.info("portfolio_agent_cleanup_completed")
            
        except Exception as e:
            logger.error("portfolio_agent_cleanup_failed", error=str(e))
