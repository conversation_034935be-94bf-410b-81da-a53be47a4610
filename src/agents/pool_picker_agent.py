"""
PoolPicker Agent - 池子發現和篩選Agent
基於PRD要求實現Token Discovery功能
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import math

# DyFlow imports
from .base_agent import BaseAgent
from ..tools.meteora_dlmm_tool import MeteoraDLMMTool
from ..tools.pancake_subgraph_tool import PancakeSubgraphTool
from ..tools.pool_scanner_tool import PoolScannerTool
from ..tools.pool_scoring_tool import PoolScoringTool
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

@dataclass
class PoolCandidate:
    """池子候選者"""
    pool_id: str
    chain: str
    protocol: str
    token_a: str
    token_b: str
    tvl_usd: float
    volume_24h_usd: float
    apr: float
    fdv_usd: float
    circulating_ratio: float
    risk_score: float
    total_score: float
    metadata: Dict[str, Any]

@dataclass
class DiscoveryResult:
    """發現結果"""
    top_pools: List[PoolCandidate]
    total_scanned: int
    filtered_count: int
    discovery_timestamp: datetime
    discovery_criteria: Dict[str, Any]

class PoolPickerAgent(BaseAgent):
    """池子發現和篩選Agent - 實現PRD 4.1 Token Discovery"""

    def __init__(self, config: Dict[str, Any]):
        # 創建基本的Agent配置
        agent_config = {
            'name': 'PoolPicker',
            'model_provider': 'ollama',
            'model_name': 'qwen2.5:3b'
        }
        super().__init__(agent_config)

        # 保存原始配置
        self.pool_config = config

        # 發現參數 (來自PRD)
        self.min_tvl_usd = config.get('min_tvl_usd', 20000)  # ≥ 20k USD
        self.max_fdv_usd = config.get('max_fdv_usd', 1000000)  # ≤ 1M USD
        self.max_circulating_ratio = config.get('max_circulating_ratio', 0.05)  # ≤ 5%
        self.min_total_score = config.get('min_total_score', 60)  # 最小總分
        self.max_pools_per_chain = config.get('max_pools_per_chain', 25)
        
        # 評分權重
        self.scoring_weights = config.get('scoring_weights', {
            'tvl_score': 0.25,
            'volume_score': 0.20,
            'apr_score': 0.20,
            'risk_score': 0.15,
            'liquidity_score': 0.10,
            'trend_score': 0.10
        })
        
        # 工具實例
        self.meteora_tool: Optional[MeteoraDLMMTool] = None
        self.pancake_tool: Optional[PancakeSubgraphTool] = None
        self.pool_scanner: Optional[PoolScannerTool] = None
        self.pool_scorer: Optional[PoolScoringTool] = None
        
        # 緩存
        self.last_discovery: Optional[DiscoveryResult] = None
        self.discovery_cache_ttl = timedelta(minutes=5)
        
    async def initialize(self) -> None:
        """初始化Agent和工具"""
        await super().initialize()
        
        try:
            # 初始化Meteora工具
            meteora_config = {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'api_base': 'https://dlmm-api.meteora.ag',
                'program_id': 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'
            }
            self.meteora_tool = MeteoraDLMMTool(meteora_config)
            await self.meteora_tool.initialize()
            
            # 初始化PancakeSwap工具
            pancake_config = {
                'subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
                'api_key': '9731921233db132a98c2325878e6c153',
                'rpc_url': 'https://bsc-dataseed1.binance.org/'
            }
            self.pancake_tool = PancakeSubgraphTool(pancake_config)
            await self.pancake_tool.initialize()
            
            # 初始化池子掃描器
            self.pool_scanner = PoolScannerTool()
            # PoolScannerTool不需要initialize方法
            
            # 初始化池子評分器
            self.pool_scorer = PoolScoringTool()
            # PoolScoringTool不需要initialize方法
            
            logger.info("pool_picker_agent_initialized",
                       min_tvl=self.min_tvl_usd,
                       max_fdv=self.max_fdv_usd,
                       max_circulating_ratio=self.max_circulating_ratio)
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error("pool_picker_agent_initialization_failed", error=str(e))
            raise DyFlowException(f"PoolPicker Agent初始化失敗: {e}")
    
    async def execute(self) -> Any:
        """執行池子發現和篩選"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("pool_discovery_started")
            
            # 檢查緩存
            if self._is_cache_valid():
                logger.info("using_cached_discovery_result")
                return self._create_agent_result(self.last_discovery)
            
            # 1. 掃描所有鏈的池子
            all_pools = await self._scan_all_chains()
            
            # 2. 應用PRD篩選規則
            filtered_pools = self._apply_discovery_filters(all_pools)
            
            # 3. 計算池子評分
            scored_pools = await self._score_pools(filtered_pools)
            
            # 4. 選擇頂級池子
            top_pools = self._select_top_pools(scored_pools)
            
            # 5. 創建發現結果
            discovery_result = DiscoveryResult(
                top_pools=top_pools,
                total_scanned=len(all_pools),
                filtered_count=len(filtered_pools),
                discovery_timestamp=get_utc_timestamp(),
                discovery_criteria={
                    'min_tvl_usd': self.min_tvl_usd,
                    'max_fdv_usd': self.max_fdv_usd,
                    'max_circulating_ratio': self.max_circulating_ratio,
                    'min_total_score': self.min_total_score
                }
            )
            
            # 更新緩存
            self.last_discovery = discovery_result
            
            logger.info("pool_discovery_completed",
                       total_scanned=discovery_result.total_scanned,
                       filtered_count=discovery_result.filtered_count,
                       top_pools_count=len(discovery_result.top_pools))
            
            return self._create_agent_result(discovery_result)
            
        except Exception as e:
            logger.error("pool_discovery_failed", error=str(e))
            return self._create_error_result(str(e))
    
    async def _scan_all_chains(self) -> List[Dict[str, Any]]:
        """掃描所有支持的鏈"""
        all_pools = []
        
        # 並發掃描Solana和BSC
        tasks = [
            self._scan_solana_pools(),
            self._scan_bsc_pools()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            chain_name = ['solana', 'bsc'][i]
            if isinstance(result, Exception):
                logger.error("chain_scan_failed", chain=chain_name, error=str(result))
            else:
                all_pools.extend(result)
                logger.info("chain_scan_completed", chain=chain_name, pools_found=len(result))
        
        return all_pools
    
    async def _scan_solana_pools(self) -> List[Dict[str, Any]]:
        """掃描Solana Meteora DLMM池子"""
        try:
            pools = await self.meteora_tool.get_all_pools(limit=100)
            
            formatted_pools = []
            for pool in pools:
                formatted_pools.append({
                    'pool_id': pool.address,
                    'chain': 'solana',
                    'protocol': 'meteora_dlmm',
                    'token_a': pool.token_a_address,
                    'token_b': pool.token_b_address,
                    'token_a_symbol': pool.token_a_symbol,
                    'token_b_symbol': pool.token_b_symbol,
                    'tvl_usd': pool.tvl_usd,
                    'volume_24h_usd': pool.volume_24h_usd,
                    'apr': pool.apr,
                    'fee_rate': pool.fee_rate,
                    'liquidity': pool.liquidity,
                    'raw_data': pool.__dict__
                })
            
            return formatted_pools
            
        except Exception as e:
            logger.error("solana_pool_scan_failed", error=str(e))
            return []
    
    async def _scan_bsc_pools(self) -> List[Dict[str, Any]]:
        """掃描BSC PancakeSwap V3池子"""
        try:
            pools = await self.pancake_tool.get_top_pools(limit=100, min_tvl=self.min_tvl_usd)
            
            formatted_pools = []
            for pool in pools:
                formatted_pools.append({
                    'pool_id': pool.address,
                    'chain': 'bsc',
                    'protocol': 'pancakeswap_v3',
                    'token_a': pool.token0.get('address', ''),
                    'token_b': pool.token1.get('address', ''),
                    'token_a_symbol': pool.token0.get('symbol', ''),
                    'token_b_symbol': pool.token1.get('symbol', ''),
                    'tvl_usd': pool.tvl_usd,
                    'volume_24h_usd': pool.volume_24h_usd,
                    'apr': pool.apr,
                    'fee_rate': pool.fee_tier / 10000,  # 轉換為小數
                    'liquidity': pool.liquidity,
                    'raw_data': pool.__dict__
                })
            
            return formatted_pools
            
        except Exception as e:
            logger.error("bsc_pool_scan_failed", error=str(e))
            return []
    
    def _apply_discovery_filters(self, pools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """應用PRD篩選規則"""
        filtered_pools = []
        
        for pool in pools:
            # 1. TVL篩選: ≥ 20k USD
            if pool['tvl_usd'] < self.min_tvl_usd:
                continue
            
            # 2. 估算FDV和流通比例 (簡化計算)
            estimated_fdv = self._estimate_fdv(pool)
            circulating_ratio = self._estimate_circulating_ratio(pool)
            
            # 3. FDV篩選: ≤ 1M USD
            if estimated_fdv > self.max_fdv_usd:
                continue
            
            # 4. 流通比例篩選: ≤ 5%
            if circulating_ratio > self.max_circulating_ratio:
                continue
            
            # 5. 基本健康檢查
            if not self._is_pool_healthy(pool):
                continue
            
            # 添加計算的字段
            pool['fdv_usd'] = estimated_fdv
            pool['circulating_ratio'] = circulating_ratio
            
            filtered_pools.append(pool)
        
        return filtered_pools
    
    def _estimate_fdv(self, pool: Dict[str, Any]) -> float:
        """估算FDV (簡化實現)"""
        # 基於TVL和流動性比例估算
        tvl = pool['tvl_usd']
        
        # 假設LP佔總供應量的5-20%
        estimated_lp_ratio = 0.1  # 10%
        estimated_fdv = tvl / estimated_lp_ratio
        
        return min(estimated_fdv, 10000000)  # 限制最大值
    
    def _estimate_circulating_ratio(self, pool: Dict[str, Any]) -> float:
        """估算流通比例 (簡化實現)"""
        # 基於池子特徵估算
        tvl = pool['tvl_usd']
        volume_24h = pool['volume_24h_usd']
        
        # 高交易量通常意味著更高的流通比例
        if volume_24h > 0:
            volume_ratio = volume_24h / tvl
            # 簡化映射: 高交易量 -> 高流通比例
            estimated_ratio = min(volume_ratio * 0.1, 0.5)
        else:
            estimated_ratio = 0.05  # 默認5%
        
        return estimated_ratio
    
    def _is_pool_healthy(self, pool: Dict[str, Any]) -> bool:
        """檢查池子健康狀態"""
        # 基本健康檢查
        if pool['tvl_usd'] <= 0:
            return False
        if pool['apr'] < 0 or pool['apr'] > 1000:  # APR異常
            return False
        if not pool['token_a_symbol'] or not pool['token_b_symbol']:
            return False
        
        return True

    async def _score_pools(self, pools: List[Dict[str, Any]]) -> List[PoolCandidate]:
        """計算池子評分"""
        scored_pools = []

        for pool in pools:
            try:
                # 計算各項評分
                tvl_score = self._calculate_tvl_score(pool['tvl_usd'])
                volume_score = self._calculate_volume_score(pool['volume_24h_usd'])
                apr_score = self._calculate_apr_score(pool['apr'])
                risk_score = self._calculate_risk_score(pool)
                liquidity_score = self._calculate_liquidity_score(pool)
                trend_score = self._calculate_trend_score(pool)

                # 計算總分
                total_score = (
                    tvl_score * self.scoring_weights['tvl_score'] +
                    volume_score * self.scoring_weights['volume_score'] +
                    apr_score * self.scoring_weights['apr_score'] +
                    risk_score * self.scoring_weights['risk_score'] +
                    liquidity_score * self.scoring_weights['liquidity_score'] +
                    trend_score * self.scoring_weights['trend_score']
                ) * 100

                # 創建候選者
                candidate = PoolCandidate(
                    pool_id=pool['pool_id'],
                    chain=pool['chain'],
                    protocol=pool['protocol'],
                    token_a=pool['token_a_symbol'],
                    token_b=pool['token_b_symbol'],
                    tvl_usd=pool['tvl_usd'],
                    volume_24h_usd=pool['volume_24h_usd'],
                    apr=pool['apr'],
                    fdv_usd=pool['fdv_usd'],
                    circulating_ratio=pool['circulating_ratio'],
                    risk_score=risk_score * 100,
                    total_score=total_score,
                    metadata={
                        'scores': {
                            'tvl_score': tvl_score * 100,
                            'volume_score': volume_score * 100,
                            'apr_score': apr_score * 100,
                            'risk_score': risk_score * 100,
                            'liquidity_score': liquidity_score * 100,
                            'trend_score': trend_score * 100
                        },
                        'raw_pool_data': pool['raw_data']
                    }
                )

                scored_pools.append(candidate)

            except Exception as e:
                logger.error("pool_scoring_failed", pool_id=pool['pool_id'], error=str(e))

        return scored_pools

    def _calculate_tvl_score(self, tvl_usd: float) -> float:
        """計算TVL評分 (0-1)"""
        # 對數評分，TVL越高分數越高
        if tvl_usd <= 0:
            return 0.0

        # 20k = 0.1, 100k = 0.5, 1M = 1.0
        log_tvl = math.log10(max(tvl_usd, 1))
        log_min = math.log10(20000)  # 20k
        log_max = math.log10(1000000)  # 1M

        score = (log_tvl - log_min) / (log_max - log_min)
        return max(0.0, min(1.0, score))

    def _calculate_volume_score(self, volume_24h_usd: float) -> float:
        """計算交易量評分 (0-1)"""
        if volume_24h_usd <= 0:
            return 0.0

        # 1k = 0.1, 10k = 0.5, 100k = 1.0
        log_volume = math.log10(max(volume_24h_usd, 1))
        log_min = math.log10(1000)  # 1k
        log_max = math.log10(100000)  # 100k

        score = (log_volume - log_min) / (log_max - log_min)
        return max(0.0, min(1.0, score))

    def _calculate_apr_score(self, apr: float) -> float:
        """計算APR評分 (0-1)"""
        if apr <= 0:
            return 0.0

        # 5% = 0.1, 20% = 0.5, 50% = 1.0
        if apr >= 50:
            return 1.0
        elif apr >= 20:
            return 0.5 + (apr - 20) / 60  # 20-50% 映射到 0.5-1.0
        elif apr >= 5:
            return 0.1 + (apr - 5) * 0.4 / 15  # 5-20% 映射到 0.1-0.5
        else:
            return apr / 50  # 0-5% 映射到 0-0.1

    def _calculate_risk_score(self, pool: Dict[str, Any]) -> float:
        """計算風險評分 (0-1, 1為低風險)"""
        risk_factors = []

        # 1. TVL風險 (TVL越高風險越低)
        tvl_risk = 1.0 - min(pool['tvl_usd'] / 1000000, 1.0)  # 1M TVL = 0風險
        risk_factors.append(tvl_risk)

        # 2. 波動性風險 (APR過高可能意味著高風險)
        volatility_risk = min(pool['apr'] / 100, 1.0)  # 100% APR = 最高風險
        risk_factors.append(volatility_risk)

        # 3. 流通比例風險
        circulation_risk = pool['circulating_ratio'] / self.max_circulating_ratio
        risk_factors.append(circulation_risk)

        # 4. 協議風險 (簡化)
        protocol_risk = 0.1 if pool['protocol'] in ['pancakeswap_v3', 'meteora_dlmm'] else 0.3
        risk_factors.append(protocol_risk)

        # 計算綜合風險 (取平均)
        avg_risk = sum(risk_factors) / len(risk_factors)

        # 返回風險評分 (1 - 風險 = 安全評分)
        return max(0.0, 1.0 - avg_risk)

    def _calculate_liquidity_score(self, pool: Dict[str, Any]) -> float:
        """計算流動性評分 (0-1)"""
        tvl = pool['tvl_usd']
        volume_24h = pool['volume_24h_usd']

        if tvl <= 0:
            return 0.0

        # 流動性效率 = 24h交易量 / TVL
        if volume_24h > 0:
            liquidity_efficiency = volume_24h / tvl
            # 0.1 = 0.5分, 0.5 = 1.0分
            score = min(liquidity_efficiency / 0.5, 1.0)
        else:
            score = 0.0

        return score

    def _calculate_trend_score(self, pool: Dict[str, Any]) -> float:
        """計算趨勢評分 (0-1) - 簡化實現"""
        # 基於APR和交易量的簡單趨勢評估
        apr = pool['apr']
        volume_24h = pool['volume_24h_usd']

        # 高APR + 高交易量 = 好趨勢
        apr_factor = min(apr / 30, 1.0)  # 30% APR = 滿分
        volume_factor = min(volume_24h / 50000, 1.0)  # 50k volume = 滿分

        trend_score = (apr_factor + volume_factor) / 2
        return trend_score

    def _select_top_pools(self, scored_pools: List[PoolCandidate]) -> List[PoolCandidate]:
        """選擇頂級池子"""
        # 過濾低分池子
        qualified_pools = [
            pool for pool in scored_pools
            if pool.total_score >= self.min_total_score
        ]

        # 按總分排序
        qualified_pools.sort(key=lambda x: x.total_score, reverse=True)

        # 按鏈分配
        top_pools = []
        chain_counts = {'solana': 0, 'bsc': 0}

        for pool in qualified_pools:
            if chain_counts[pool.chain] < self.max_pools_per_chain:
                top_pools.append(pool)
                chain_counts[pool.chain] += 1

            # 如果兩條鏈都達到上限，停止
            if all(count >= self.max_pools_per_chain for count in chain_counts.values()):
                break

        return top_pools

    def _is_cache_valid(self) -> bool:
        """檢查緩存是否有效"""
        if not self.last_discovery:
            return False

        cache_age = datetime.now() - self.last_discovery.discovery_timestamp.replace(tzinfo=None)
        return cache_age < self.discovery_cache_ttl

    def _create_agent_result(self, discovery_result: DiscoveryResult) -> Any:
        """創建Agent執行結果"""
        from ..utils.models import AgentResult

        return AgentResult(
            agent_name=self.name,
            data=discovery_result.top_pools,
            timestamp=get_utc_timestamp(),
            status="success",
            metadata={
                'total_scanned': discovery_result.total_scanned,
                'filtered_count': discovery_result.filtered_count,
                'top_pools_count': len(discovery_result.top_pools),
                'discovery_criteria': discovery_result.discovery_criteria,
                'chain_distribution': self._get_chain_distribution(discovery_result.top_pools),
                'avg_score': sum(p.total_score for p in discovery_result.top_pools) / len(discovery_result.top_pools) if discovery_result.top_pools else 0,
                'discovery_timestamp': discovery_result.discovery_timestamp.isoformat()
            }
        )

    def _create_error_result(self, error_message: str) -> Any:
        """創建錯誤結果"""
        from ..utils.models import AgentResult

        return AgentResult(
            agent_name=self.name,
            data=[],
            timestamp=get_utc_timestamp(),
            status="error",
            metadata={'error': error_message}
        )

    def _get_chain_distribution(self, pools: List[PoolCandidate]) -> Dict[str, int]:
        """獲取鏈分佈統計"""
        distribution = {}
        for pool in pools:
            distribution[pool.chain] = distribution.get(pool.chain, 0) + 1
        return distribution

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.meteora_tool:
                await self.meteora_tool.cleanup()
            if self.pancake_tool:
                await self.pancake_tool.cleanup()
            # PoolScannerTool和PoolScoringTool不需要cleanup方法

            logger.info("pool_picker_agent_cleanup_completed")

        except Exception as e:
            logger.error("pool_picker_agent_cleanup_failed", error=str(e))
