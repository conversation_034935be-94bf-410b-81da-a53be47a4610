"""
Range Rebalancer Agent - DyFlow v3 + Agno Framework
基於 Agno Framework 的 LP 範圍重新平衡 Agent
負責監控和調整 LP 位置範圍，確保最優資本效率
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.tools.reasoning import ReasoningTools
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass
    class BaseModel:
        pass

from .base_agent import BaseAgent
from ..utils.models_v3 import AgentResult, RiskLevel
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException
from ..tools.meteora_dlmm_tool import MeteoraDLMMTool
from ..tools.pancake_subgraph_tool import PancakeSubgraphTool

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class RangeAnalysis(BaseModel):
        """範圍分析 - Agno 結構化輸出"""
        pool_address: str = Field(..., description="池子地址")
        current_price: float = Field(..., description="當前價格")
        price_trend: str = Field(..., description="價格趨勢", pattern="^(upward|downward|sideways)$")
        volatility_level: str = Field(..., description="波動率水平", pattern="^(low|medium|high|extreme)$")
        
        # 範圍評估
        current_range_efficiency: float = Field(..., ge=0.0, le=1.0, description="當前範圍效率")
        optimal_tick_lower: int = Field(..., description="最優下界 tick")
        optimal_tick_upper: int = Field(..., description="最優上界 tick")
        rebalance_urgency: str = Field(..., description="重平衡緊急度", pattern="^(low|medium|high|critical)$")
        
        # 預期影響
        expected_capital_efficiency_gain: float = Field(..., description="預期資本效率提升")
        estimated_gas_cost: float = Field(..., description="預估 gas 成本")
        net_benefit_score: float = Field(..., ge=0.0, le=100.0, description="淨收益評分")
        
        reasoning: str = Field(..., description="分析推理過程")
        recommended_action: str = Field(..., description="建議行動", pattern="^(hold|rebalance|exit)$")

    class RebalanceDecision(BaseModel):
        """重平衡決策 - Agno 結構化輸出"""
        pool_address: str = Field(..., description="池子地址")
        action_type: str = Field(..., description="行動類型", pattern="^(full_rebalance|partial_rebalance|range_expansion|range_contraction|no_action)$")
        
        # 執行參數
        new_tick_lower: Optional[int] = Field(None, description="新下界 tick")
        new_tick_upper: Optional[int] = Field(None, description="新上界 tick")
        liquidity_to_remove: float = Field(..., ge=0.0, le=1.0, description="移除流動性比例")
        liquidity_to_add: float = Field(..., ge=0.0, description="添加流動性數量")
        
        # 時機控制
        execution_timing: str = Field(..., description="執行時機", pattern="^(immediate|next_block|wait_for_better_price|scheduled)$")
        max_slippage: float = Field(..., ge=0.0, le=0.1, description="最大滑點")
        
        # 風險評估
        execution_risk: str = Field(..., description="執行風險", pattern="^(low|medium|high)$")
        expected_outcome: str = Field(..., description="預期結果")
        
        reasoning: str = Field(..., description="決策推理")

else:
    # 降級數據類
    @dataclass
    class RangeAnalysis:
        pool_address: str
        current_price: float
        rebalance_urgency: str
        reasoning: str
        current_range_efficiency: float = 0.0
        optimal_tick_lower: int = 0
        optimal_tick_upper: int = 0
        expected_capital_efficiency_gain: float = 0.0
        estimated_gas_cost: float = 0.0

    @dataclass
    class RebalanceDecision:
        action_type: str
        execution_timing: str
        reasoning: str

# ========== Range Rebalancer Agent ==========

class RangeRebalancerAgent(BaseAgent):
    """LP 範圍重新平衡 Agent"""
    
    def __init__(self, name: str = None, config = None):
        super().__init__(name, config)

        # 數據庫連接（可選）
        self.database = None  # 暫時不使用數據庫
        
        # Agno 相關配置
        self.agno_available = AGNO_AVAILABLE
        agent_specific_config = config or {}
        agno_config = agent_specific_config.get('agno', {})
        
        self.primary_model = agno_config.get('primary_model', 'gpt-4o')
        self.use_reasoning = agno_config.get('use_reasoning', True)
        self.debug_mode = agno_config.get('debug_mode', False)
        
        # Agno Agent 實例
        self.agno_range_analyzer: Optional[Agent] = None
        self.agno_rebalance_strategist: Optional[Agent] = None
        
        # 工具實例
        self.meteora_tool: Optional[MeteoraDLMMTool] = None
        self.pancake_tool: Optional[PancakeSubgraphTool] = None
        
        # 重平衡配置
        self.rebalance_config = {
            'efficiency_threshold': 0.7,  # 效率低於 70% 時考慮重平衡
            'price_deviation_threshold': 0.15,  # 價格偏離 15% 時重平衡
            'min_rebalance_interval_hours': 4,  # 最小重平衡間隔
            'max_gas_cost_ratio': 0.02,  # 最大 gas 成本比例 (2%)
            'volatility_adjustment_factor': 1.5  # 波動率調整因子
        }
        
        # 狀態追蹤
        self.last_rebalance_times: Dict[str, datetime] = {}
        self.position_states: Dict[str, Dict[str, Any]] = {}
        
        logger.info("range_rebalancer_agent_initialized", 
                   agno_available=self.agno_available,
                   config=self.rebalance_config)
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        
        if not self.agno_available:
            logger.info("range_rebalancer_fallback_initialized")
            self.is_initialized = True
            return
        
        try:
            # 初始化工具
            tool_config = self.config.get('tools', {})
            
            meteora_config = tool_config.get('meteora_dlmm', {})
            self.meteora_tool = MeteoraDLMMTool(meteora_config)
            await self.meteora_tool.initialize()
            
            pancake_config = tool_config.get('pancake_subgraph', {})
            self.pancake_tool = PancakeSubgraphTool(pancake_config)
            await self.pancake_tool.initialize()
            
            # 初始化範圍分析 Agent
            self.agno_range_analyzer = Agent(
                name="LPRangeAnalyzer",
                role="Analyze LP position ranges for optimal capital efficiency",
                agent_id="rebalancer-range-analyzer",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=self.use_reasoning,
                response_model=RangeAnalysis,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are an expert LP range analyzer for DeFi protocols.",
                    "Analyze current LP positions and determine optimal range adjustments.",
                    "Consider price trends, volatility, capital efficiency, and gas costs.",
                    "Provide actionable insights for range rebalancing decisions.",
                    "Focus on maximizing fee generation while minimizing impermanent loss."
                ]
            )
            
            # 初始化重平衡策略 Agent
            self.agno_rebalance_strategist = Agent(
                name="LPRebalanceStrategist",
                role="Make strategic decisions for LP range rebalancing",
                agent_id="rebalancer-strategist",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=self.use_reasoning,
                response_model=RebalanceDecision,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a strategic LP rebalancing expert.",
                    "Make optimal decisions for when and how to rebalance LP positions.",
                    "Consider market conditions, gas costs, and risk-reward ratios.",
                    "Optimize for long-term profitability and capital efficiency.",
                    "Provide clear execution plans with risk assessments."
                ]
            )
            
            logger.info("range_rebalancer_agno_initialized")
            self.is_initialized = True
            
        except Exception as e:
            logger.error("range_rebalancer_initialization_failed", error=str(e))
            self.agno_available = False
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行範圍重平衡分析和操作"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("range_rebalancer_execution_started")
            
            # 1. 獲取所有活躍 LP 持倉
            active_positions = await self._get_active_positions()
            
            if not active_positions:
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有活躍的 LP 持倉需要重平衡"}
                )
            
            # 2. 分析每個持倉的範圍效率
            rebalance_actions = []
            
            for position in active_positions:
                try:
                    # 檢查重平衡間隔
                    if not self._should_check_rebalance(position['pool_address']):
                        continue
                    
                    # AI 範圍分析
                    range_analysis = await self._analyze_range_with_ai(position)
                    
                    if range_analysis and range_analysis.rebalance_urgency in ['high', 'critical']:
                        # AI 重平衡決策
                        rebalance_decision = await self._make_rebalance_decision_with_ai(
                            position, range_analysis
                        )
                        
                        if rebalance_decision and rebalance_decision.action_type != 'no_action':
                            # 執行重平衡
                            execution_result = await self._execute_rebalance(
                                position, rebalance_decision
                            )
                            
                            rebalance_actions.append({
                                "pool_address": position['pool_address'],
                                "action": rebalance_decision.action_type,
                                "analysis": range_analysis,
                                "decision": rebalance_decision,
                                "execution_result": execution_result
                            })
                
                except Exception as e:
                    logger.error("position_rebalance_failed", 
                               pool=position.get('pool_address'), 
                               error=str(e))
                    continue
            
            logger.info("range_rebalancer_execution_completed", 
                       positions_checked=len(active_positions),
                       rebalance_actions=len(rebalance_actions))
            
            return AgentResult(
                agent_name=self.name,
                data=rebalance_actions,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "positions_checked": len(active_positions),
                    "rebalance_actions": len(rebalance_actions),
                    "agno_enhanced": self.agno_available
                }
            )
            
        except Exception as e:
            logger.error("range_rebalancer_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )

    async def _get_active_positions(self) -> List[Dict[str, Any]]:
        """獲取所有活躍 LP 持倉"""
        try:
            positions = []
            
            # 從數據庫獲取活躍持倉
            # 這裡需要實際的數據庫查詢邏輯
            # 簡化實現，返回示例數據
            
            # 實際應該查詢 Supabase 中的 positions 表
            sample_positions = [
                {
                    "pool_address": "0x1234567890abcdef",
                    "chain": "bsc",
                    "protocol": "pancakeswap_v3",
                    "token0": "WBNB",
                    "token1": "USDT",
                    "tick_lower": -1000,
                    "tick_upper": 1000,
                    "liquidity": "1000000",
                    "last_update": datetime.now()
                }
            ]
            
            return sample_positions
            
        except Exception as e:
            logger.error("get_active_positions_failed", error=str(e))
            return []

    async def _analyze_range_with_ai(self, position: Dict[str, Any]) -> Optional[RangeAnalysis]:
        """使用 AI 分析範圍效率"""
        if not self.agno_range_analyzer:
            return None
        
        try:
            # 獲取池子當前狀態
            pool_info = await self._get_pool_current_state(position)
            
            analysis_prompt = f"""
            Analyze the LP position range efficiency for optimal rebalancing:
            
            Position Details:
            - Pool: {position['pool_address']}
            - Chain: {position['chain']}
            - Token Pair: {position['token0']}/{position['token1']}
            - Current Range: [{position['tick_lower']}, {position['tick_upper']}]
            - Liquidity: {position['liquidity']}
            
            Current Pool State:
            {pool_info}
            
            Please analyze:
            1. Current range efficiency and capital utilization
            2. Price trend and volatility assessment
            3. Optimal range boundaries for current market conditions
            4. Urgency of rebalancing action needed
            5. Expected benefits vs costs of rebalancing
            
            Consider:
            - Fee generation optimization
            - Impermanent loss minimization
            - Gas cost efficiency
            - Market volatility and trends
            """
            
            response = await self.agno_range_analyzer.arun(analysis_prompt)
            
            if response and hasattr(response, 'structured_content'):
                analysis = response.structured_content
                logger.info("range_analysis_completed", 
                           pool=position['pool_address'],
                           urgency=analysis.rebalance_urgency,
                           efficiency=analysis.current_range_efficiency)
                return analysis
            
            return None
            
        except Exception as e:
            logger.error("range_analysis_with_ai_failed", 
                        pool=position['pool_address'], 
                        error=str(e))
            return None

    async def _make_rebalance_decision_with_ai(self, position: Dict[str, Any], 
                                             analysis: RangeAnalysis) -> Optional[RebalanceDecision]:
        """使用 AI 制定重平衡決策"""
        if not self.agno_rebalance_strategist:
            return None
        
        try:
            decision_prompt = f"""
            Make a strategic rebalancing decision based on the range analysis:
            
            Position: {position['pool_address']}
            
            Range Analysis Results:
            - Current Efficiency: {analysis.current_range_efficiency:.2%}
            - Rebalance Urgency: {analysis.rebalance_urgency}
            - Optimal Range: [{analysis.optimal_tick_lower}, {analysis.optimal_tick_upper}]
            - Expected Efficiency Gain: {analysis.expected_capital_efficiency_gain:.2%}
            - Estimated Gas Cost: ${analysis.estimated_gas_cost:.2f}
            
            Analysis Reasoning: {analysis.reasoning}
            
            Please decide:
            1. What type of rebalancing action to take
            2. Specific execution parameters (new ranges, liquidity amounts)
            3. Optimal timing for execution
            4. Risk assessment and mitigation strategies
            5. Expected outcomes and success metrics
            
            Consider:
            - Cost-benefit analysis
            - Market timing and volatility
            - Gas optimization strategies
            - Risk management principles
            """
            
            response = await self.agno_rebalance_strategist.arun(decision_prompt)
            
            if response and hasattr(response, 'structured_content'):
                decision = response.structured_content
                logger.info("rebalance_decision_made", 
                           pool=position['pool_address'],
                           action=decision.action_type,
                           timing=decision.execution_timing)
                return decision
            
            return None
            
        except Exception as e:
            logger.error("rebalance_decision_with_ai_failed", 
                        pool=position['pool_address'], 
                        error=str(e))
            return None

    async def _execute_rebalance(self, position: Dict[str, Any], 
                               decision: RebalanceDecision) -> Dict[str, Any]:
        """執行重平衡操作"""
        try:
            logger.info("rebalance_execution_started", 
                       pool=position['pool_address'],
                       action=decision.action_type)
            
            # 根據鏈選擇工具
            if position['chain'] == 'solana':
                result = await self._execute_solana_rebalance(position, decision)
            elif position['chain'] == 'bsc':
                result = await self._execute_bsc_rebalance(position, decision)
            else:
                raise DyFlowException(f"Unsupported chain: {position['chain']}")
            
            # 更新重平衡時間
            self.last_rebalance_times[position['pool_address']] = datetime.now()
            
            return result
            
        except Exception as e:
            logger.error("rebalance_execution_failed", 
                        pool=position['pool_address'], 
                        error=str(e))
            return {
                "success": False,
                "error": str(e),
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def _execute_solana_rebalance(self, position: Dict[str, Any], 
                                      decision: RebalanceDecision) -> Dict[str, Any]:
        """執行 Solana 鏈重平衡"""
        if not self.meteora_tool:
            raise DyFlowException("Meteora tool not available")
        
        # 實現 Solana DLMM 重平衡邏輯
        # 1. 退出當前持倉
        # 2. 重新訂閱新範圍
        
        return {
            "success": True,
            "chain": "solana",
            "action": decision.action_type,
            "timestamp": get_utc_timestamp().isoformat()
        }

    async def _execute_bsc_rebalance(self, position: Dict[str, Any], 
                                   decision: RebalanceDecision) -> Dict[str, Any]:
        """執行 BSC 鏈重平衡"""
        if not self.pancake_tool:
            raise DyFlowException("PancakeSwap tool not available")
        
        # 實現 PancakeSwap V3 重平衡邏輯
        # 1. 移除流動性
        # 2. 重新添加到新範圍
        
        return {
            "success": True,
            "chain": "bsc",
            "action": decision.action_type,
            "timestamp": get_utc_timestamp().isoformat()
        }

    def _should_check_rebalance(self, pool_address: str) -> bool:
        """檢查是否應該進行重平衡檢查"""
        last_rebalance = self.last_rebalance_times.get(pool_address)
        if not last_rebalance:
            return True
        
        min_interval = timedelta(hours=self.rebalance_config['min_rebalance_interval_hours'])
        return datetime.now() - last_rebalance >= min_interval

    async def _get_pool_current_state(self, position: Dict[str, Any]) -> str:
        """獲取池子當前狀態信息"""
        try:
            # 根據鏈獲取池子狀態
            if position['chain'] == 'solana' and self.meteora_tool:
                pool_info = await self.meteora_tool.get_pool_info(position['pool_address'])
                if pool_info:
                    return f"Current Price: {pool_info.current_price}, APR: {pool_info.apr:.2%}, TVL: ${pool_info.liquidity:,.0f}"
            
            elif position['chain'] == 'bsc' and self.pancake_tool:
                pool_info = await self.pancake_tool.get_pool_info(position['pool_address'])
                if pool_info:
                    return f"Current Price: {pool_info.token0_price}, APR: {pool_info.apr:.2%}, TVL: ${pool_info.tvl_usd:,.0f}"
            
            return "Pool state information not available"
            
        except Exception as e:
            logger.error("get_pool_current_state_failed", error=str(e))
            return "Error retrieving pool state"

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.meteora_tool:
                await self.meteora_tool.cleanup()
            if self.pancake_tool:
                await self.pancake_tool.cleanup()
            
            logger.info("range_rebalancer_cleanup_completed")
            
        except Exception as e:
            logger.error("range_rebalancer_cleanup_failed", error=str(e))
