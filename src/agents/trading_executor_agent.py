"""
TradingExecutorAgent - DyFlow v3 + Agno Framework
主要的交易執行 Agent，支持 swap 功能和完整的 DLMM LP 策略部署
實現 SOL -> DLMM LP 頭寸 和 DLMM LP 頭寸 -> SOL 的完整循環
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog
from dataclasses import dataclass
from enum import Enum

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass

# DyFlow imports
from .base_agent import BaseAgent, AgentResult
from ..tools.jupiter_swap_tool import JupiterSwapTool, SwapResult, DCAParams
from ..tools.meteora_dlmm_tool import MeteoraDLMMTool, DLMMPoolInfo, DLMMPosition
from ..tools.supabase_db_tool import SupabaseDbTool
from ..utils.strategy_types import (
    DLMMStrategyType, StrategyParameters, StrategyFactory, 
    validate_strategy_params, get_strategy_config
)
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

class TradingAction(Enum):
    """交易動作類型"""
    SWAP = "swap"
    DEPLOY_LP = "deploy_lp"
    HARVEST_FEES = "harvest_fees"
    EXIT_POSITION = "exit_position"
    DCA_SWAP = "dca_swap"
    BATCH_SWAP = "batch_swap"

@dataclass
class TradingRequest:
    """交易請求"""
    action: TradingAction
    parameters: Dict[str, Any]
    priority: str = "NORMAL"  # LOW, NORMAL, HIGH, URGENT
    user_id: Optional[str] = None
    
@dataclass
class TradingResult:
    """交易結果"""
    success: bool
    action: TradingAction
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: Optional[str] = None

class TradingExecutorAgent(BaseAgent):
    """交易執行 Agent - 支持完整的 LP 策略部署和管理"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化交易執行 Agent"""
        default_config = {
            "name": "TradingExecutor",
            "model_provider": "mock",  # 默認使用 mock 模式
            "model_name": "mock-model",
            "enable_reasoning": False,  # 簡化配置
            "enable_memory": False,     # 簡化配置
            "max_retries": 3
        }

        if config:
            default_config.update(config)

        super().__init__(default_config)

        # 工具初始化
        self.jupiter_tool: Optional[JupiterSwapTool] = None
        self.meteora_tool: Optional[MeteoraDLMMTool] = None
        self.db_tool: Optional[SupabaseDbTool] = None

        # 執行統計
        self.execution_stats = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "total_volume": 0.0,
            "strategies_deployed": 0,
            "fees_harvested": 0.0
        }

        # 模擬模式標誌
        self.mock_mode = default_config.get("model_provider") == "mock"
        
    async def initialize(self) -> bool:
        """初始化 Agent 和工具"""
        try:
            # 初始化基礎 Agent
            base_init_success = await super().initialize()

            # 即使基礎 Agent 初始化失敗，也嘗試初始化工具
            # 這樣可以在沒有 Ollama 的情況下仍然使用交易功能
            if not base_init_success:
                logger.warning("base_agent_initialization_failed_but_continuing")

            # 初始化工具 (使用容錯機制)
            tools_initialized = await self._initialize_tools_with_fallback()

            # 如果至少有一個組件成功初始化，就認為是成功的
            success = base_init_success or tools_initialized

            if success:
                logger.info("trading_executor_agent_initialized",
                           name=self.config.name,
                           base_agent=base_init_success,
                           tools=tools_initialized)
                self.initialized = True
                return True
            else:
                logger.error("trading_executor_agent_complete_initialization_failed")
                return False

        except Exception as e:
            logger.error("trading_executor_agent_initialization_failed", error=str(e))
            return False
    
    async def _initialize_tools_with_fallback(self) -> bool:
        """初始化交易工具 (容錯版本)"""
        tools_success = []

        # Jupiter Swap Tool
        try:
            jupiter_config = {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'jupiter_api': 'https://quote-api.jup.ag/v6',
                'default_slippage_bps': 50,
                'max_accounts': 64
            }
            self.jupiter_tool = JupiterSwapTool(jupiter_config)
            await self.jupiter_tool.initialize()
            tools_success.append("jupiter")
            logger.info("jupiter_tool_initialized")
        except Exception as e:
            logger.warning("jupiter_tool_initialization_failed", error=str(e))
            self.jupiter_tool = None

        # Meteora DLMM Tool
        try:
            meteora_config = {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'api_base': 'https://dlmm-api.meteora.ag',
                'program_id': 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'
            }
            self.meteora_tool = MeteoraDLMMTool(meteora_config)
            await self.meteora_tool.initialize()
            tools_success.append("meteora")
            logger.info("meteora_tool_initialized")
        except Exception as e:
            logger.warning("meteora_tool_initialization_failed", error=str(e))
            self.meteora_tool = None

        # Supabase DB Tool
        try:
            self.db_tool = SupabaseDbTool()
            tools_success.append("supabase")
            logger.info("supabase_tool_initialized")
        except Exception as e:
            logger.warning("supabase_tool_initialization_failed", error=str(e))
            self.db_tool = None

        success = len(tools_success) > 0
        logger.info("trading_tools_initialization_completed",
                   successful_tools=tools_success,
                   total_success=success)

        return success

    async def _initialize_tools(self):
        """初始化交易工具 (原版本，保持向後兼容)"""
        success = await self._initialize_tools_with_fallback()
        if not success:
            raise DyFlowException("Failed to initialize any trading tools")
    
    def _get_agent_role(self) -> str:
        """獲取 Agent 角色描述"""
        return "DyFlow 交易執行專家"
    
    def _get_agent_instructions(self) -> List[str]:
        """獲取 Agent 指令"""
        return [
            "你是 DyFlow 系統的交易執行專家。",
            "負責執行各種交易操作，包括：",
            "1. Solana 鏈上的代幣交換 (使用 Jupiter 聚合器)",
            "2. DLMM LP 策略部署 (四種策略類型)",
            "3. LP 頭寸管理 (開倉、收割、平倉)",
            "4. SOL <-> DLMM LP 頭寸的完整循環",
            "5. DCA 和批量交易執行",
            "始終確保交易安全性和最優執行。",
            "提供詳細的執行報告和風險評估。",
            "支持緊急情況下的快速平倉操作。"
        ]
    
    async def _get_agent_tools(self) -> List[Any]:
        """獲取 Agent 工具"""
        return []  # 工具通過直接調用管理，不通過 Agno 框架
    
    # ========== 主要交易功能 ==========
    
    async def execute_trading_request(self, request: TradingRequest) -> TradingResult:
        """執行交易請求"""
        start_time = datetime.now()
        
        try:
            logger.info("trading_request_started",
                       action=request.action.value,
                       priority=request.priority,
                       user_id=request.user_id)
            
            # 根據動作類型執行相應操作
            if request.action == TradingAction.SWAP:
                result_data = await self._execute_swap(request.parameters)
            elif request.action == TradingAction.DEPLOY_LP:
                result_data = await self._deploy_lp_strategy(request.parameters)
            elif request.action == TradingAction.HARVEST_FEES:
                result_data = await self._harvest_fees(request.parameters)
            elif request.action == TradingAction.EXIT_POSITION:
                result_data = await self._exit_position(request.parameters)
            elif request.action == TradingAction.DCA_SWAP:
                result_data = await self._execute_dca_swap(request.parameters)
            elif request.action == TradingAction.BATCH_SWAP:
                result_data = await self._execute_batch_swap(request.parameters)
            else:
                raise DyFlowException(f"Unsupported trading action: {request.action}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 更新統計
            self.execution_stats["total_trades"] += 1
            if result_data.get("success", False):
                self.execution_stats["successful_trades"] += 1
            else:
                self.execution_stats["failed_trades"] += 1
            
            return TradingResult(
                success=result_data.get("success", False),
                action=request.action,
                data=result_data,
                execution_time=execution_time,
                timestamp=get_utc_timestamp()
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error("trading_request_failed",
                        action=request.action.value,
                        error=str(e),
                        execution_time=execution_time)
            
            self.execution_stats["total_trades"] += 1
            self.execution_stats["failed_trades"] += 1
            
            return TradingResult(
                success=False,
                action=request.action,
                error=str(e),
                execution_time=execution_time,
                timestamp=get_utc_timestamp()
            )
    
    async def _execute_swap(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """執行代幣交換"""
        try:
            if not self.jupiter_tool:
                raise DyFlowException("Jupiter tool not initialized")
            
            # 獲取報價
            quote = await self.jupiter_tool.get_quote(
                input_mint=params["input_mint"],
                output_mint=params["output_mint"],
                amount=params["amount"],
                slippage_bps=params.get("slippage_bps", 50)
            )
            
            if not quote:
                return {"success": False, "error": "Failed to get quote"}
            
            # 執行交換
            result = await self.jupiter_tool.execute_swap(
                quote=quote,
                user_public_key=params["user_public_key"],
                priority_fee_lamports=params.get("priority_fee", 0)
            )
            
            # 記錄到數據庫
            if self.db_tool and result.success:
                await self._record_swap_to_db(params, result)
            
            return {
                "success": result.success,
                "signature": result.signature,
                "input_amount": result.input_amount,
                "output_amount": result.output_amount,
                "price_impact": result.price_impact,
                "error": result.error
            }
            
        except Exception as e:
            logger.error("execute_swap_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def _deploy_lp_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """部署 LP 策略"""
        try:
            if not self.meteora_tool:
                raise DyFlowException("Meteora tool not initialized")

            # 創建策略參數
            strategy_params = StrategyFactory.create_strategy_params(
                strategy_type=params["strategy_type"],
                token_amount=params["token_amount"],
                token_mint=params["token_mint"],
                **params.get("strategy_config", {})
            )

            # 驗證策略參數
            if not validate_strategy_params(strategy_params):
                return {"success": False, "error": "Invalid strategy parameters"}

            # 部署策略
            result = await self.meteora_tool.deploy_strategy(
                pool_address=params["pool_address"],
                strategy_params=strategy_params
            )

            # 記錄到數據庫
            if self.db_tool and result.get("success"):
                await self._record_lp_deployment_to_db(params, result)
                self.execution_stats["strategies_deployed"] += 1

            return result

        except Exception as e:
            logger.error("deploy_lp_strategy_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def _harvest_fees(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """收割手續費"""
        try:
            if not self.meteora_tool:
                raise DyFlowException("Meteora tool not initialized")

            result = await self.meteora_tool.harvest_fees(
                pool_address=params["pool_address"],
                wallet_address=params["wallet_address"]
            )

            # 更新統計
            if result.get("success") and "fees_harvested" in result:
                fees_usd = sum(result["fees_harvested"].values())
                self.execution_stats["fees_harvested"] += fees_usd

            # 記錄到數據庫
            if self.db_tool and result.get("success"):
                await self._record_harvest_to_db(params, result)

            return result

        except Exception as e:
            logger.error("harvest_fees_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def _exit_position(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """退出持倉"""
        try:
            if not self.meteora_tool:
                raise DyFlowException("Meteora tool not initialized")

            result = await self.meteora_tool.exit_position(
                pool_address=params["pool_address"],
                wallet_address=params["wallet_address"],
                percentage=params.get("percentage", 1.0)
            )

            # 記錄到數據庫
            if self.db_tool and result.get("success"):
                await self._record_exit_to_db(params, result)

            return result

        except Exception as e:
            logger.error("exit_position_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def _execute_dca_swap(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """執行 DCA 交換"""
        try:
            if not self.jupiter_tool:
                raise DyFlowException("Jupiter tool not initialized")

            dca_params = DCAParams(
                input_mint=params["input_mint"],
                output_mint=params["output_mint"],
                total_amount=params["total_amount"],
                intervals=params["intervals"],
                interval_seconds=params.get("interval_seconds", 60),
                slippage_bps=params.get("slippage_bps", 50),
                user_public_key=params["user_public_key"],
                priority_fee_lamports=params.get("priority_fee", 0)
            )

            result = await self.jupiter_tool.execute_dca_swap(dca_params)

            # 記錄到數據庫
            if self.db_tool and result.get("success"):
                await self._record_dca_to_db(params, result)

            return result

        except Exception as e:
            logger.error("execute_dca_swap_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def _execute_batch_swap(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """執行批量交換"""
        try:
            if not self.jupiter_tool:
                raise DyFlowException("Jupiter tool not initialized")

            swap_requests = params["swap_requests"]
            results = await self.jupiter_tool.execute_batch_swap(swap_requests)

            successful_swaps = sum(1 for r in results if r.success)

            batch_result = {
                "success": successful_swaps > 0,
                "total_swaps": len(swap_requests),
                "successful_swaps": successful_swaps,
                "failed_swaps": len(swap_requests) - successful_swaps,
                "results": [
                    {
                        "success": r.success,
                        "signature": r.signature,
                        "input_amount": r.input_amount,
                        "output_amount": r.output_amount,
                        "error": r.error
                    } for r in results
                ]
            }

            # 記錄到數據庫
            if self.db_tool:
                await self._record_batch_swap_to_db(params, batch_result)

            return batch_result

        except Exception as e:
            logger.error("execute_batch_swap_failed", error=str(e))
            return {"success": False, "error": str(e)}

    # ========== 完整循環功能 ==========

    async def sol_to_dlmm_lp_cycle(self, sol_amount: float, pool_address: str,
                                  strategy_type: str, **strategy_config) -> Dict[str, Any]:
        """SOL -> DLMM LP 頭寸完整循環"""
        try:
            logger.info("sol_to_dlmm_cycle_started",
                       sol_amount=sol_amount,
                       pool=pool_address,
                       strategy=strategy_type)

            # 1. 獲取池子信息
            pool_info = await self.meteora_tool.get_pool_info(pool_address)
            if not pool_info:
                return {"success": False, "error": "Pool not found"}

            # 2. 確定目標代幣 (假設是池子的 token_x 或 token_y)
            target_token = pool_info.mint_x  # 可以根據策略選擇

            # 3. SOL -> 目標代幣
            if not self.jupiter_tool.wallet_keypair:
                return {"success": False, "error": "Wallet not configured"}

            swap_result = await self.jupiter_tool.sol_to_token_swap(
                token_mint=target_token,
                sol_amount=sol_amount
            )

            if not swap_result.success:
                return {
                    "success": False,
                    "error": f"SOL to token swap failed: {swap_result.error}",
                    "step": "swap"
                }

            # 4. 部署 LP 策略
            strategy_params = StrategyFactory.create_strategy_params(
                strategy_type=strategy_type,
                token_amount=swap_result.output_amount or 0,
                token_mint=target_token,
                **strategy_config
            )

            lp_result = await self.meteora_tool.deploy_strategy(
                pool_address=pool_address,
                strategy_params=strategy_params
            )

            if not lp_result.get("success"):
                return {
                    "success": False,
                    "error": f"LP deployment failed: {lp_result.get('error')}",
                    "step": "lp_deployment",
                    "swap_result": swap_result.__dict__
                }

            # 5. 記錄完整循環
            cycle_result = {
                "success": True,
                "sol_amount": sol_amount,
                "token_amount": swap_result.output_amount,
                "target_token": target_token,
                "pool_address": pool_address,
                "strategy_type": strategy_type,
                "swap_signature": swap_result.signature,
                "lp_signature": lp_result.get("signature"),
                "timestamp": get_utc_timestamp()
            }

            if self.db_tool:
                await self._record_cycle_to_db("sol_to_lp", cycle_result)

            return cycle_result

        except Exception as e:
            logger.error("sol_to_dlmm_cycle_failed", error=str(e))
            return {"success": False, "error": str(e)}

    async def dlmm_lp_to_sol_cycle(self, pool_address: str, wallet_address: str,
                                  exit_percentage: float = 1.0) -> Dict[str, Any]:
        """DLMM LP 頭寸 -> SOL 完整循環"""
        try:
            logger.info("dlmm_to_sol_cycle_started",
                       pool=pool_address,
                       wallet=wallet_address,
                       exit_percentage=exit_percentage)

            # 1. 收割手續費 (如果有)
            harvest_result = await self.meteora_tool.harvest_fees(
                pool_address=pool_address,
                wallet_address=wallet_address
            )

            # 2. 退出 LP 持倉
            exit_result = await self.meteora_tool.exit_position(
                pool_address=pool_address,
                wallet_address=wallet_address,
                percentage=exit_percentage
            )

            if not exit_result.get("success"):
                return {
                    "success": False,
                    "error": f"LP exit failed: {exit_result.get('error')}",
                    "step": "lp_exit",
                    "harvest_result": harvest_result
                }

            # 3. 獲取池子信息以確定代幣類型
            pool_info = await self.meteora_tool.get_pool_info(pool_address)
            if not pool_info:
                return {"success": False, "error": "Pool not found"}

            # 4. 將獲得的代幣轉換為 SOL
            # 這裡需要檢查退出後獲得的代幣餘額
            token_balances = []

            if self.jupiter_tool.wallet_keypair:
                wallet_key = str(self.jupiter_tool.wallet_keypair.public_key)

                # 檢查 token_x 餘額
                balance_x = await self.meteora_tool.get_token_balance(wallet_key, pool_info.mint_x)
                if balance_x > 0:
                    token_balances.append({"mint": pool_info.mint_x, "amount": balance_x})

                # 檢查 token_y 餘額
                balance_y = await self.meteora_tool.get_token_balance(wallet_key, pool_info.mint_y)
                if balance_y > 0:
                    token_balances.append({"mint": pool_info.mint_y, "amount": balance_y})

            # 5. 將代幣轉換為 SOL
            swap_results = []
            total_sol_received = 0.0

            for token_balance in token_balances:
                if token_balance["mint"] == "So11111111111111111111111111111111111111112":
                    # 已經是 SOL，直接計算
                    total_sol_received += token_balance["amount"] / 1e9
                else:
                    # 轉換為 SOL
                    swap_result = await self.jupiter_tool.token_to_sol_swap(
                        token_mint=token_balance["mint"],
                        token_amount=token_balance["amount"]
                    )

                    swap_results.append({
                        "token_mint": token_balance["mint"],
                        "token_amount": token_balance["amount"],
                        "result": swap_result
                    })

                    if swap_result.success and swap_result.output_amount:
                        total_sol_received += swap_result.output_amount / 1e9

            # 6. 記錄完整循環
            cycle_result = {
                "success": True,
                "pool_address": pool_address,
                "exit_percentage": exit_percentage,
                "harvest_fees": harvest_result.get("fees_harvested", {}),
                "token_balances": token_balances,
                "swap_results": swap_results,
                "total_sol_received": total_sol_received,
                "harvest_signature": harvest_result.get("signature"),
                "exit_signature": exit_result.get("signature"),
                "timestamp": get_utc_timestamp()
            }

            if self.db_tool:
                await self._record_cycle_to_db("lp_to_sol", cycle_result)

            return cycle_result

        except Exception as e:
            logger.error("dlmm_to_sol_cycle_failed", error=str(e))
            return {"success": False, "error": str(e)}

    # ========== 數據庫記錄功能 ==========

    async def _record_swap_to_db(self, params: Dict[str, Any], result: SwapResult):
        """記錄交換到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "swap",
                "input_mint": params["input_mint"],
                "output_mint": params["output_mint"],
                "input_amount": result.input_amount,
                "output_amount": result.output_amount,
                "price_impact": result.price_impact,
                "signature": result.signature,
                "user_public_key": params.get("user_public_key"),
                "timestamp": result.timestamp
            }

            await self.db_tool.insert_record("trading_history", record)

        except Exception as e:
            logger.error("record_swap_to_db_failed", error=str(e))

    async def _record_lp_deployment_to_db(self, params: Dict[str, Any], result: Dict[str, Any]):
        """記錄 LP 部署到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "lp_deployment",
                "pool_address": params["pool_address"],
                "strategy_type": params["strategy_type"],
                "token_amount": params["token_amount"],
                "token_mint": params["token_mint"],
                "signature": result.get("signature"),
                "bin_distribution": result.get("bin_distribution"),
                "timestamp": result.get("timestamp")
            }

            await self.db_tool.insert_record("lp_positions", record)

        except Exception as e:
            logger.error("record_lp_deployment_to_db_failed", error=str(e))

    async def _record_harvest_to_db(self, params: Dict[str, Any], result: Dict[str, Any]):
        """記錄收割到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "harvest",
                "pool_address": params["pool_address"],
                "wallet_address": params["wallet_address"],
                "fees_harvested": result.get("fees_harvested"),
                "signature": result.get("signature"),
                "timestamp": result.get("timestamp")
            }

            await self.db_tool.insert_record("trading_history", record)

        except Exception as e:
            logger.error("record_harvest_to_db_failed", error=str(e))

    async def _record_exit_to_db(self, params: Dict[str, Any], result: Dict[str, Any]):
        """記錄退出到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "exit",
                "pool_address": params["pool_address"],
                "wallet_address": params["wallet_address"],
                "exit_percentage": params.get("percentage", 1.0),
                "exit_shares": result.get("exit_shares"),
                "signature": result.get("signature"),
                "timestamp": result.get("timestamp")
            }

            await self.db_tool.insert_record("trading_history", record)

        except Exception as e:
            logger.error("record_exit_to_db_failed", error=str(e))

    async def _record_dca_to_db(self, params: Dict[str, Any], result: Dict[str, Any]):
        """記錄 DCA 到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "dca",
                "input_mint": params["input_mint"],
                "output_mint": params["output_mint"],
                "total_amount": params["total_amount"],
                "intervals": params["intervals"],
                "successful_intervals": result.get("successful_intervals"),
                "total_input_amount": result.get("total_input_amount"),
                "total_output_amount": result.get("total_output_amount"),
                "average_price": result.get("average_price"),
                "timestamp": result.get("timestamp")
            }

            await self.db_tool.insert_record("trading_history", record)

        except Exception as e:
            logger.error("record_dca_to_db_failed", error=str(e))

    async def _record_batch_swap_to_db(self, params: Dict[str, Any], result: Dict[str, Any]):
        """記錄批量交換到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": "batch_swap",
                "total_swaps": result.get("total_swaps"),
                "successful_swaps": result.get("successful_swaps"),
                "failed_swaps": result.get("failed_swaps"),
                "swap_requests": params["swap_requests"],
                "results": result.get("results"),
                "timestamp": get_utc_timestamp()
            }

            await self.db_tool.insert_record("trading_history", record)

        except Exception as e:
            logger.error("record_batch_swap_to_db_failed", error=str(e))

    async def _record_cycle_to_db(self, cycle_type: str, result: Dict[str, Any]):
        """記錄完整循環到數據庫"""
        try:
            if not self.db_tool:
                return

            record = {
                "type": f"cycle_{cycle_type}",
                "cycle_data": result,
                "timestamp": result.get("timestamp")
            }

            await self.db_tool.insert_record("trading_cycles", record)

        except Exception as e:
            logger.error("record_cycle_to_db_failed", error=str(e))

    # ========== 工具函數 ==========

    def set_wallet(self, private_key: str):
        """設置錢包"""
        try:
            if self.jupiter_tool:
                self.jupiter_tool.set_wallet(private_key)
            if self.meteora_tool:
                self.meteora_tool.set_wallet(private_key)
            logger.info("wallet_configured_for_trading_agent")
        except Exception as e:
            logger.error("wallet_configuration_failed", error=str(e))
            raise DyFlowException(f"Failed to configure wallet: {e}")

    def get_execution_stats(self) -> Dict[str, Any]:
        """獲取執行統計"""
        return self.execution_stats.copy()

    async def get_supported_strategies(self) -> List[Dict[str, Any]]:
        """獲取支持的策略列表"""
        strategies = []

        for strategy_type in DLMMStrategyType:
            config = get_strategy_config(strategy_type)
            if config:
                strategies.append({
                    "type": strategy_type.value,
                    "name": config.name,
                    "description": config.description,
                    "risk_level": config.risk_level,
                    "min_capital": config.min_capital,
                    "expected_apr": config.expected_apr,
                    "max_impermanent_loss": config.max_impermanent_loss,
                    "rebalance_frequency": config.rebalance_frequency
                })

        return strategies

    async def cleanup(self):
        """清理資源"""
        try:
            if self.jupiter_tool:
                await self.jupiter_tool.cleanup()
            if self.meteora_tool:
                await self.meteora_tool.cleanup()
            logger.info("trading_executor_agent_cleanup_completed")
        except Exception as e:
            logger.error("trading_executor_agent_cleanup_failed", error=str(e))
