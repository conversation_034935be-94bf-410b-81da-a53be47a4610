"""
Portfolio API - 投資組合數據 API
提供投資組合狀態、持倉信息和階段監控數據
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import asyncio
import structlog
from datetime import datetime, timezone
import random

logger = structlog.get_logger(__name__)

router = APIRouter()

# 模擬投資組合數據
MOCK_POSITIONS = [
    {
        "id": "pos_sol_usdc_001",
        "pool": "SOL/USDC",
        "pool_address": "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2",
        "chain": "solana",
        "protocol": "meteora_damm_v2",
        "strategy": "SPOT_IMBALANCED_DAMM",
        "liquidity_usd": 5000.0,
        "token0_amount": 25.5,
        "token1_amount": 1200.0,
        "pnl_usd": 125.50,
        "il_usd": -45.20,
        "il_percentage": -0.904,  # -0.904%
        "apr": 185.5,
        "fees_24h_usd": 25.50,
        "fees_total_usd": 156.80,
        "opened_at": "2025-06-15T10:30:00Z",
        "status": "active",
        "risk_level": "medium",
        "price_range": {
            "lower": 95.5,
            "upper": 125.8,
            "current": 110.2
        }
    },
    {
        "id": "pos_bnb_usdt_002", 
        "pool": "BNB/USDT",
        "pool_address": "0x36696169c63e42cd08ce11f5deebbcebae652050",
        "chain": "bsc",
        "protocol": "pancakeswap_v3",
        "strategy": "CURVE_BALANCED",
        "liquidity_usd": 3000.0,
        "token0_amount": 8.2,
        "token1_amount": 2850.0,
        "pnl_usd": 85.30,
        "il_usd": -20.15,
        "il_percentage": -0.672,  # -0.672%
        "apr": 145.2,
        "fees_24h_usd": 12.30,
        "fees_total_usd": 89.45,
        "opened_at": "2025-06-14T15:45:00Z",
        "status": "active",
        "risk_level": "low",
        "price_range": {
            "lower": 340.0,
            "upper": 380.0,
            "current": 347.8
        }
    },
    {
        "id": "pos_bonk_sol_003",
        "pool": "BONK/SOL", 
        "pool_address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
        "chain": "solana",
        "protocol": "meteora_damm_v2",
        "strategy": "SPOT_IMBALANCED_DAMM",
        "liquidity_usd": 2500.0,
        "token0_amount": 125000000.0,  # BONK has many decimals
        "token1_amount": 22.5,
        "pnl_usd": -35.80,
        "il_usd": -85.60,
        "il_percentage": -3.424,  # -3.424%
        "apr": 425.8,
        "fees_24h_usd": 28.90,
        "fees_total_usd": 145.20,
        "opened_at": "2025-06-16T08:20:00Z",
        "status": "active",
        "risk_level": "high",
        "price_range": {
            "lower": 0.000018,
            "upper": 0.000025,
            "current": 0.000020
        }
    }
]

# 模擬階段狀態
MOCK_PHASE_STATUS = {
    0: {
        "status": "completed",
        "started_at": "2025-06-16T04:00:00Z",
        "completed_at": "2025-06-16T04:00:05Z",
        "error": None
    },
    1: {
        "status": "completed", 
        "started_at": "2025-06-16T04:00:05Z",
        "completed_at": "2025-06-16T04:00:15Z",
        "error": None
    },
    2: {
        "status": "completed",
        "started_at": "2025-06-16T04:00:15Z", 
        "completed_at": "2025-06-16T04:00:20Z",
        "error": None
    },
    3: {
        "status": "completed",
        "started_at": "2025-06-16T04:00:20Z",
        "completed_at": "2025-06-16T04:00:35Z", 
        "error": None
    },
    4: {
        "status": "completed",
        "started_at": "2025-06-16T04:00:35Z",
        "completed_at": "2025-06-16T04:00:45Z",
        "error": None
    },
    5: {
        "status": "completed",
        "started_at": "2025-06-16T04:00:45Z",
        "completed_at": "2025-06-16T04:00:50Z",
        "error": None
    },
    6: {
        "status": "running",
        "started_at": "2025-06-16T04:00:50Z",
        "completed_at": None,
        "error": None
    },
    7: {
        "status": "pending",
        "started_at": None,
        "completed_at": None,
        "error": None
    },
    8: {
        "status": "pending",
        "started_at": None,
        "completed_at": None,
        "error": None
    }
}

@router.get("/portfolio/status")
async def get_portfolio_status():
    """獲取投資組合總體狀態"""
    try:
        # 計算投資組合指標
        total_value = sum(pos["liquidity_usd"] for pos in MOCK_POSITIONS)
        total_pnl = sum(pos["pnl_usd"] for pos in MOCK_POSITIONS)
        total_il = sum(pos["il_usd"] for pos in MOCK_POSITIONS)
        avg_apr = sum(pos["apr"] for pos in MOCK_POSITIONS) / len(MOCK_POSITIONS)
        fees_24h = sum(pos["fees_24h_usd"] for pos in MOCK_POSITIONS)
        
        # 風險評分計算
        il_percentage = (total_il / total_value) * 100 if total_value > 0 else 0
        risk_score = max(0, min(100, 100 - abs(il_percentage) * 10))
        
        # 添加一些隨機波動來模擬實時數據
        variation = random.uniform(-0.05, 0.05)  # ±5% 變動
        
        portfolio_data = {
            "total_value_usd": total_value * (1 + variation),
            "total_pnl_usd": total_pnl * (1 + variation * 2),
            "total_il_usd": total_il * (1 + variation * 0.5),
            "avg_apr": avg_apr,
            "fees_24h_usd": fees_24h * (1 + variation),
            "risk_score": risk_score,
            "pnl_percentage": (total_pnl / total_value) * 100 if total_value > 0 else 0,
            "il_percentage": il_percentage,
            "positions_count": len(MOCK_POSITIONS),
            "active_strategies": list(set(pos["strategy"] for pos in MOCK_POSITIONS)),
            "chains": list(set(pos["chain"] for pos in MOCK_POSITIONS)),
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info("portfolio_status_requested", 
                   total_value=portfolio_data["total_value_usd"],
                   pnl=portfolio_data["total_pnl_usd"],
                   il=portfolio_data["total_il_usd"])
        
        return portfolio_data
        
    except Exception as e:
        logger.error("portfolio_status_error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions")
async def get_positions():
    """獲取所有 LP 持倉"""
    try:
        # 添加實時價格變動
        positions = []
        for pos in MOCK_POSITIONS:
            # 模擬價格變動
            price_change = random.uniform(-0.02, 0.02)  # ±2% 價格變動
            
            updated_pos = pos.copy()
            updated_pos["price_range"]["current"] *= (1 + price_change)
            
            # 重新計算 PnL 和 IL
            updated_pos["pnl_usd"] *= (1 + price_change * 0.5)
            updated_pos["il_usd"] *= (1 + abs(price_change) * 0.3)
            
            positions.append(updated_pos)
        
        logger.info("positions_requested", count=len(positions))
        return positions
        
    except Exception as e:
        logger.error("positions_error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/phases/status")
async def get_phases_status():
    """獲取八階段啟動序列狀態"""
    try:
        # 模擬階段進展
        current_time = datetime.now(timezone.utc)
        
        # 隨機推進階段 (模擬實時進展)
        current_phase = 6  # 當前在 Phase 6
        
        phase_data = {
            "current_phase": current_phase,
            "phase_status": MOCK_PHASE_STATUS,
            "total_phases": 9,
            "completed_phases": sum(1 for status in MOCK_PHASE_STATUS.values() if status["status"] == "completed"),
            "progress_percentage": (sum(1 for status in MOCK_PHASE_STATUS.values() if status["status"] == "completed") / 9) * 100,
            "last_updated": current_time.isoformat()
        }
        
        logger.info("phases_status_requested", 
                   current_phase=current_phase,
                   completed=phase_data["completed_phases"])
        
        return phase_data
        
    except Exception as e:
        logger.error("phases_status_error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/positions/{position_id}/close")
async def close_position(position_id: str):
    """關閉特定 LP 持倉 (模擬)"""
    try:
        # 查找持倉
        position = next((pos for pos in MOCK_POSITIONS if pos["id"] == position_id), None)
        if not position:
            raise HTTPException(status_code=404, detail="Position not found")
        
        # 模擬關閉操作
        close_result = {
            "position_id": position_id,
            "status": "closing",
            "estimated_completion": (datetime.now(timezone.utc).timestamp() + 30),  # 30秒後完成
            "tx_hash": f"0x{''.join([hex(random.randint(0, 15))[2:] for _ in range(64)])}",
            "estimated_gas": random.randint(150000, 300000),
            "message": "Position close transaction submitted"
        }
        
        logger.info("position_close_requested", 
                   position_id=position_id,
                   tx_hash=close_result["tx_hash"])
        
        return close_result
        
    except Exception as e:
        logger.error("position_close_error", position_id=position_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/risk/assessment")
async def get_risk_assessment():
    """獲取風險評估數據"""
    try:
        # 計算各種風險指標
        total_value = sum(pos["liquidity_usd"] for pos in MOCK_POSITIONS)
        total_il = sum(pos["il_usd"] for pos in MOCK_POSITIONS)
        
        # VaR 計算 (模擬)
        var_95 = total_value * 0.05  # 5% VaR
        
        risk_data = {
            "il_net_percentage": (total_il / total_value) * 100 if total_value > 0 else 0,
            "var_95_usd": var_95,
            "var_95_percentage": 5.0,
            "volatility_1m": random.uniform(2.0, 8.0),  # 1分鐘波動率
            "health_score": random.uniform(0.7, 0.9),   # 健康分數
            "risk_alerts": [
                {
                    "level": "warning",
                    "message": "BONK/SOL 池子 IL 超過 -3%",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            ] if total_il < -100 else [],
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info("risk_assessment_requested", 
                   il_net=risk_data["il_net_percentage"],
                   var_95=risk_data["var_95_usd"])
        
        return risk_data
        
    except Exception as e:
        logger.error("risk_assessment_error", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
