#!/usr/bin/env python3
"""
专注交易对策略
BSC: BNB/USDC, BNB/USDT 交易对
Solana: SOL/USDC 交易对
退出时确保退出为BNB或SOL
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import structlog

logger = structlog.get_logger(__name__)

@dataclass
class FocusedPair:
    """专注交易对配置"""
    chain: str
    base_token: str  # BNB or SOL
    quote_tokens: List[str]  # USDC, USDT
    min_tvl: float
    min_volume_24h: float
    max_positions: int
    exit_token: str  # 退出时的目标代币

class FocusedPairsStrategy:
    """专注交易对策略"""
    
    def __init__(self):
        # 定义专注的交易对
        self.focused_pairs = {
            "bsc": FocusedPair(
                chain="bsc",
                base_token="BNB",
                quote_tokens=["USDC", "USDT"],
                min_tvl=100000,
                min_volume_24h=50000,
                max_positions=5,
                exit_token="BNB"
            ),
            "solana": FocusedPair(
                chain="solana", 
                base_token="SOL",
                quote_tokens=["USDC"],
                min_tvl=100000,
                min_volume_24h=50000,
                max_positions=5,
                exit_token="SOL"
            )
        }
        
        # 策略配置
        self.config = {
            "max_slippage": 0.01,  # 1%
            "stop_loss_threshold": 0.15,  # 15%
            "emergency_exit_threshold": 0.10,  # 10%
            "rebalance_threshold": 0.02,  # 2%
            "max_position_size": 0.20,  # 20%
            "reserve_ratio": 0.10,  # 10%
            "risk_check_interval": 300,  # 5分钟
        }
        
        logger.info("focused_pairs_strategy_initialized")
    
    def is_valid_pair(self, chain: str, token0: str, token1: str) -> bool:
        """检查是否为有效的专注交易对"""
        if chain not in self.focused_pairs:
            return False
        
        focused_pair = self.focused_pairs[chain]
        base_token = focused_pair.base_token
        quote_tokens = focused_pair.quote_tokens
        
        # 检查是否为目标交易对
        valid_combinations = [
            (base_token, quote) for quote in quote_tokens
        ] + [
            (quote, base_token) for quote in quote_tokens
        ]
        
        return (token0, token1) in valid_combinations or (token1, token0) in valid_combinations
    
    def get_target_pairs(self, chain: str) -> List[Tuple[str, str]]:
        """获取目标交易对列表"""
        if chain not in self.focused_pairs:
            return []
        
        focused_pair = self.focused_pairs[chain]
        base_token = focused_pair.base_token
        quote_tokens = focused_pair.quote_tokens
        
        return [(base_token, quote) for quote in quote_tokens]
    
    def filter_pools_by_strategy(self, pools: List[Any], chain: str) -> List[Any]:
        """根据策略过滤池子"""
        if chain not in self.focused_pairs:
            return []
        
        focused_pair = self.focused_pairs[chain]
        filtered_pools = []
        
        for pool in pools:
            # 获取代币符号
            if hasattr(pool, 'token0') and hasattr(pool, 'token1'):
                token0_symbol = pool.token0.get('symbol', '') if isinstance(pool.token0, dict) else getattr(pool.token0, 'symbol', '')
                token1_symbol = pool.token1.get('symbol', '') if isinstance(pool.token1, dict) else getattr(pool.token1, 'symbol', '')
            else:
                # 对于Meteora池子
                token0_symbol = getattr(pool, 'mint_x_symbol', '')
                token1_symbol = getattr(pool, 'mint_y_symbol', '')
            
            # 检查是否为目标交易对
            if not self.is_valid_pair(chain, token0_symbol, token1_symbol):
                continue
            
            # 检查TVL要求
            tvl = getattr(pool, 'tvl_usd', 0) or getattr(pool, 'liquidity', 0)
            if tvl < focused_pair.min_tvl:
                continue
            
            # 检查交易量要求
            volume = getattr(pool, 'volume_usd_24h', 0) or getattr(pool, 'trade_volume_24h', 0)
            if volume < focused_pair.min_volume_24h:
                continue
            
            filtered_pools.append(pool)
        
        # 按TVL排序，取前N个
        filtered_pools.sort(key=lambda x: getattr(x, 'tvl_usd', 0) or getattr(x, 'liquidity', 0), reverse=True)
        return filtered_pools[:focused_pair.max_positions]
    
    def calculate_position_size(self, pool_info: Dict[str, Any], total_portfolio_value: float) -> float:
        """计算持仓大小"""
        # 基础持仓大小
        base_size = total_portfolio_value * self.config["max_position_size"]
        
        # 根据池子质量调整
        pool_score = pool_info.get('quality_score', 50) / 100  # 假设评分0-100
        adjusted_size = base_size * pool_score
        
        # 确保不超过最大限制
        max_size = total_portfolio_value * self.config["max_position_size"]
        return min(adjusted_size, max_size)
    
    def should_exit_position(self, position_info: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否应该退出持仓"""
        pnl_percentage = position_info.get('pnl_percentage', 0)
        impermanent_loss = position_info.get('impermanent_loss', 0)
        
        # 止损检查
        if pnl_percentage <= -self.config["stop_loss_threshold"] * 100:
            return True, "STOP_LOSS"
        
        # 紧急退出检查
        if pnl_percentage <= -self.config["emergency_exit_threshold"] * 100:
            return True, "EMERGENCY_EXIT"
        
        # 无常损失检查
        if abs(impermanent_loss) >= 8.0:  # 8%无常损失
            return True, "IMPERMANENT_LOSS"
        
        return False, "HOLD"
    
    def get_exit_strategy(self, chain: str, position_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取退出策略"""
        if chain not in self.focused_pairs:
            return {}
        
        focused_pair = self.focused_pairs[chain]
        exit_token = focused_pair.exit_token
        
        return {
            "exit_token": exit_token,
            "exit_method": "SWAP_TO_BASE",
            "max_slippage": self.config["max_slippage"],
            "priority": "HIGH" if position_info.get('pnl_percentage', 0) < -10 else "NORMAL"
        }
    
    def generate_rebalance_plan(self, current_positions: List[Dict[str, Any]], 
                              target_pools: List[Any], 
                              total_portfolio_value: float) -> Dict[str, Any]:
        """生成重平衡计划"""
        plan = {
            "timestamp": datetime.now().isoformat(),
            "actions": [],
            "total_value": total_portfolio_value,
            "reserve_amount": total_portfolio_value * self.config["reserve_ratio"]
        }
        
        # 检查需要退出的持仓
        for position in current_positions:
            should_exit, reason = self.should_exit_position(position)
            if should_exit:
                chain = position.get('chain', 'unknown')
                exit_strategy = self.get_exit_strategy(chain, position)
                
                plan["actions"].append({
                    "type": "EXIT",
                    "position_id": position.get('position_id'),
                    "reason": reason,
                    "exit_strategy": exit_strategy,
                    "priority": "HIGH" if reason in ["EMERGENCY_EXIT", "STOP_LOSS"] else "MEDIUM"
                })
        
        # 检查新的入场机会
        available_capital = total_portfolio_value * (1 - self.config["reserve_ratio"])
        used_capital = sum(pos.get('usd_value', 0) for pos in current_positions)
        free_capital = available_capital - used_capital
        
        for pool in target_pools:
            # 检查是否已有持仓
            pool_address = getattr(pool, 'address', '')
            existing_position = any(
                pos.get('pool_address') == pool_address for pos in current_positions
            )
            
            if not existing_position and free_capital > 1000:  # 至少$1000
                position_size = self.calculate_position_size(
                    {"quality_score": 75},  # 默认评分
                    total_portfolio_value
                )
                
                if position_size <= free_capital:
                    chain = 'bsc' if hasattr(pool, 'token0') else 'solana'
                    
                    plan["actions"].append({
                        "type": "ENTER",
                        "pool_address": pool_address,
                        "chain": chain,
                        "position_size": position_size,
                        "priority": "LOW"
                    })
                    
                    free_capital -= position_size
        
        return plan
    
    def validate_strategy_compliance(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证策略合规性"""
        compliance_report = {
            "timestamp": datetime.now().isoformat(),
            "is_compliant": True,
            "violations": [],
            "recommendations": []
        }
        
        total_value = portfolio_data.get('total_value', 0)
        positions = portfolio_data.get('positions', [])
        
        # 检查持仓集中度
        for position in positions:
            position_ratio = position.get('usd_value', 0) / total_value if total_value > 0 else 0
            if position_ratio > self.config["max_position_size"]:
                compliance_report["violations"].append({
                    "type": "POSITION_SIZE_VIOLATION",
                    "position_id": position.get('position_id'),
                    "current_ratio": position_ratio,
                    "max_allowed": self.config["max_position_size"]
                })
                compliance_report["is_compliant"] = False
        
        # 检查交易对合规性
        for position in positions:
            chain = position.get('chain')
            token0 = position.get('token0_symbol', '')
            token1 = position.get('token1_symbol', '')
            
            if not self.is_valid_pair(chain, token0, token1):
                compliance_report["violations"].append({
                    "type": "INVALID_PAIR",
                    "position_id": position.get('position_id'),
                    "pair": f"{token0}/{token1}",
                    "chain": chain
                })
                compliance_report["is_compliant"] = False
        
        # 检查储备比例
        cash_ratio = portfolio_data.get('cash_ratio', 0)
        if cash_ratio < self.config["reserve_ratio"]:
            compliance_report["recommendations"].append({
                "type": "INCREASE_RESERVES",
                "current_ratio": cash_ratio,
                "target_ratio": self.config["reserve_ratio"]
            })
        
        return compliance_report
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """获取策略总结"""
        return {
            "strategy_name": "Focused Pairs Strategy",
            "description": "专注于BNB/USDC/USDT和SOL/USDC交易对的LP策略",
            "target_pairs": {
                chain: self.get_target_pairs(chain) 
                for chain in self.focused_pairs.keys()
            },
            "risk_management": {
                "stop_loss": f"{self.config['stop_loss_threshold']*100}%",
                "emergency_exit": f"{self.config['emergency_exit_threshold']*100}%",
                "max_position_size": f"{self.config['max_position_size']*100}%",
                "reserve_ratio": f"{self.config['reserve_ratio']*100}%"
            },
            "exit_tokens": {
                chain: pair.exit_token 
                for chain, pair in self.focused_pairs.items()
            },
            "last_updated": datetime.now().isoformat()
        }
