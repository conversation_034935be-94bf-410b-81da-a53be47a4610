"""
PancakeSwap GraphQL API 集成 - 更新版
支持 PancakeSwap V2/V3 數據獲取和用戶錢包 LP 查詢
"""

import asyncio
import aiohttp
from typing import List, Dict, Optional, Any
from datetime import datetime
import structlog

logger = structlog.get_logger(__name__)


class PancakeSwapV3Integration:
    """PancakeSwap V3 GraphQL API 集成"""
    
    def __init__(self, config: Any):
        self.config = config
        # PancakeSwap V3 Subgraph 端點 (使用The Graph的正确端点)
        self.endpoint = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
        self.api_key = "9731921233db132a98c2325878e6c153"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}',
                'User-Agent': 'DyFlow-AI-Agent/3.0'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def get_user_positions(self, wallet_address: str) -> List[Dict[str, Any]]:
        """獲取用戶的 V3 LP 倉位"""
        query = """
        query getUserV3Positions($userAddress: Bytes!) {
          positions(
            where: { owner: $userAddress, liquidity_gt: "0" }
            first: 1000
            orderBy: liquidity
            orderDirection: desc
          ) {
            id
            owner
            liquidity
            depositedToken0
            depositedToken1
            withdrawnToken0
            withdrawnToken1
            collectedFeesToken0
            collectedFeesToken1
            feeGrowthInside0LastX128
            feeGrowthInside1LastX128
            pool {
              id
              token0 {
                id
                symbol
                name
                decimals
              }
              token1 {
                id
                symbol
                name
                decimals
              }
              feeTier
              sqrtPrice
              tick
              liquidity
              volumeUSD
              tvlUSD
              token0Price
              token1Price
            }
            tickLower {
              tickIdx
            }
            tickUpper {
              tickIdx
            }
          }
        }
        """
        
        variables = {
            "userAddress": wallet_address.lower()
        }
        
        try:
            result = await self._execute_query(query, variables)
            positions = result.get('data', {}).get('positions', [])
            
            logger.info("user_v3_positions_retrieved", 
                       wallet=wallet_address, 
                       count=len(positions))
            
            return positions
            
        except Exception as e:
            logger.error("get_user_v3_positions_failed", 
                        wallet=wallet_address, error=str(e))
            return []
    
    async def get_top_pools(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取頂級 V3 流動性池"""
        query = """
        query getTopV3Pools($limit: Int!) {
          pools(
            first: $limit
            orderBy: volumeUSD
            orderDirection: desc
            where: { volumeUSD_gt: "10000" }
          ) {
            id
            token0 {
              id
              symbol
              name
              decimals
            }
            token1 {
              id
              symbol
              name
              decimals
            }
            feeTier
            sqrtPrice
            tick
            liquidity
            volumeUSD
            totalValueLockedUSD
            token0Price
            token1Price
            feeGrowthGlobal0X128
            feeGrowthGlobal1X128
            totalValueLockedToken0
            totalValueLockedToken1
          }
        }
        """
        
        variables = {"limit": limit}
        
        try:
            result = await self._execute_query(query, variables)
            pools = result.get('data', {}).get('pools', [])
            
            logger.info("top_v3_pools_retrieved", count=len(pools))
            return pools
            
        except Exception as e:
            logger.error("get_top_v3_pools_failed", error=str(e))
            return []
    
    async def get_pool_details(self, pool_id: str) -> Optional[Dict[str, Any]]:
        """獲取 V3 池子詳情"""
        query = """
        query getV3PoolDetails($poolId: ID!) {
          pool(id: $poolId) {
            id
            token0 {
              id
              symbol
              name
              decimals
            }
            token1 {
              id
              symbol
              name
              decimals
            }
            feeTier
            sqrtPrice
            tick
            liquidity
            volumeUSD
            totalValueLockedUSD
            token0Price
            token1Price
            feeGrowthGlobal0X128
            feeGrowthGlobal1X128
            totalValueLockedToken0
            totalValueLockedToken1
          }
        }
        """
        
        variables = {"poolId": pool_id}
        
        try:
            result = await self._execute_query(query, variables)
            pool = result.get('data', {}).get('pool')
            
            if pool:
                logger.info("v3_pool_details_retrieved", pool_id=pool_id)
            else:
                logger.warning("v3_pool_not_found", pool_id=pool_id)
            
            return pool
            
        except Exception as e:
            logger.error("get_v3_pool_details_failed", pool_id=pool_id, error=str(e))
            return None
    
    async def _execute_query(self, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """執行 GraphQL 查詢"""
        if not self.session:
            raise Exception("HTTP session 未初始化")
        
        payload = {
            "query": query,
            "variables": variables
        }
        
        try:
            async with self.session.post(self.endpoint, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"GraphQL 請求失敗: {response.status} - {error_text}")
                
                result = await response.json()
                
                if 'errors' in result:
                    errors = result['errors']
                    raise Exception(f"GraphQL 查詢錯誤: {errors}")
                
                return result
                
        except aiohttp.ClientError as e:
            raise Exception(f"網絡請求失敗: {e}")
        except Exception as e:
            raise Exception(f"查詢執行失敗: {e}")


class PancakeSwapIntegration:
    """PancakeSwap V2 GraphQL API 集成"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.endpoint = config.get('endpoint', 'https://bsc.streamingfast.io/subgraphs/name/pancakeswap/exchange-v2')
        self.min_tvl = config.get('min_tvl', 50000)
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'DyFlow-AI-Agent/3.0'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def get_user_lp_positions(self, wallet_address: str) -> List[Dict[str, Any]]:
        """獲取用戶的 V2 LP 持倉"""
        query = """
        query getUserLPPositions($userAddress: Bytes!) {
          liquidityPositions(
            where: { user: $userAddress, liquidityTokenBalance_gt: "0" }
            first: 1000
            orderBy: liquidityTokenBalance
            orderDirection: desc
          ) {
            id
            liquidityTokenBalance
            user {
              id
            }
            pair {
              id
              token0 {
                id
                symbol
                name
                decimals
                derivedETH
              }
              token1 {
                id
                symbol
                name
                decimals
                derivedETH
              }
              reserve0
              reserve1
              reserveUSD
              totalSupply
              token0Price
              token1Price
              volumeUSD
              liquidityProviderCount
              dayData(first: 1, orderBy: date, orderDirection: desc) {
                dailyVolumeUSD
                reserveUSD
                totalSupply
                date
              }
            }
          }
        }
        """
        
        variables = {
            "userAddress": wallet_address.lower()
        }
        
        try:
            result = await self._execute_query(query, variables)
            positions = result.get('data', {}).get('liquidityPositions', [])
            
            # 計算每個持倉的詳細信息
            enriched_positions = []
            for position in positions:
                try:
                    enriched_position = await self._enrich_lp_position(position)
                    enriched_positions.append(enriched_position)
                except Exception as e:
                    logger.warning("position_enrichment_failed", 
                                 position_id=position.get('id'), error=str(e))
                    continue
            
            return enriched_positions
            
        except Exception as e:
            logger.error("get_user_lp_positions_failed", 
                        wallet=wallet_address, error=str(e))
            return []
    
    async def _enrich_lp_position(self, position: Dict[str, Any]) -> Dict[str, Any]:
        """豐富 LP 持倉數據"""
        try:
            pair = position['pair']
            lp_balance = float(position['liquidityTokenBalance'])
            total_supply = float(pair['totalSupply'])
            
            if total_supply <= 0:
                raise ValueError("總供應量為 0")
            
            # 計算持倉占比
            ownership_percentage = lp_balance / total_supply
            
            # 計算持倉價值
            pool_value_usd = float(pair['reserveUSD'])
            position_value_usd = pool_value_usd * ownership_percentage
            
            # 計算代幣數量
            token0_amount = float(pair['reserve0']) * ownership_percentage
            token1_amount = float(pair['reserve1']) * ownership_percentage
            
            # 獲取最近交易量
            day_data = pair.get('dayData', [])
            daily_volume = float(day_data[0].get('dailyVolumeUSD', 0)) if day_data else 0
            
            # 計算 APR
            apr = await self._calculate_apr(pair)
            
            # 計算預計日收益
            fee_rate = 0.0025  # 0.25%
            daily_fees = daily_volume * fee_rate * ownership_percentage
            
            return {
                'id': position['id'],
                'pair_address': pair['id'],
                'token0': {
                    'symbol': pair['token0']['symbol'],
                    'name': pair['token0']['name'],
                    'address': pair['token0']['id'],
                    'amount': token0_amount,
                    'decimals': int(pair['token0']['decimals'])
                },
                'token1': {
                    'symbol': pair['token1']['symbol'],
                    'name': pair['token1']['name'],
                    'address': pair['token1']['id'],
                    'amount': token1_amount,
                    'decimals': int(pair['token1']['decimals'])
                },
                'liquidityTokenBalance': lp_balance,
                'ownership_percentage': ownership_percentage,
                'position_value_usd': position_value_usd,
                'pool_info': {
                    'total_liquidity_usd': pool_value_usd,
                    'total_supply': total_supply,
                    'daily_volume_usd': daily_volume,
                    'lp_count': pair.get('liquidityProviderCount', 0),
                    'apr': apr
                },
                'estimated_daily_earnings': {
                    'fees_usd': daily_fees,
                    'apr': apr
                },
                'pair_name': f"{pair['token0']['symbol']}-{pair['token1']['symbol']}",
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("enrich_lp_position_failed", 
                        position_id=position.get('id'), error=str(e))
            raise
    
    async def _calculate_apr(self, pool_data: Dict[str, Any]) -> float:
        """計算年化收益率 APR"""
        try:
            # 獲取 24h 交易量
            day_data = pool_data.get('dayData', [])
            if not day_data:
                return 0.0
            
            daily_volume = float(day_data[0].get('dailyVolumeUSD', 0))
            tvl = float(pool_data.get('reserveUSD', 0))
            
            if tvl <= 0:
                return 0.0
            
            # 計算手續費收入
            fee_rate = 0.0025  # 0.25%
            daily_fees = daily_volume * fee_rate
            
            # 年化收益率
            apr = (daily_fees / tvl) * 365
            
            return min(apr, 10.0)  # 限制最大 APR 為 1000%
            
        except Exception as e:
            logger.warning("apr_calculation_failed", error=str(e))
            return 0.0
    
    async def _execute_query(self, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """執行 GraphQL 查詢"""
        if not self.session:
            raise Exception("HTTP session 未初始化")
        
        payload = {
            "query": query,
            "variables": variables
        }
        
        try:
            async with self.session.post(self.endpoint, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"GraphQL 請求失敗: {response.status} - {error_text}")
                
                result = await response.json()
                
                if 'errors' in result:
                    errors = result['errors']
                    raise Exception(f"GraphQL 查詢錯誤: {errors}")
                
                return result
                
        except aiohttp.ClientError as e:
            raise Exception(f"網絡請求失敗: {e}")
        except Exception as e:
            raise Exception(f"查詢執行失敗: {e}")