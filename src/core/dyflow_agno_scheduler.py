"""
DyFlow Agno Scheduler - 基于Agno Framework的Agent调度器
实现Agent间通信、智能决策集成、工作流自动化
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog
import json

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass

from ..agents.data_provider_agent import DataProviderAgent
from ..agents.planner_agno import PlannerAgnoAgent
from ..agents.risk_sentinel_agno import RiskSentinelAgnoAgent
from ..utils.config import Config
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

class DyFlowAgnoScheduler:
    """DyFlow Agno调度器 - 管理Agent间通信和工作流"""
    
    def __init__(self, config: Config):
        self.config = config
        self.agents: Dict[str, Agent] = {}
        self.agent_states: Dict[str, str] = {}
        self.agent_results: Dict[str, Any] = {}
        self.message_queue: List[Dict[str, Any]] = []
        self.running = False
        
        # 初始化Agent
        self._initialize_agents()
        
        # 设置Agent依赖关系
        self.agent_dependencies = {
            'data_provider': [],  # 无依赖，最先执行
            'planner': ['data_provider'],  # 依赖数据提供者
            'risk_sentinel': ['data_provider', 'planner'],  # 依赖数据和规划
        }
        
        # 执行统计
        self.execution_stats = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'agent_executions': {},
            'last_execution': None
        }
    
    def _initialize_agents(self):
        """初始化所有Agent"""
        try:
            if not AGNO_AVAILABLE:
                logger.warning("agno_framework_not_available")
                return
            
            # 数据提供Agent
            self.agents['data_provider'] = DataProviderAgent()
            self.agent_states['data_provider'] = 'idle'
            
            # 规划Agent
            planner_config = self.config.get_agent_config('planner', {})
            self.agents['planner'] = PlannerAgnoAgent(planner_config)
            self.agent_states['planner'] = 'idle'
            
            # 风险监控Agent
            risk_config = self.config.get_agent_config('risk_sentinel', {})
            self.agents['risk_sentinel'] = RiskSentinelAgnoAgent(risk_config)
            self.agent_states['risk_sentinel'] = 'idle'
            
            logger.info("agents_initialized", 
                       count=len(self.agents),
                       agents=list(self.agents.keys()))
            
        except Exception as e:
            logger.error("agents_initialization_failed", error=str(e))
    
    async def start(self):
        """启动调度器"""
        try:
            self.running = True
            logger.info("dyflow_agno_scheduler_started")
            
            # 启动主执行循环
            await self._main_execution_loop()
            
        except Exception as e:
            logger.error("scheduler_start_failed", error=str(e))
            self.running = False
    
    async def stop(self):
        """停止调度器"""
        self.running = False
        logger.info("dyflow_agno_scheduler_stopped")
    
    async def _main_execution_loop(self):
        """主执行循环"""
        while self.running:
            try:
                cycle_start = datetime.utcnow()
                self.execution_stats['total_cycles'] += 1
                
                logger.info("execution_cycle_started", 
                           cycle=self.execution_stats['total_cycles'])
                
                # 执行Agent工作流
                success = await self._execute_agent_workflow()
                
                if success:
                    self.execution_stats['successful_cycles'] += 1
                else:
                    self.execution_stats['failed_cycles'] += 1
                
                self.execution_stats['last_execution'] = cycle_start.isoformat()
                
                # 处理Agent间消息
                await self._process_message_queue()
                
                logger.info("execution_cycle_completed",
                           cycle=self.execution_stats['total_cycles'],
                           success=success,
                           duration=(datetime.utcnow() - cycle_start).total_seconds())
                
                # 等待下一个周期 (5分钟)
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error("execution_cycle_failed", error=str(e))
                self.execution_stats['failed_cycles'] += 1
                await asyncio.sleep(60)  # 错误时等待1分钟
    
    async def _execute_agent_workflow(self) -> bool:
        """执行Agent工作流"""
        try:
            # 按依赖顺序执行Agent
            execution_order = self._get_execution_order()
            
            for agent_name in execution_order:
                if agent_name not in self.agents:
                    logger.warning("agent_not_found", agent=agent_name)
                    continue
                
                # 检查依赖是否满足
                if not self._check_dependencies(agent_name):
                    logger.warning("agent_dependencies_not_met", agent=agent_name)
                    continue
                
                # 执行Agent
                success = await self._execute_agent(agent_name)
                if not success:
                    logger.error("agent_execution_failed", agent=agent_name)
                    return False
            
            return True
            
        except Exception as e:
            logger.error("workflow_execution_failed", error=str(e))
            return False
    
    def _get_execution_order(self) -> List[str]:
        """获取Agent执行顺序"""
        # 简单的拓扑排序
        order = []
        remaining = set(self.agents.keys())
        
        while remaining:
            # 找到没有未满足依赖的Agent
            ready = []
            for agent in remaining:
                deps = self.agent_dependencies.get(agent, [])
                if all(dep in order for dep in deps):
                    ready.append(agent)
            
            if not ready:
                # 如果没有准备好的Agent，可能有循环依赖
                logger.warning("possible_circular_dependency", remaining=list(remaining))
                ready = [list(remaining)[0]]  # 强制执行第一个
            
            # 添加到执行顺序
            for agent in ready:
                order.append(agent)
                remaining.remove(agent)
        
        return order
    
    def _check_dependencies(self, agent_name: str) -> bool:
        """检查Agent依赖是否满足"""
        deps = self.agent_dependencies.get(agent_name, [])
        for dep in deps:
            if dep not in self.agent_results or self.agent_results[dep].get('status') != 'success':
                return False
        return True
    
    async def _execute_agent(self, agent_name: str) -> bool:
        """执行单个Agent"""
        try:
            agent = self.agents[agent_name]
            self.agent_states[agent_name] = 'running'
            
            # 准备执行上下文
            context = self._prepare_agent_context(agent_name)
            task = f"执行{agent_name}任务，上下文: {json.dumps(context, ensure_ascii=False)}"
            
            logger.info("agent_execution_started", agent=agent_name)
            
            # 执行Agent
            result = await agent.execute(task)
            
            # 保存结果
            self.agent_results[agent_name] = result
            self.agent_states[agent_name] = 'completed'
            
            # 更新统计
            if agent_name not in self.execution_stats['agent_executions']:
                self.execution_stats['agent_executions'][agent_name] = 0
            self.execution_stats['agent_executions'][agent_name] += 1
            
            logger.info("agent_execution_completed", 
                       agent=agent_name,
                       status=result.get('status', 'unknown'))
            
            return result.get('status') == 'success'
            
        except Exception as e:
            logger.error("agent_execution_error", agent=agent_name, error=str(e))
            self.agent_states[agent_name] = 'error'
            self.agent_results[agent_name] = {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
            return False
    
    def _prepare_agent_context(self, agent_name: str) -> Dict[str, Any]:
        """为Agent准备执行上下文"""
        context = {
            'agent_name': agent_name,
            'cycle': self.execution_stats['total_cycles'],
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # 添加依赖Agent的结果
        deps = self.agent_dependencies.get(agent_name, [])
        for dep in deps:
            if dep in self.agent_results:
                context[f'{dep}_result'] = self.agent_results[dep]
        
        return context
    
    async def _process_message_queue(self):
        """处理Agent间消息队列"""
        try:
            while self.message_queue:
                message = self.message_queue.pop(0)
                await self._handle_agent_message(message)
                
        except Exception as e:
            logger.error("message_queue_processing_failed", error=str(e))
    
    async def _handle_agent_message(self, message: Dict[str, Any]):
        """处理Agent间消息"""
        try:
            sender = message.get('from')
            receiver = message.get('to')
            msg_type = message.get('type')
            data = message.get('data')
            
            logger.info("agent_message_processed",
                       from_agent=sender,
                       to_agent=receiver,
                       message_type=msg_type)
            
            # 这里可以实现具体的消息处理逻辑
            # 例如：数据共享、状态同步、协调决策等
            
        except Exception as e:
            logger.error("agent_message_handling_failed", error=str(e))
    
    def send_message(self, from_agent: str, to_agent: str, msg_type: str, data: Any):
        """发送Agent间消息"""
        message = {
            'from': from_agent,
            'to': to_agent,
            'type': msg_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.message_queue.append(message)
        
        logger.info("agent_message_queued",
                   from_agent=from_agent,
                   to_agent=to_agent,
                   message_type=msg_type)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'running': self.running,
            'agents': {
                name: {
                    'state': self.agent_states.get(name, 'unknown'),
                    'last_result': self.agent_results.get(name, {}).get('status', 'none')
                }
                for name in self.agents.keys()
            },
            'execution_stats': self.execution_stats,
            'message_queue_size': len(self.message_queue),
            'timestamp': datetime.utcnow().isoformat()
        }

# 导出
__all__ = ['DyFlowAgnoScheduler', 'AGNO_AVAILABLE']
