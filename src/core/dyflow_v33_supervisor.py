"""
DyFlow v3.3 Supervisor Agent - 八階段啟動序列管理
實現 PRD v3.3 定義的固定啟動流程和 Agent 協調
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
import json

logger = structlog.get_logger(__name__)

class Phase(Enum):
    """八階段啟動序列"""
    PHASE_0_INIT = 0          # SupervisorAgent - 讀取 YAML/ENV、Vault 發放密鑰
    PHASE_1_HEALTH = 1        # HealthGuardAgent + ConnProbeTool - RPC/Subgraph/DB/UI ≥ 90% 健康
    PHASE_2_UI = 2            # WebUI & PrometheusExporter - http://localhost:3000 200 OK
    PHASE_3_WALLET = 3        # WalletProbeTool - MPC 簽名 & nonce 測試通過
    PHASE_4_MARKET = 4        # MarketIntelAgent - 掃描池子，Agno 通訊
    PHASE_5_PORTFOLIO = 5     # PortfolioManagerAgent - NAV ≥ 0 且資金鎖可寫入
    PHASE_6_STRATEGY = 6      # StrategyAgent - 產生 LPPlan.approved
    PHASE_7_EXECUTION = 7     # ExecutionAgent - Tx ≥ 1 筆成功廣播
    PHASE_8_RISK = 8          # RiskSentinelAgent + FeeCollectorTool - IL_net、VaR 均在限內

class SupervisorAgent:
    """
    DyFlow v3.3 Supervisor Agent
    負責八階段啟動序列和系統生命週期管理
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.current_phase = Phase.PHASE_0_INIT
        self.phase_status = {}
        self.agents = {}
        self.tools = {}
        self.nats_client = None
        self.is_running = False
        
        # 初始化階段狀態
        for phase in Phase:
            self.phase_status[phase] = {
                'status': 'pending',
                'started_at': None,
                'completed_at': None,
                'error': None,
                'retries': 0
            }
    
    async def start_system(self) -> bool:
        """
        啟動 DyFlow v3.3 系統
        按照八階段序列逐步初始化
        """
        logger.info("dyflow_v33_startup_initiated", version="3.3")
        
        try:
            # 按順序執行八個階段
            for phase in Phase:
                success = await self._execute_phase(phase)
                if not success:
                    logger.error("phase_failed", phase=phase.name)
                    await self._rollback_system()
                    return False
                    
                self.current_phase = phase
                logger.info("phase_completed", phase=phase.name)
            
            self.is_running = True
            logger.info("dyflow_v33_startup_completed", all_phases_success=True)
            return True
            
        except Exception as e:
            logger.error("startup_failed", error=str(e))
            await self._rollback_system()
            return False
    
    async def _execute_phase(self, phase: Phase) -> bool:
        """執行特定階段"""
        phase_info = self.phase_status[phase]
        phase_info['status'] = 'running'
        phase_info['started_at'] = datetime.utcnow()
        
        try:
            if phase == Phase.PHASE_0_INIT:
                return await self._phase_0_init()
            elif phase == Phase.PHASE_1_HEALTH:
                return await self._phase_1_health()
            elif phase == Phase.PHASE_2_UI:
                return await self._phase_2_ui()
            elif phase == Phase.PHASE_3_WALLET:
                return await self._phase_3_wallet()
            elif phase == Phase.PHASE_4_MARKET:
                return await self._phase_4_market()
            elif phase == Phase.PHASE_5_PORTFOLIO:
                return await self._phase_5_portfolio()
            elif phase == Phase.PHASE_6_STRATEGY:
                return await self._phase_6_strategy()
            elif phase == Phase.PHASE_7_EXECUTION:
                return await self._phase_7_execution()
            elif phase == Phase.PHASE_8_RISK:
                return await self._phase_8_risk()
            else:
                return False
                
        except Exception as e:
            phase_info['status'] = 'failed'
            phase_info['error'] = str(e)
            logger.error("phase_execution_failed", phase=phase.name, error=str(e))
            return False
    
    async def _phase_0_init(self) -> bool:
        """Phase 0: SupervisorAgent - 讀取 YAML/ENV、Vault 發放密鑰"""
        logger.info("executing_phase_0_init")
        
        # 讀取配置
        if not self.config:
            logger.error("config_missing")
            return False
        
        # 初始化 NATS 連接
        try:
            # 這裡應該初始化 NATS 客戶端
            # self.nats_client = await nats.connect("nats://localhost:4222")
            logger.info("nats_connection_simulated")  # 暫時模擬
        except Exception as e:
            logger.error("nats_connection_failed", error=str(e))
            return False
        
        # Vault 密鑰發放 (模擬)
        logger.info("vault_keys_distributed")
        
        self.phase_status[Phase.PHASE_0_INIT]['status'] = 'completed'
        self.phase_status[Phase.PHASE_0_INIT]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_1_health(self) -> bool:
        """Phase 1: HealthGuardAgent + ConnProbeTool"""
        logger.info("executing_phase_1_health")
        
        # 初始化 HealthGuardAgent
        from ..agents.health_guard_agent import HealthGuardAgent
        self.agents['health'] = HealthGuardAgent(self.config.get('health_guard', {}))
        
        # 執行健康檢查
        health_result = await self.agents['health'].check_system_health()
        if health_result.get('health_score', 0) < 0.9:  # 要求 ≥ 90%
            logger.error("health_check_failed", score=health_result.get('health_score'))
            return False
        
        self.phase_status[Phase.PHASE_1_HEALTH]['status'] = 'completed'
        self.phase_status[Phase.PHASE_1_HEALTH]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_2_ui(self) -> bool:
        """Phase 2: WebUI & PrometheusExporter"""
        logger.info("executing_phase_2_ui")
        
        # 啟動 WebUI (模擬)
        # 實際應該檢查 http://localhost:3000 是否返回 200 OK
        logger.info("webui_started", url="http://localhost:3000")
        
        # 啟動 Prometheus Exporter (模擬)
        logger.info("prometheus_exporter_started")
        
        self.phase_status[Phase.PHASE_2_UI]['status'] = 'completed'
        self.phase_status[Phase.PHASE_2_UI]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_3_wallet(self) -> bool:
        """Phase 3: WalletProbeTool - MPC 簽名 & nonce 測試"""
        logger.info("executing_phase_3_wallet")
        
        # 初始化 WalletProbeTool
        from ..tools.wallet_probe_tool import WalletProbeTool
        self.tools['wallet_probe'] = WalletProbeTool(self.config.get('wallet', {}))
        
        # 執行 MPC 簽名測試
        wallet_test = await self.tools['wallet_probe'].test_mpc_signing()
        if not wallet_test.get('success', False):
            logger.error("wallet_test_failed", error=wallet_test.get('error'))
            return False
        
        self.phase_status[Phase.PHASE_3_WALLET]['status'] = 'completed'
        self.phase_status[Phase.PHASE_3_WALLET]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_4_market(self) -> bool:
        """Phase 4: MarketIntelAgent - 掃描池子，使用 Agno 通訊"""
        logger.info("executing_phase_4_market")
        
        # 初始化 MarketIntelAgent
        from ..agents.market_intel_agent import MarketIntelAgent
        self.agents['market_intel'] = MarketIntelAgent(self.config.get('market_intel', {}))
        
        # 開始池事件推送
        await self.agents['market_intel'].start_pool_scanning()
        
        self.phase_status[Phase.PHASE_4_MARKET]['status'] = 'completed'
        self.phase_status[Phase.PHASE_4_MARKET]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_5_portfolio(self) -> bool:
        """Phase 5: PortfolioManagerAgent - NAV ≥ 0 且資金鎖可寫入"""
        logger.info("executing_phase_5_portfolio")
        
        # 初始化 PortfolioManagerAgent
        from ..agents.portfolio_manager_agent import PortfolioManagerAgent
        self.agents['portfolio'] = PortfolioManagerAgent(self.config.get('portfolio', {}))
        
        # 檢查 NAV 和資金鎖
        portfolio_status = await self.agents['portfolio'].check_portfolio_status()
        if portfolio_status.get('nav', 0) < 0:
            logger.error("negative_nav", nav=portfolio_status.get('nav'))
            return False
        
        self.phase_status[Phase.PHASE_5_PORTFOLIO]['status'] = 'completed'
        self.phase_status[Phase.PHASE_5_PORTFOLIO]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_6_strategy(self) -> bool:
        """Phase 6: StrategyAgent - 產生 LPPlan.approved"""
        logger.info("executing_phase_6_strategy")
        
        # 初始化 StrategyAgent
        from ..agents.strategy_agent import StrategyAgent
        self.agents['strategy'] = StrategyAgent(self.config.get('strategy', {}))
        
        # 生成策略計劃
        strategy_result = await self.agents['strategy'].generate_lp_plans()
        if not strategy_result.get('plans'):
            logger.error("no_strategy_plans_generated")
            return False
        
        self.phase_status[Phase.PHASE_6_STRATEGY]['status'] = 'completed'
        self.phase_status[Phase.PHASE_6_STRATEGY]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_7_execution(self) -> bool:
        """Phase 7: ExecutionAgent - Tx ≥ 1 筆成功廣播"""
        logger.info("executing_phase_7_execution")
        
        # 初始化 ExecutionAgent
        from ..agents.execution_agent import ExecutionAgent
        self.agents['execution'] = ExecutionAgent(self.config.get('execution', {}))
        
        # 執行交易測試
        execution_result = await self.agents['execution'].test_transaction()
        if not execution_result.get('success'):
            logger.error("execution_test_failed", error=execution_result.get('error'))
            return False
        
        self.phase_status[Phase.PHASE_7_EXECUTION]['status'] = 'completed'
        self.phase_status[Phase.PHASE_7_EXECUTION]['completed_at'] = datetime.utcnow()
        return True
    
    async def _phase_8_risk(self) -> bool:
        """Phase 8: RiskSentinelAgent + FeeCollectorTool"""
        logger.info("executing_phase_8_risk")
        
        # 初始化 RiskSentinelAgent
        from ..agents.risk_sentinel_agent_v33 import RiskSentinelAgentV33
        self.agents['risk'] = RiskSentinelAgentV33(self.config.get('risk', {}))
        
        # 初始化 FeeCollectorTool
        from ..tools.fee_collector_tool import FeeCollectorTool
        self.tools['fee_collector'] = FeeCollectorTool(self.config.get('fee_collector', {}))
        
        # 檢查風控指標
        risk_status = await self.agents['risk'].check_risk_metrics()
        if not risk_status.get('within_limits'):
            logger.error("risk_metrics_exceeded", metrics=risk_status)
            return False
        
        self.phase_status[Phase.PHASE_8_RISK]['status'] = 'completed'
        self.phase_status[Phase.PHASE_8_RISK]['completed_at'] = datetime.utcnow()
        return True
    
    async def _rollback_system(self):
        """系統回滾"""
        logger.warning("initiating_system_rollback")
        
        # 停止所有 Agent
        for agent_name, agent in self.agents.items():
            try:
                if hasattr(agent, 'stop'):
                    await agent.stop()
                logger.info("agent_stopped", agent=agent_name)
            except Exception as e:
                logger.error("agent_stop_failed", agent=agent_name, error=str(e))
        
        # 關閉 NATS 連接
        if self.nats_client:
            try:
                await self.nats_client.close()
            except Exception as e:
                logger.error("nats_close_failed", error=str(e))
        
        self.is_running = False
        logger.info("system_rollback_completed")
    
    async def stop_system(self):
        """停止系統"""
        logger.info("stopping_dyflow_v33_system")
        await self._rollback_system()
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            'is_running': self.is_running,
            'current_phase': self.current_phase.name if self.current_phase else None,
            'phase_status': {phase.name: status for phase, status in self.phase_status.items()},
            'agents_count': len(self.agents),
            'tools_count': len(self.tools)
        }
