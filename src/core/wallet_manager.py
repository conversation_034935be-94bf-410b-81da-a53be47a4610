#!/usr/bin/env python3
"""
DyFlow钱包管理系统
管理多钱包、资产追踪、风险监控
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from cryptography.fernet import Fernet
import structlog
from decimal import Decimal

logger = structlog.get_logger(__name__)

@dataclass
class WalletInfo:
    """钱包信息"""
    name: str
    chain: str  # 'bsc' or 'solana'
    address: str
    encrypted_private_key: str
    created_at: str
    last_updated: str
    is_active: bool = True

@dataclass
class AssetBalance:
    """资产余额"""
    token_symbol: str
    token_address: str
    balance: float
    usd_value: float
    last_updated: str

@dataclass
class LPPosition:
    """LP持仓信息"""
    pool_address: str
    pool_name: str
    token0_symbol: str
    token1_symbol: str
    liquidity: float
    token0_amount: float
    token1_amount: float
    usd_value: float
    entry_price: float
    current_price: float
    pnl: float
    pnl_percentage: float
    impermanent_loss: float
    fees_earned: float
    created_at: str
    last_updated: str

@dataclass
class RiskMetrics:
    """风险指标"""
    total_delta: float
    max_possible_loss: float
    impermanent_loss_risk: float
    liquidity_risk: float
    concentration_risk: float
    volatility_risk: float
    overall_risk_score: float
    risk_level: str  # LOW, MEDIUM, HIGH, CRITICAL
    last_calculated: str

class WalletManager:
    """钱包管理器"""
    
    def __init__(self, data_dir: str = "data/wallets"):
        self.data_dir = data_dir
        self.wallets_file = os.path.join(data_dir, "wallets.json")
        self.positions_file = os.path.join(data_dir, "positions.json")
        self.risk_file = os.path.join(data_dir, "risk_metrics.json")
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 加密密钥 (实际使用中应该从环境变量或安全存储获取)
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # 加载数据
        self.wallets: Dict[str, WalletInfo] = self._load_wallets()
        self.positions: Dict[str, List[LPPosition]] = self._load_positions()
        self.risk_metrics: Dict[str, RiskMetrics] = self._load_risk_metrics()
        
        logger.info("wallet_manager_initialized", 
                   wallets_count=len(self.wallets),
                   data_dir=data_dir)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = os.path.join(self.data_dir, ".encryption_key")
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def _load_wallets(self) -> Dict[str, WalletInfo]:
        """加载钱包数据"""
        if not os.path.exists(self.wallets_file):
            return {}
        
        try:
            with open(self.wallets_file, 'r') as f:
                data = json.load(f)
            
            wallets = {}
            for wallet_id, wallet_data in data.items():
                wallets[wallet_id] = WalletInfo(**wallet_data)
            
            return wallets
        except Exception as e:
            logger.error("load_wallets_failed", error=str(e))
            return {}
    
    def _save_wallets(self):
        """保存钱包数据"""
        try:
            data = {}
            for wallet_id, wallet in self.wallets.items():
                data[wallet_id] = asdict(wallet)
            
            with open(self.wallets_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error("save_wallets_failed", error=str(e))
    
    def _load_positions(self) -> Dict[str, List[LPPosition]]:
        """加载LP持仓数据"""
        if not os.path.exists(self.positions_file):
            return {}
        
        try:
            with open(self.positions_file, 'r') as f:
                data = json.load(f)
            
            positions = {}
            for wallet_id, position_list in data.items():
                positions[wallet_id] = [LPPosition(**pos) for pos in position_list]
            
            return positions
        except Exception as e:
            logger.error("load_positions_failed", error=str(e))
            return {}
    
    def _save_positions(self):
        """保存LP持仓数据"""
        try:
            data = {}
            for wallet_id, position_list in self.positions.items():
                data[wallet_id] = [asdict(pos) for pos in position_list]
            
            with open(self.positions_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error("save_positions_failed", error=str(e))
    
    def _load_risk_metrics(self) -> Dict[str, RiskMetrics]:
        """加载风险指标数据"""
        if not os.path.exists(self.risk_file):
            return {}
        
        try:
            with open(self.risk_file, 'r') as f:
                data = json.load(f)
            
            metrics = {}
            for wallet_id, metric_data in data.items():
                metrics[wallet_id] = RiskMetrics(**metric_data)
            
            return metrics
        except Exception as e:
            logger.error("load_risk_metrics_failed", error=str(e))
            return {}
    
    def _save_risk_metrics(self):
        """保存风险指标数据"""
        try:
            data = {}
            for wallet_id, metrics in self.risk_metrics.items():
                data[wallet_id] = asdict(metrics)
            
            with open(self.risk_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error("save_risk_metrics_failed", error=str(e))
    
    def add_wallet(self, name: str, chain: str, address: str, private_key: str) -> str:
        """添加钱包"""
        try:
            # 加密私钥
            encrypted_key = self.cipher.encrypt(private_key.encode()).decode()
            
            # 生成钱包ID
            wallet_id = f"{chain}_{name}_{int(datetime.now().timestamp())}"
            
            # 创建钱包信息
            wallet = WalletInfo(
                name=name,
                chain=chain,
                address=address,
                encrypted_private_key=encrypted_key,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat()
            )
            
            self.wallets[wallet_id] = wallet
            self._save_wallets()
            
            logger.info("wallet_added", wallet_id=wallet_id, name=name, chain=chain)
            return wallet_id
            
        except Exception as e:
            logger.error("add_wallet_failed", error=str(e))
            raise
    
    def get_wallet_private_key(self, wallet_id: str) -> str:
        """获取钱包私钥 (解密)"""
        if wallet_id not in self.wallets:
            raise ValueError(f"Wallet {wallet_id} not found")
        
        encrypted_key = self.wallets[wallet_id].encrypted_private_key
        return self.cipher.decrypt(encrypted_key.encode()).decode()
    
    def update_lp_position(self, wallet_id: str, position: LPPosition):
        """更新LP持仓"""
        if wallet_id not in self.positions:
            self.positions[wallet_id] = []
        
        # 查找现有持仓
        existing_index = None
        for i, existing_pos in enumerate(self.positions[wallet_id]):
            if existing_pos.pool_address == position.pool_address:
                existing_index = i
                break
        
        if existing_index is not None:
            self.positions[wallet_id][existing_index] = position
        else:
            self.positions[wallet_id].append(position)
        
        self._save_positions()
        logger.info("lp_position_updated", wallet_id=wallet_id, pool=position.pool_name)
    
    def calculate_risk_metrics(self, wallet_id: str) -> RiskMetrics:
        """计算风险指标"""
        try:
            positions = self.positions.get(wallet_id, [])
            
            if not positions:
                return RiskMetrics(
                    total_delta=0.0,
                    max_possible_loss=0.0,
                    impermanent_loss_risk=0.0,
                    liquidity_risk=0.0,
                    concentration_risk=0.0,
                    volatility_risk=0.0,
                    overall_risk_score=0.0,
                    risk_level="LOW",
                    last_calculated=datetime.now().isoformat()
                )
            
            # 计算总Delta风险
            total_delta = sum(abs(pos.pnl) for pos in positions)
            
            # 计算最大可能损失 (假设最坏情况下损失50%)
            total_value = sum(pos.usd_value for pos in positions)
            max_possible_loss = total_value * 0.5
            
            # 计算无常损失风险
            avg_il = sum(abs(pos.impermanent_loss) for pos in positions) / len(positions)
            
            # 计算流动性风险 (基于池子大小)
            liquidity_risk = 0.0
            for pos in positions:
                if pos.usd_value > 0:
                    # 假设小池子风险更高
                    pool_risk = min(pos.usd_value / 1000000, 1.0) * 100
                    liquidity_risk += pool_risk
            liquidity_risk = liquidity_risk / len(positions) if positions else 0
            
            # 计算集中度风险
            if total_value > 0:
                max_position_ratio = max(pos.usd_value / total_value for pos in positions)
                concentration_risk = max_position_ratio * 100
            else:
                concentration_risk = 0
            
            # 计算波动率风险 (基于PnL波动)
            pnl_values = [pos.pnl_percentage for pos in positions]
            if len(pnl_values) > 1:
                avg_pnl = sum(pnl_values) / len(pnl_values)
                volatility_risk = sum((pnl - avg_pnl) ** 2 for pnl in pnl_values) / len(pnl_values)
                volatility_risk = volatility_risk ** 0.5
            else:
                volatility_risk = 0
            
            # 计算综合风险评分
            risk_score = (
                min(total_delta / 10000, 1.0) * 30 +  # Delta风险权重30%
                min(avg_il / 20, 1.0) * 25 +          # 无常损失权重25%
                min(liquidity_risk / 100, 1.0) * 20 + # 流动性风险权重20%
                min(concentration_risk / 100, 1.0) * 15 + # 集中度风险权重15%
                min(volatility_risk / 50, 1.0) * 10   # 波动率风险权重10%
            )
            
            # 确定风险等级
            if risk_score < 25:
                risk_level = "LOW"
            elif risk_score < 50:
                risk_level = "MEDIUM"
            elif risk_score < 75:
                risk_level = "HIGH"
            else:
                risk_level = "CRITICAL"
            
            metrics = RiskMetrics(
                total_delta=total_delta,
                max_possible_loss=max_possible_loss,
                impermanent_loss_risk=avg_il,
                liquidity_risk=liquidity_risk,
                concentration_risk=concentration_risk,
                volatility_risk=volatility_risk,
                overall_risk_score=risk_score,
                risk_level=risk_level,
                last_calculated=datetime.now().isoformat()
            )
            
            self.risk_metrics[wallet_id] = metrics
            self._save_risk_metrics()
            
            return metrics
            
        except Exception as e:
            logger.error("calculate_risk_metrics_failed", error=str(e))
            raise
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合总结"""
        try:
            total_value = 0.0
            total_lp_value = 0.0
            total_pnl = 0.0
            total_fees = 0.0
            
            chain_breakdown = {"bsc": 0.0, "solana": 0.0}
            risk_breakdown = {"LOW": 0, "MEDIUM": 0, "HIGH": 0, "CRITICAL": 0}
            
            for wallet_id, wallet in self.wallets.items():
                if not wallet.is_active:
                    continue
                
                positions = self.positions.get(wallet_id, [])
                wallet_value = sum(pos.usd_value for pos in positions)
                wallet_pnl = sum(pos.pnl for pos in positions)
                wallet_fees = sum(pos.fees_earned for pos in positions)
                
                total_value += wallet_value
                total_lp_value += wallet_value
                total_pnl += wallet_pnl
                total_fees += wallet_fees
                
                chain_breakdown[wallet.chain] += wallet_value
                
                # 风险分布
                risk_metrics = self.risk_metrics.get(wallet_id)
                if risk_metrics:
                    risk_breakdown[risk_metrics.risk_level] += 1
            
            return {
                "total_portfolio_value": total_value,
                "total_lp_value": total_lp_value,
                "total_pnl": total_pnl,
                "total_fees_earned": total_fees,
                "chain_breakdown": chain_breakdown,
                "risk_distribution": risk_breakdown,
                "active_wallets": len([w for w in self.wallets.values() if w.is_active]),
                "total_positions": sum(len(positions) for positions in self.positions.values()),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("get_portfolio_summary_failed", error=str(e))
            return {}
    
    def get_wallet_summary(self, wallet_id: str) -> Dict[str, Any]:
        """获取单个钱包总结"""
        if wallet_id not in self.wallets:
            return {}
        
        wallet = self.wallets[wallet_id]
        positions = self.positions.get(wallet_id, [])
        risk_metrics = self.risk_metrics.get(wallet_id)
        
        total_value = sum(pos.usd_value for pos in positions)
        total_pnl = sum(pos.pnl for pos in positions)
        total_fees = sum(pos.fees_earned for pos in positions)
        
        return {
            "wallet_info": asdict(wallet),
            "total_value": total_value,
            "total_pnl": total_pnl,
            "total_fees": total_fees,
            "position_count": len(positions),
            "positions": [asdict(pos) for pos in positions],
            "risk_metrics": asdict(risk_metrics) if risk_metrics else None
        }
