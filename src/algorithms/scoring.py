"""
五因子评分算法
实现核心的流动性池评分逻辑：
score = 0.45*APR_norm + 0.25*Volume_norm + 0.10*Depth_norm - 0.15*Volatility_norm - 0.05*IL_est
"""

import math
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
import structlog

from .normalization import Normalizer
from .risk_calc import RiskCalculator

logger = structlog.get_logger(__name__)


@dataclass
class PoolMetrics:
    """流动性池指标数据"""
    apr: float                    # 年化收益率 (%)
    volume_24h: float            # 24小时交易量 (USD)
    tvl: float                   # 总锁定价值 (USD)
    volatility: float            # 价格波动率 (%)
    token0: str                  # 代币0符号
    token1: str                  # 代币1符号
    fee_rate: float              # 手续费率
    chain: str                   # 区块链
    pool_id: str                 # 池子ID
    
    # 可选字段
    liquidity_depth: Optional[float] = None  # 流动性深度
    price_impact: Optional[float] = None     # 价格影响
    trading_frequency: Optional[float] = None # 交易频率


@dataclass
class ScoreComponents:
    """评分组成部分"""
    apr_score: float              # APR评分
    volume_score: float           # 交易量评分
    depth_score: float            # 深度评分
    volatility_score: float       # 波动率评分(负数)
    il_score: float               # 无常损失评分(负数)
    
    raw_apr_norm: float           # 原始APR归一化值
    raw_volume_norm: float        # 原始交易量归一化值
    raw_depth_norm: float         # 原始深度归一化值
    raw_volatility_norm: float    # 原始波动率归一化值
    raw_il_est: float             # 原始无常损失估算


@dataclass
class ScoringResult:
    """评分结果"""
    final_score: float            # 最终评分 (0-100)
    normalized_score: float       # 归一化评分 (0-1)
    components: ScoreComponents   # 评分组成部分
    risk_level: str              # 风险等级
    confidence: float            # 置信度
    warnings: list               # 警告信息
    metadata: Dict[str, Any]     # 元数据


class FiveFactorScorer:
    """五因子评分算法实现"""
    
    def __init__(self, 
                 weights: Optional[Dict[str, float]] = None,
                 normalization_params: Optional[Dict[str, Any]] = None):
        """
        初始化评分器
        
        Args:
            weights: 评分权重配置
            normalization_params: 归一化参数配置
        """
        # 默认权重配置
        self.weights = weights or {
            'apr': 0.45,           # APR权重
            'volume': 0.25,        # 交易量权重
            'depth': 0.10,         # 深度权重
            'volatility': -0.15,   # 波动率权重(负数，风险因子)
            'il': -0.05           # 无常损失权重(负数，风险因子)
        }
        
        # 验证权重总和
        self._validate_weights()
        
        # 初始化归一化器和风险计算器
        self.normalizer = Normalizer(normalization_params)
        self.risk_calc = RiskCalculator()
        
        # 决策阈值
        self.thresholds = {
            'enter_threshold': 0.60,      # 进入阈值
            'exit_threshold': 0.30,       # 退出阈值
            'high_score_threshold': 0.75, # 高分阈值
            'max_positions': 5,           # 最大持仓数
            'rebalance_threshold': 0.80   # 再平衡阈值(价格离区间80%)
        }
    
    def score_pool(self, pool_data: Dict[str, Any]) -> ScoringResult:
        """
        对单个池子进行评分
        
        Args:
            pool_data: 池子数据字典
            
        Returns:
            评分结果
        """
        try:
            # 解析池子指标
            metrics = self._parse_pool_metrics(pool_data)
            
            # 计算各项归一化指标
            apr_norm = self.normalizer.normalize_apr(metrics.apr)
            volume_norm = self.normalizer.normalize_volume(metrics.volume_24h)
            depth_norm = self.normalizer.normalize_depth(metrics.tvl)
            volatility_norm = self.normalizer.normalize_volatility(metrics.volatility)
            
            # 计算无常损失估算
            il_est = self.risk_calc.estimate_impermanent_loss(
                metrics.token0, metrics.token1, metrics.volatility
            )
            
            # 应用评分公式
            apr_score = self.weights['apr'] * apr_norm
            volume_score = self.weights['volume'] * volume_norm
            depth_score = self.weights['depth'] * depth_norm
            volatility_score = self.weights['volatility'] * volatility_norm
            il_score = self.weights['il'] * il_est
            
            # 计算最终评分
            raw_score = apr_score + volume_score + depth_score + volatility_score + il_score
            
            # 归一化到0-1范围，然后转换为0-100
            normalized_score = max(0.0, min(1.0, raw_score))
            final_score = normalized_score * 100
            
            # 评估风险等级
            risk_level = self._determine_risk_level(metrics, normalized_score)
            
            # 计算置信度
            confidence = self._calculate_confidence(metrics, pool_data)
            
            # 生成警告
            warnings = self._generate_warnings(metrics, normalized_score)
            
            # 构建评分组成部分
            components = ScoreComponents(
                apr_score=apr_score,
                volume_score=volume_score,
                depth_score=depth_score,
                volatility_score=volatility_score,
                il_score=il_score,
                raw_apr_norm=apr_norm,
                raw_volume_norm=volume_norm,
                raw_depth_norm=depth_norm,
                raw_volatility_norm=volatility_norm,
                raw_il_est=il_est
            )
            
            # 构建元数据
            metadata = {
                'pool_id': metrics.pool_id,
                'chain': metrics.chain,
                'token_pair': f"{metrics.token0}/{metrics.token1}",
                'scoring_timestamp': pool_data.get('timestamp'),
                'algorithm_version': '1.0',
                'weights_used': self.weights.copy()
            }
            
            result = ScoringResult(
                final_score=final_score,
                normalized_score=normalized_score,
                components=components,
                risk_level=risk_level,
                confidence=confidence,
                warnings=warnings,
                metadata=metadata
            )
            
            logger.debug("pool_scored",
                        pool_id=metrics.pool_id,
                        final_score=final_score,
                        risk_level=risk_level)
            
            return result
            
        except Exception as e:
            logger.error("pool_scoring_failed",
                        pool_id=pool_data.get('pool_id'),
                        error=str(e))
            raise
    
    def batch_score_pools(self, pools_data: list) -> Dict[str, ScoringResult]:
        """
        批量评分池子
        
        Args:
            pools_data: 池子数据列表
            
        Returns:
            池子ID到评分结果的映射
        """
        results = {}
        
        for pool_data in pools_data:
            try:
                pool_id = pool_data.get('pool_id')
                if not pool_id:
                    logger.warning("pool_missing_id", pool_data=pool_data)
                    continue
                
                result = self.score_pool(pool_data)
                results[pool_id] = result
                
            except Exception as e:
                logger.error("batch_scoring_pool_failed",
                           pool_id=pool_data.get('pool_id'),
                           error=str(e))
                continue
        
        logger.info("batch_scoring_completed",
                   total_pools=len(pools_data),
                   successful_scores=len(results))
        
        return results
    
    def get_investment_decision(self, 
                              score_result: ScoringResult,
                              current_positions: int = 0,
                              current_exposure: float = 0.0) -> Dict[str, Any]:
        """
        基于评分结果生成投资决策
        
        Args:
            score_result: 评分结果
            current_positions: 当前持仓数量
            current_exposure: 当前总敞口
            
        Returns:
            投资决策
        """
        decision = {
            'action': 'hold',
            'reasoning': '',
            'confidence': score_result.confidence,
            'risk_assessment': score_result.risk_level,
            'score': score_result.final_score
        }
        
        score = score_result.normalized_score
        
        # 进入条件检查
        if (score >= self.thresholds['enter_threshold'] and 
            current_positions < self.thresholds['max_positions']):
            
            decision['action'] = 'enter'
            decision['reasoning'] = (
                f"评分{score_result.final_score:.1f}超过进入阈值"
                f"{self.thresholds['enter_threshold']*100:.0f}%，"
                f"且未达到最大持仓数限制({self.thresholds['max_positions']})"
            )
        
        # 退出条件检查
        elif score <= self.thresholds['exit_threshold']:
            decision['action'] = 'exit'
            decision['reasoning'] = (
                f"评分{score_result.final_score:.1f}低于退出阈值"
                f"{self.thresholds['exit_threshold']*100:.0f}%"
            )
        
        # 极端风险检查
        elif score_result.risk_level == 'critical':
            decision['action'] = 'exit'
            decision['reasoning'] = "检测到极端风险(ExtremeRisk = true)"
        
        # 高分池子识别
        elif score >= self.thresholds['high_score_threshold']:
            decision['high_quality'] = True
            decision['reasoning'] = f"高质量池子(评分{score_result.final_score:.1f})"
        
        # 添加警告信息
        if score_result.warnings:
            decision['warnings'] = score_result.warnings
        
        return decision
    
    def should_rebalance(self, 
                        current_price: float,
                        range_lower: float,
                        range_upper: float,
                        fee_estimate: float,
                        gas_cost: float) -> Dict[str, Any]:
        """
        判断是否需要区间调整/再平衡
        
        Args:
            current_price: 当前价格
            range_lower: 区间下限
            range_upper: 区间上限
            fee_estimate: 预估手续费收入
            gas_cost: Gas成本
            
        Returns:
            再平衡决策
        """
        # 计算价格偏离程度
        range_center = (range_lower + range_upper) / 2
        range_width = range_upper - range_lower
        
        price_deviation = abs(current_price - range_center) / (range_width / 2)
        
        # 计算费用收益比
        fee_to_gas_ratio = fee_estimate / max(gas_cost, 1e-6)  # 避免除零
        
        should_rebalance = (
            price_deviation > self.thresholds['rebalance_threshold'] and
            fee_to_gas_ratio >= 1.2
        )
        
        return {
            'should_rebalance': should_rebalance,
            'price_deviation': price_deviation,
            'fee_to_gas_ratio': fee_to_gas_ratio,
            'reasoning': (
                f"价格偏离{price_deviation:.1%}，费用收益比{fee_to_gas_ratio:.2f}"
                if should_rebalance else
                f"价格偏离{price_deviation:.1%}不足或费用收益比{fee_to_gas_ratio:.2f}过低"
            )
        }
    
    def _parse_pool_metrics(self, pool_data: Dict[str, Any]) -> PoolMetrics:
        """解析池子指标数据"""
        try:
            # 必需字段
            required_fields = ['pool_id', 'token0', 'token1', 'chain']
            for field in required_fields:
                if field not in pool_data:
                    raise ValueError(f"缺失必需字段: {field}")
            
            # 数值字段处理
            apr = float(pool_data.get('fee_apr', 0))
            volume_24h = float(pool_data.get('volume_24h', 0))
            tvl = float(pool_data.get('tvl_usd', pool_data.get('tvl', 0)))
            fee_rate = float(pool_data.get('fee_rate', 0))
            
            # 波动率估算(如果没有提供)
            volatility = pool_data.get('volatility')
            if volatility is None:
                volatility = self._estimate_volatility(
                    pool_data['token0'], pool_data['token1']
                )
            else:
                volatility = float(volatility)
            
            return PoolMetrics(
                pool_id=pool_data['pool_id'],
                token0=pool_data['token0'].upper(),
                token1=pool_data['token1'].upper(),
                chain=pool_data['chain'],
                apr=apr,
                volume_24h=volume_24h,
                tvl=tvl,
                volatility=volatility,
                fee_rate=fee_rate,
                liquidity_depth=pool_data.get('liquidity_depth'),
                price_impact=pool_data.get('price_impact'),
                trading_frequency=pool_data.get('trading_frequency')
            )
            
        except (ValueError, TypeError, KeyError) as e:
            raise ValueError(f"池子数据解析失败: {e}")
    
    def _estimate_volatility(self, token0: str, token1: str) -> float:
        """估算代币对波动率"""
        # 稳定币列表
        stablecoins = {'USDT', 'USDC', 'BUSD', 'DAI', 'FRAX', 'TUSD', 'FDUSD'}
        
        # 主流币列表(相对低波动)
        major_coins = {'BTC', 'ETH', 'BNB', 'SOL', 'BTCB', 'WBNB', 'WETH', 'WBTC'}
        
        token0 = token0.upper()
        token1 = token1.upper()
        
        both_stable = token0 in stablecoins and token1 in stablecoins
        one_stable = token0 in stablecoins or token1 in stablecoins
        both_major = token0 in major_coins and token1 in major_coins
        one_major = token0 in major_coins or token1 in major_coins
        
        if both_stable:
            return 2.0    # 极低波动
        elif one_stable and one_major:
            return 8.0    # 低波动
        elif both_major:
            return 20.0   # 中等波动
        elif one_stable:
            return 35.0   # 中高波动
        elif one_major:
            return 45.0   # 高波动
        else:
            return 60.0   # 极高波动
    
    def _determine_risk_level(self, metrics: PoolMetrics, score: float) -> str:
        """确定风险等级"""
        # 基于多个因素综合判断
        risk_factors = []
        
        # TVL风险
        if metrics.tvl < 100000:  # TVL < 10万USD
            risk_factors.append('low_tvl')
        
        # 波动率风险
        if metrics.volatility > 50:
            risk_factors.append('high_volatility')
        elif metrics.volatility > 30:
            risk_factors.append('medium_volatility')
        
        # 交易量风险
        volume_ratio = metrics.volume_24h / max(metrics.tvl, 1)
        if volume_ratio < 0.01:  # 日交易量/TVL < 1%
            risk_factors.append('low_activity')
        
        # 评分风险
        if score < 0.3:
            risk_factors.append('low_score')
        
        # 综合判断
        if len(risk_factors) >= 3 or 'low_score' in risk_factors:
            return 'critical'
        elif len(risk_factors) >= 2 or 'high_volatility' in risk_factors:
            return 'high'
        elif len(risk_factors) >= 1:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_confidence(self, metrics: PoolMetrics, pool_data: Dict[str, Any]) -> float:
        """计算评分置信度"""
        confidence = 1.0
        
        # 数据完整性检查
        if metrics.tvl <= 0:
            confidence *= 0.7
        
        if metrics.volume_24h <= 0:
            confidence *= 0.8
        
        # 数据新鲜度检查
        timestamp = pool_data.get('timestamp')
        if timestamp:
            # 如果数据超过1小时，降低置信度
            # 这里简化处理，实际应该检查时间差
            pass
        
        # TVL规模影响置信度
        if metrics.tvl < 10000:
            confidence *= 0.5
        elif metrics.tvl < 100000:
            confidence *= 0.8
        
        # 交易活跃度影响置信度
        volume_ratio = metrics.volume_24h / max(metrics.tvl, 1)
        if volume_ratio < 0.001:  # 几乎无交易
            confidence *= 0.6
        
        return max(0.1, min(1.0, confidence))
    
    def _generate_warnings(self, metrics: PoolMetrics, score: float) -> list:
        """生成警告信息"""
        warnings = []
        
        if metrics.tvl < 10000:
            warnings.append("TVL过低，流动性风险较高")
        
        if metrics.volume_24h / max(metrics.tvl, 1) < 0.001:
            warnings.append("交易活跃度极低")
        
        if metrics.volatility > 60:
            warnings.append("价格波动率极高")
        
        if metrics.apr > 200:
            warnings.append("APR异常高，可能存在风险")
        
        if score < 0.2:
            warnings.append("综合评分过低，不建议投资")
        
        return warnings
    
    def _validate_weights(self) -> None:
        """验证权重配置"""
        total_positive = sum(w for w in self.weights.values() if w > 0)
        total_negative = abs(sum(w for w in self.weights.values() if w < 0))
        
        # 权重总和应该接近1.0
        total_weight = total_positive - total_negative
        if abs(total_weight - 1.0) > 0.01:
            logger.warning("weights_sum_not_one", 
                         total_weight=total_weight,
                         weights=self.weights)
        
        # 检查必需的权重
        required_weights = ['apr', 'volume', 'depth', 'volatility', 'il']
        for weight_name in required_weights:
            if weight_name not in self.weights:
                raise ValueError(f"缺失必需权重: {weight_name}")
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        return {
            'name': 'FiveFactorScorer',
            'version': '1.0',
            'description': '五因子流动性池评分算法',
            'formula': 'score = 0.45*APR_norm + 0.25*Volume_norm + 0.10*Depth_norm - 0.15*Volatility_norm - 0.05*IL_est',
            'weights': self.weights.copy(),
            'thresholds': self.thresholds.copy(),
            'normalization_info': self.normalizer.get_params()
        }