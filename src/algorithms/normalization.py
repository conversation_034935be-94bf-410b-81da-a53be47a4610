"""
归一化算法模块
实现各项指标的归一化函数，按照任务规格：
- APR_norm = min(APR / 500%, 1.0)  # 500% APR 为满分
- Volume_norm = min(log10(volume_24h / 100k), 1.0)  # 100k 以上为满分
- Depth_norm = min(TVL / 1M, 1.0)  # 1M TVL 为满分
- Volatility_norm = min(σ_24h / 0.5, 1.0)  # 50% 波动率为最高风险
"""

import math
from typing import Dict, Any, Optional
import structlog

logger = structlog.get_logger(__name__)


class Normalizer:
    """数值归一化处理器"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        """
        初始化归一化器
        
        Args:
            params: 归一化参数配置
        """
        # 默认归一化参数
        default_params = {
            'apr_max': 500.0,           # APR满分阈值: 500%
            'volume_base': 100000.0,    # 交易量基准: 100k USD
            'tvl_max': 1000000.0,       # TVL满分阈值: 1M USD
            'volatility_max': 50.0,     # 波动率最高风险: 50%
            'min_volume': 1000.0,       # 最小有效交易量
            'min_tvl': 1000.0,          # 最小有效TVL
        }
        
        # 合并用户参数
        self.params = default_params.copy()
        if params:
            self.params.update(params)
        
        logger.debug("normalizer_initialized", params=self.params)
    
    def normalize_apr(self, apr: float) -> float:
        """
        归一化APR
        公式: APR_norm = min(APR / 500%, 1.0)
        
        Args:
            apr: 年化收益率(百分比形式，如15.5表示15.5%)
            
        Returns:
            归一化后的APR值 (0-1)
        """
        if apr < 0:
            logger.warning("negative_apr", apr=apr)
            return 0.0
        
        # 超高APR可能有风险，给予警告
        if apr > self.params['apr_max'] * 2:
            logger.warning("extremely_high_apr", apr=apr)
        
        normalized = min(apr / self.params['apr_max'], 1.0)
        
        logger.debug("apr_normalized", 
                    original=apr, 
                    normalized=normalized,
                    max_threshold=self.params['apr_max'])
        
        return normalized
    
    def normalize_volume(self, volume_24h: float) -> float:
        """
        归一化交易量
        公式: Volume_norm = min(log10(volume_24h / 100k), 1.0)
        
        Args:
            volume_24h: 24小时交易量(USD)
            
        Returns:
            归一化后的交易量值 (0-1)
        """
        if volume_24h <= 0:
            return 0.0
        
        # 确保最小交易量
        volume_24h = max(volume_24h, self.params['min_volume'])
        
        try:
            # 计算相对于基准的倍数
            volume_ratio = volume_24h / self.params['volume_base']
            
            # 使用对数归一化，使得100k为满分(log10(1) = 0，我们需要调整)
            # 当volume = 100k时，ratio = 1，log10(1) = 0
            # 当volume = 1M时，ratio = 10，log10(10) = 1
            # 所以我们实际上希望100k就是满分，这里需要调整公式
            
            if volume_ratio >= 1.0:
                # 交易量达到或超过100k，给满分
                normalized = 1.0
            else:
                # 交易量低于100k，使用线性归一化
                normalized = volume_ratio
            
            # 另一种实现：使用对数但调整基准
            # normalized = min(math.log10(volume_ratio + 1), 1.0)
            
            logger.debug("volume_normalized",
                        original=volume_24h,
                        ratio=volume_ratio,
                        normalized=normalized,
                        base_threshold=self.params['volume_base'])
            
            return normalized
            
        except (ValueError, ZeroDivisionError) as e:
            logger.error("volume_normalization_failed", 
                        volume=volume_24h, 
                        error=str(e))
            return 0.0
    
    def normalize_depth(self, tvl: float) -> float:
        """
        归一化流动性深度(TVL)
        公式: Depth_norm = min(TVL / 1M, 1.0)
        
        Args:
            tvl: 总锁定价值(USD)
            
        Returns:
            归一化后的深度值 (0-1)
        """
        if tvl <= 0:
            return 0.0
        
        # 确保最小TVL
        tvl = max(tvl, self.params['min_tvl'])
        
        normalized = min(tvl / self.params['tvl_max'], 1.0)
        
        logger.debug("tvl_normalized",
                    original=tvl,
                    normalized=normalized,
                    max_threshold=self.params['tvl_max'])
        
        return normalized
    
    def normalize_volatility(self, volatility: float) -> float:
        """
        归一化波动率(风险因子)
        公式: Volatility_norm = min(σ_24h / 50%, 1.0)
        注意：这是风险因子，值越高风险越大
        
        Args:
            volatility: 价格波动率(百分比形式，如25.5表示25.5%)
            
        Returns:
            归一化后的波动率值 (0-1)，1表示最高风险
        """
        if volatility < 0:
            logger.warning("negative_volatility", volatility=volatility)
            return 0.0
        
        normalized = min(volatility / self.params['volatility_max'], 1.0)
        
        logger.debug("volatility_normalized",
                    original=volatility,
                    normalized=normalized,
                    max_threshold=self.params['volatility_max'])
        
        return normalized
    
    def normalize_all_metrics(self, 
                            apr: float,
                            volume_24h: float,
                            tvl: float,
                            volatility: float) -> Dict[str, float]:
        """
        一次性归一化所有指标
        
        Args:
            apr: 年化收益率
            volume_24h: 24小时交易量
            tvl: 总锁定价值
            volatility: 价格波动率
            
        Returns:
            归一化后的指标字典
        """
        return {
            'apr_norm': self.normalize_apr(apr),
            'volume_norm': self.normalize_volume(volume_24h),
            'depth_norm': self.normalize_depth(tvl),
            'volatility_norm': self.normalize_volatility(volatility)
        }
    
    def denormalize_apr(self, normalized_apr: float) -> float:
        """
        反向归一化APR
        
        Args:
            normalized_apr: 归一化的APR值 (0-1)
            
        Returns:
            原始APR值
        """
        return normalized_apr * self.params['apr_max']
    
    def denormalize_volume(self, normalized_volume: float) -> float:
        """
        反向归一化交易量(近似)
        
        Args:
            normalized_volume: 归一化的交易量值 (0-1)
            
        Returns:
            近似的原始交易量值
        """
        return normalized_volume * self.params['volume_base']
    
    def denormalize_tvl(self, normalized_tvl: float) -> float:
        """
        反向归一化TVL
        
        Args:
            normalized_tvl: 归一化的TVL值 (0-1)
            
        Returns:
            原始TVL值
        """
        return normalized_tvl * self.params['tvl_max']
    
    def denormalize_volatility(self, normalized_volatility: float) -> float:
        """
        反向归一化波动率
        
        Args:
            normalized_volatility: 归一化的波动率值 (0-1)
            
        Returns:
            原始波动率值
        """
        return normalized_volatility * self.params['volatility_max']
    
    def get_normalization_bounds(self) -> Dict[str, Dict[str, float]]:
        """
        获取各项指标的归一化边界
        
        Returns:
            归一化边界信息
        """
        return {
            'apr': {
                'min': 0.0,
                'max': self.params['apr_max'],
                'description': f"APR归一化，{self.params['apr_max']}%为满分"
            },
            'volume': {
                'min': self.params['min_volume'],
                'target': self.params['volume_base'],
                'description': f"交易量归一化，{self.params['volume_base']:,.0f}USD为满分"
            },
            'tvl': {
                'min': self.params['min_tvl'],
                'max': self.params['tvl_max'],
                'description': f"TVL归一化，{self.params['tvl_max']:,.0f}USD为满分"
            },
            'volatility': {
                'min': 0.0,
                'max': self.params['volatility_max'],
                'description': f"波动率归一化，{self.params['volatility_max']}%为最高风险"
            }
        }
    
    def validate_input_ranges(self, 
                            apr: float,
                            volume_24h: float,
                            tvl: float,
                            volatility: float) -> Dict[str, list]:
        """
        验证输入数据的合理性
        
        Args:
            apr: 年化收益率
            volume_24h: 24小时交易量
            tvl: 总锁定价值
            volatility: 价格波动率
            
        Returns:
            验证结果，包含警告和错误信息
        """
        warnings = []
        errors = []
        
        # APR检查
        if apr < 0:
            errors.append(f"APR不能为负数: {apr}")
        elif apr > self.params['apr_max'] * 3:
            warnings.append(f"APR异常高: {apr}%，可能存在风险")
        elif apr > self.params['apr_max']:
            warnings.append(f"APR超过满分阈值: {apr}% > {self.params['apr_max']}%")
        
        # 交易量检查
        if volume_24h < 0:
            errors.append(f"交易量不能为负数: {volume_24h}")
        elif volume_24h < self.params['min_volume']:
            warnings.append(f"交易量过低: {volume_24h} < {self.params['min_volume']}")
        
        # TVL检查
        if tvl < 0:
            errors.append(f"TVL不能为负数: {tvl}")
        elif tvl < self.params['min_tvl']:
            warnings.append(f"TVL过低: {tvl} < {self.params['min_tvl']}")
        
        # 波动率检查
        if volatility < 0:
            errors.append(f"波动率不能为负数: {volatility}")
        elif volatility > 100:
            warnings.append(f"波动率异常高: {volatility}%")
        
        # 逻辑一致性检查
        if volume_24h > 0 and tvl > 0:
            volume_ratio = volume_24h / tvl
            if volume_ratio > 10:  # 日交易量超过TVL的10倍
                warnings.append(f"交易量/TVL比例异常高: {volume_ratio:.1f}")
            elif volume_ratio < 0.001:  # 日交易量低于TVL的0.1%
                warnings.append(f"交易量/TVL比例过低: {volume_ratio:.4f}")
        
        return {
            'warnings': warnings,
            'errors': errors,
            'is_valid': len(errors) == 0
        }
    
    def get_params(self) -> Dict[str, Any]:
        """获取归一化参数"""
        return self.params.copy()
    
    def update_params(self, new_params: Dict[str, Any]) -> None:
        """
        更新归一化参数
        
        Args:
            new_params: 新的参数配置
        """
        old_params = self.params.copy()
        self.params.update(new_params)
        
        logger.info("normalization_params_updated",
                   old_params=old_params,
                   new_params=self.params)
    
    def get_normalization_stats(self, 
                              values: Dict[str, list]) -> Dict[str, Dict[str, float]]:
        """
        计算一批数据的归一化统计信息
        
        Args:
            values: 包含各指标数值列表的字典
            
        Returns:
            归一化统计信息
        """
        stats = {}
        
        for metric, value_list in values.items():
            if not value_list:
                continue
            
            # 归一化所有值
            if metric == 'apr':
                normalized_values = [self.normalize_apr(v) for v in value_list]
            elif metric == 'volume':
                normalized_values = [self.normalize_volume(v) for v in value_list]
            elif metric == 'tvl':
                normalized_values = [self.normalize_depth(v) for v in value_list]
            elif metric == 'volatility':
                normalized_values = [self.normalize_volatility(v) for v in value_list]
            else:
                continue
            
            # 计算统计信息
            stats[metric] = {
                'count': len(normalized_values),
                'mean': sum(normalized_values) / len(normalized_values),
                'min': min(normalized_values),
                'max': max(normalized_values),
                'saturated_count': sum(1 for v in normalized_values if v >= 1.0),
                'zero_count': sum(1 for v in normalized_values if v <= 0.0)
            }
        
        return stats


class AdaptiveNormalizer(Normalizer):
    """自适应归一化器，可以根据历史数据调整归一化参数"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        super().__init__(params)
        self.historical_data = {
            'apr': [],
            'volume': [],
            'tvl': [],
            'volatility': []
        }
        self.adaptation_enabled = True
        self.min_samples = 100  # 最小样本数量
    
    def add_sample(self, 
                   apr: float,
                   volume_24h: float,
                   tvl: float,
                   volatility: float) -> None:
        """
        添加历史样本数据
        
        Args:
            apr: 年化收益率
            volume_24h: 24小时交易量
            tvl: 总锁定价值
            volatility: 价格波动率
        """
        if not self.adaptation_enabled:
            return
        
        self.historical_data['apr'].append(apr)
        self.historical_data['volume'].append(volume_24h)
        self.historical_data['tvl'].append(tvl)
        self.historical_data['volatility'].append(volatility)
        
        # 保持历史数据大小限制
        max_history = 1000
        for key in self.historical_data:
            if len(self.historical_data[key]) > max_history:
                self.historical_data[key] = self.historical_data[key][-max_history:]
    
    def adapt_parameters(self) -> bool:
        """
        基于历史数据自适应调整归一化参数
        
        Returns:
            是否进行了参数调整
        """
        if not self.adaptation_enabled:
            return False
        
        # 检查是否有足够的样本
        for key, data in self.historical_data.items():
            if len(data) < self.min_samples:
                logger.debug("insufficient_samples_for_adaptation",
                           metric=key, 
                           samples=len(data),
                           required=self.min_samples)
                return False
        
        updated = False
        old_params = self.params.copy()
        
        # 调整APR参数（使用95分位数）
        apr_95th = sorted(self.historical_data['apr'])[int(len(self.historical_data['apr']) * 0.95)]
        if apr_95th > self.params['apr_max'] * 1.2:
            self.params['apr_max'] = apr_95th * 1.1
            updated = True
        
        # 调整交易量参数（使用75分位数）
        volume_75th = sorted(self.historical_data['volume'])[int(len(self.historical_data['volume']) * 0.75)]
        if volume_75th > self.params['volume_base'] * 2:
            self.params['volume_base'] = volume_75th
            updated = True
        
        # 调整TVL参数（使用90分位数）
        tvl_90th = sorted(self.historical_data['tvl'])[int(len(self.historical_data['tvl']) * 0.9)]
        if tvl_90th > self.params['tvl_max'] * 1.5:
            self.params['tvl_max'] = tvl_90th
            updated = True
        
        # 调整波动率参数（使用90分位数）
        vol_90th = sorted(self.historical_data['volatility'])[int(len(self.historical_data['volatility']) * 0.9)]
        if vol_90th > self.params['volatility_max'] * 1.2:
            self.params['volatility_max'] = vol_90th
            updated = True
        
        if updated:
            logger.info("normalization_parameters_adapted",
                       old_params=old_params,
                       new_params=self.params)
        
        return updated