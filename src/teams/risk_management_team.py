"""
風險管理團隊 - 持續評估風險並提供對沖建議
"""

from typing import Dict, List, Any
from datetime import datetime
import structlog

from .base_team import DyFlowBaseTeam

logger = structlog.get_logger(__name__)


class RiskManagementTeam(DyFlowBaseTeam):
    """風險管理團隊 - 監控並評估LP風險"""

    def __init__(self, name: str, description: str = "", **kwargs):
        super().__init__(name, description, **kwargs)

        # 團隊成員：風險分析師與策略顧問
        self.add_agent(self.create_risk_analyst_agent())
        self.add_agent(self.create_strategy_advisor_agent())

        self.logger.info(
            "risk_management_team_initialized", agents_count=len(self.agents)
        )

    async def assess_risks(self, alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根據警報訊息生成風險評估報告"""
        risk_prompt = f"""
        目前收到的風險警報：{alerts}
        請綜合評估這些警報對整體LP倉位的影響，並提出對沖或調整建議。
        """
        response = await self.run(risk_prompt)

        return {
            "assessment_id": f"risk_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat(),
            "assessment": response.content if hasattr(response, 'content') else str(response),
            "status": "completed",
        }
