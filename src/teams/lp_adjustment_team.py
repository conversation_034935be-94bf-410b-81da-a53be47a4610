"""
LP调整团队 - 负责根據池子表現建議並執行調整操作
"""

from typing import Dict, List, Any
from datetime import datetime
import structlog

from .base_team import DyFlowBaseTeam

logger = structlog.get_logger(__name__)


class LPAdjustmentTeam(DyFlowBaseTeam):
    """LP调整團隊 - 建議並執行LP範圍調整"""

    def __init__(self, name: str, description: str = "", **kwargs):
        super().__init__(name, description, **kwargs)

        # 團隊成員：策略顧問與執行管理
        self.add_agent(self.create_strategy_advisor_agent())
        self.add_agent(self.create_execution_manager_agent())

        self.logger.info(
            "lp_adjustment_team_initialized", agents_count=len(self.agents)
        )

    async def adjust_positions(self, positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根據當前持倉生成調整計劃並執行"""
        # 1. 生成策略建議
        strategy_prompt = f"""
        當前LP持倉: {positions}

        請評估每個持倉的表現與風險，提出需要調整的範圍或退出建議，
        如需重新平衡，給出目標區間和倉位大小。
        """
        strategy_response = await self.run(strategy_prompt)

        # 2. 執行策略
        execution_prompt = f"""
        根據以下調整計劃執行操作，並給出執行摘要：
        {strategy_response.content if hasattr(strategy_response, 'content') else str(strategy_response)}
        """
        execution_response = await self.run(execution_prompt)

        return {
            "adjustment_id": f"adjust_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat(),
            "strategy": strategy_response.content if hasattr(strategy_response, 'content') else str(strategy_response),
            "execution": execution_response.content if hasattr(execution_response, 'content') else str(execution_response),
            "status": "completed",
        }
