"""
DyFlow 数据提供者基类
定义数据获取的标准接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import asyncio
import structlog

from ..utils.models import PoolMetrics, TokenInfo
from ..utils.exceptions import DataProviderException

logger = structlog.get_logger(__name__)


class BaseDataProvider(ABC):
    """数据提供者基类"""
    
    def __init__(self, config: Any, chain_name: str):
        self.config = config
        self.chain_name = chain_name
        self.is_initialized = False
        self._connection_healthy = False
    
    async def initialize(self):
        """初始化数据提供者"""
        try:
            await self._setup_connections()
            await self._verify_connections()
            self.is_initialized = True
            logger.info("data_provider_initialized", chain=self.chain_name)
        except Exception as e:
            logger.error("data_provider_init_failed", chain=self.chain_name, error=str(e))
            raise DataProviderException(f"数据提供者初始化失败 {self.chain_name}: {e}")
    
    @abstractmethod
    async def _setup_connections(self):
        """设置网络连接"""
        pass
    
    @abstractmethod
    async def _verify_connections(self):
        """验证网络连接"""
        pass
    
    @abstractmethod
    async def get_pool_metrics(self, pool_address: str) -> PoolMetrics:
        """获取池子指标数据"""
        pass
    
    @abstractmethod
    async def get_token_prices(self, tokens: List[str]) -> Dict[str, float]:
        """获取代币价格"""
        pass
    
    @abstractmethod
    async def get_pool_liquidity(self, pool_address: str) -> float:
        """获取池子流动性"""
        pass
    
    @abstractmethod
    async def get_pool_volume_24h(self, pool_address: str) -> float:
        """获取池子24小时交易量"""
        pass
    
    async def check_connection(self) -> bool:
        """检查网络连接状态"""
        try:
            await self._verify_connections()
            self._connection_healthy = True
            return True
        except Exception:
            self._connection_healthy = False
            return False
    
    def is_healthy(self) -> bool:
        """检查提供者是否健康"""
        return self.is_initialized and self._connection_healthy
    
    async def get_multiple_pool_metrics(self, pool_addresses: List[str]) -> List[PoolMetrics]:
        """批量获取多个池子的指标"""
        tasks = []
        for address in pool_addresses:
            task = self.get_pool_metrics(address)
            tasks.append(task)
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            valid_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error("pool_metrics_fetch_failed", 
                               pool=pool_addresses[i], 
                               chain=self.chain_name,
                               error=str(result))
                else:
                    valid_results.append(result)
            
            return valid_results
        except Exception as e:
            logger.error("batch_pool_metrics_failed", chain=self.chain_name, error=str(e))
            raise DataProviderException(f"批量获取池子数据失败: {e}")