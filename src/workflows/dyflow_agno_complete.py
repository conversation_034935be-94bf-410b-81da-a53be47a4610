"""
DyFlow v3.3 完整 Agno 架構
實現 PRD 定義的 7 個 Agents + 8-phase 啟動序列
移除 NATS 依賴，使用純 Agno Framework 通訊
"""

import asyncio
import structlog
from typing import Dict, Any, List, Optional, Iterator
from datetime import datetime
from pathlib import Path
from enum import Enum

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.team import Team
    from agno.workflow import Workflow, RunResponse, RunEvent
    from agno.models.ollama import Ollama
    from agno.storage.sqlite import SqliteStorage
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Workflow:
        pass
    class BaseModel:
        pass

logger = structlog.get_logger(__name__)

# ========== Phase 定義 ==========

class Phase(Enum):
    """DyFlow v3.3 八階段啟動序列"""
    PHASE_0_INIT = 0          # SupervisorAgent - 讀取 YAML/ENV、Vault 發放密鑰
    PHASE_1_HEALTH = 1        # HealthGuardAgent + ConnProbeTool - RPC/Subgraph/DB/UI ≥ 90% 健康
    PHASE_2_UI = 2            # WebUI & PrometheusExporter - http://localhost:3000 200 OK
    PHASE_3_WALLET = 3        # WalletProbeTool - MPC 簽名 & nonce 測試通過
    PHASE_4_MARKET = 4        # MarketIntelAgent - 開始推送池事件
    PHASE_5_PORTFOLIO = 5     # PortfolioManagerAgent - NAV ≥ 0 且資金鎖可寫入
    PHASE_6_STRATEGY = 6      # StrategyAgent - 產生 LPPlan.approved
    PHASE_7_EXECUTION = 7     # ExecutionAgent - Tx ≥ 1 筆成功廣播
    PHASE_8_RISK = 8          # RiskSentinelAgent + FeeCollectorTool - IL_net、VaR 均在限內

# ========== Agno 結構化輸出模型 ==========

class PhaseResult(BaseModel):
    """階段執行結果"""
    phase_id: int = Field(..., description="階段ID")
    phase_name: str = Field(..., description="階段名稱")
    agent_name: str = Field(..., description="負責的Agent名稱")
    status: str = Field(..., description="執行狀態", pattern="^(completed|failed|running|pending)$")
    started_at: Optional[datetime] = Field(None, description="開始時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    duration_seconds: Optional[float] = Field(None, description="執行時長")
    result_data: Optional[Dict[str, Any]] = Field(None, description="執行結果數據")
    error_message: Optional[str] = Field(None, description="錯誤信息")

class SystemStatus(BaseModel):
    """系統狀態"""
    current_phase: int = Field(..., description="當前階段")
    total_phases: int = Field(default=9, description="總階段數")
    overall_status: str = Field(..., description="整體狀態")
    progress_percentage: float = Field(..., description="進度百分比")
    active_agents: List[str] = Field(default_factory=list, description="活躍的Agents")
    phase_results: List[PhaseResult] = Field(default_factory=list, description="各階段結果")

class AgentCommunication(BaseModel):
    """Agent 間通訊消息"""
    from_agent: str = Field(..., description="發送方Agent")
    to_agent: str = Field(..., description="接收方Agent")
    message_type: str = Field(..., description="消息類型")
    content: Dict[str, Any] = Field(..., description="消息內容")
    timestamp: datetime = Field(default_factory=datetime.now, description="時間戳")

# ========== DyFlow 完整 Agno Workflow ==========

class DyFlowCompleteWorkflow(Workflow):
    """
    DyFlow v3.3 完整 Agno Workflow
    實現 PRD 定義的 7 個 Agents 和 8-phase 啟動序列
    替代 NATS 消息總線，使用 Agno 內建通訊
    """
    
    description: str = "DyFlow v3.3 complete automated LP strategy system using Agno Framework"
    
    def __init__(self, session_id: str = None, storage: SqliteStorage = None):
        # 設置存儲
        if storage is None:
            storage = SqliteStorage(
                table_name="dyflow_complete_workflows",
                db_file="data/agno_workflows.db"
            )
        
        super().__init__(
            session_id=session_id or f"dyflow-complete-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            storage=storage
        )
        
        # 初始化狀態
        self.current_phase = 0
        self.phase_results: List[PhaseResult] = []
        self.agent_communications: List[AgentCommunication] = []
        
        # 初始化 7 個 DyFlow Agents
        self._initialize_agents()
        
        # 創建 Agent Team (協調模式)
        self._create_agent_team()
    
    def _initialize_agents(self):
        """初始化 PRD 定義的 7 個 Agents"""
        if not AGNO_AVAILABLE:
            logger.error("agno_framework_not_available")
            return
        
        try:
            # 1. SupervisorAgent - Phase 0
            self.supervisor_agent = Agent(
                name="SupervisorAgent",
                role="System initialization and lifecycle management",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are the DyFlow system supervisor responsible for Phase 0 initialization.",
                    "Read YAML/ENV configuration, manage Vault key distribution.",
                    "Coordinate the 8-phase startup sequence.",
                    "Ensure all components are properly initialized.",
                    "End responses with 'Supervisor-Phase-0-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 2. HealthGuardAgent - Phase 1
            self.health_agent = Agent(
                name="HealthGuardAgent", 
                role="System health monitoring and validation",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 1 health checks.",
                    "Monitor RPC/Subgraph/DB/UI health, ensure ≥ 90% health threshold.",
                    "Check BSC RPC, Solana RPC, PancakeSwap subgraph, Meteora API, database.",
                    "Report detailed health status and recommendations.",
                    "End responses with 'Health-Phase-1-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 3. MarketIntelAgent - Phase 4
            self.market_intel_agent = Agent(
                name="MarketIntelAgent",
                role="Market data collection and pool scanning",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 4 market intelligence.",
                    "Scan BSC (PancakeSwap v3) and Solana (Meteora DLMM v2) pools.",
                    "Apply filters: TVL ≥ $10M, Created ≤ 2 days, Fee/TVL ≥ 5%, 24h Fees > $5.",
                    "Collect real-time market data and identify LP opportunities.",
                    "End responses with 'Market-Phase-4-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 4. PortfolioManagerAgent - Phase 5
            self.portfolio_agent = Agent(
                name="PortfolioManagerAgent",
                role="Portfolio management and NAV calculation",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 5 portfolio management.",
                    "Ensure NAV ≥ 0 and fund locks are writable.",
                    "Manage portfolio allocation and rebalancing.",
                    "Calculate risk-adjusted returns and diversification.",
                    "End responses with 'Portfolio-Phase-5-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 5. StrategyAgent - Phase 6
            self.strategy_agent = Agent(
                name="StrategyAgent",
                role="LP strategy generation and optimization",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 6 strategy generation.",
                    "Generate LPPlan.approved with 4 strategy types:",
                    "- SPOT_BALANCED (symmetric liquidity)",
                    "- CURVE_BALANCED (curve distribution)", 
                    "- BID_ASK_BALANCED (bid-ask spread)",
                    "- SPOT_IMBALANCED_DAMM (single-sided liquidity)",
                    "Apply risk assessment and optimization.",
                    "End responses with 'Strategy-Phase-6-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 6. ExecutionAgent - Phase 7
            self.execution_agent = Agent(
                name="ExecutionAgent",
                role="Transaction execution and monitoring",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 7 transaction execution.",
                    "Execute LP transactions on BSC and Solana.",
                    "Ensure ≥ 1 transaction successfully broadcast.",
                    "Monitor transaction status and handle failures.",
                    "Use wallet signer and DEX router tools.",
                    "End responses with 'Execution-Phase-7-Complete'."
                ],
                show_tool_calls=True
            )
            
            # 7. RiskSentinelAgent - Phase 8
            self.risk_agent = Agent(
                name="RiskSentinelAgent",
                role="Risk monitoring and portfolio protection",
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                instructions=[
                    "You are responsible for Phase 8 risk monitoring.",
                    "Monitor IL_net (Impermanent Loss) and VaR (Value at Risk) metrics.",
                    "Ensure IL_net < -8% and VaR_95 > 4% thresholds.",
                    "Trigger emergency exits when risk limits exceeded.",
                    "Activate FeeCollectorTool for daily fee collection at UTC 02:00.",
                    "End responses with 'Risk-Phase-8-Complete'."
                ],
                show_tool_calls=True
            )
            
            logger.info("dyflow_7_agents_initialized", 
                       agents=["SupervisorAgent", "HealthGuardAgent", "MarketIntelAgent", 
                              "PortfolioManagerAgent", "StrategyAgent", "ExecutionAgent", "RiskSentinelAgent"])
            
        except Exception as e:
            logger.error("dyflow_agents_initialization_failed", error=str(e))
            raise
    
    def _create_agent_team(self):
        """創建 DyFlow Agent Team (協調模式)"""
        if not AGNO_AVAILABLE:
            return
        
        try:
            # 創建協調模式的 Agent Team
            self.dyflow_team = Team(
                name="DyFlow v3.3 Complete Team",
                mode="coordinate",  # 使用協調模式
                model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
                members=[
                    self.supervisor_agent,
                    self.health_agent,
                    self.market_intel_agent,
                    self.portfolio_agent,
                    self.strategy_agent,
                    self.execution_agent,
                    self.risk_agent
                ],
                instructions=[
                    "You are the DyFlow v3.3 team coordinator managing the complete 8-phase startup sequence.",
                    "Execute phases sequentially: Init → Health → UI → Wallet → Market → Portfolio → Strategy → Execution → Risk.",
                    "Each phase must complete successfully before proceeding to the next.",
                    "Coordinate between the 7 specialized agents to ensure smooth workflow execution.",
                    "Handle failures gracefully and provide detailed status reports.",
                    "Replace NATS message bus with direct agent communication.",
                    "Ensure all PRD v3.3 requirements are met."
                ],
                show_members_responses=True,
                markdown=True,
                debug_mode=True
            )
            
            logger.info("dyflow_complete_team_created", 
                       team_name="DyFlow v3.3 Complete Team",
                       members_count=7)
            
        except Exception as e:
            logger.error("dyflow_team_creation_failed", error=str(e))
            raise

    def run(self, start_phase: int = 0) -> Iterator[RunResponse]:
        """
        執行 DyFlow 8-phase 啟動序列
        使用 Agno Team 協調各個階段，替代 NATS 消息總線
        """
        if not AGNO_AVAILABLE:
            yield RunResponse(
                content="Agno Framework not available. Cannot execute workflow.",
                event=RunEvent.workflow_completed
            )
            return

        logger.info("dyflow_complete_workflow_started",
                   start_phase=start_phase,
                   total_agents=7,
                   architecture="Pure Agno (No NATS)")

        try:
            # 執行 8-phase 序列
            for phase_id in range(start_phase, 9):
                yield from self._execute_phase(phase_id)

                # 檢查階段是否成功
                if self.phase_results and self.phase_results[-1].status == "failed":
                    yield RunResponse(
                        content=f"Phase {phase_id} failed. Stopping workflow.",
                        event=RunEvent.workflow_completed
                    )
                    return

            # 生成最終狀態報告
            final_status = self._generate_status_report()
            yield RunResponse(
                content=f"🎉 DyFlow v3.3 workflow completed successfully!\n\n{final_status}",
                event=RunEvent.workflow_completed
            )

        except Exception as e:
            logger.error("dyflow_complete_workflow_execution_failed", error=str(e))
            yield RunResponse(
                content=f"Workflow execution failed: {str(e)}",
                event=RunEvent.workflow_completed
            )

    def _execute_phase(self, phase_id: int) -> Iterator[RunResponse]:
        """執行單個階段，使用對應的 Agent"""
        phase_enum = Phase(phase_id)
        phase_name = phase_enum.name.replace('_', ' ').title()

        # 確定負責的 Agent
        agent_mapping = {
            0: ("SupervisorAgent", self.supervisor_agent),
            1: ("HealthGuardAgent", self.health_agent),
            2: ("WebUI", None),  # UI 啟動不需要特定 Agent
            3: ("WalletProbe", None),  # 錢包測試工具
            4: ("MarketIntelAgent", self.market_intel_agent),
            5: ("PortfolioManagerAgent", self.portfolio_agent),
            6: ("StrategyAgent", self.strategy_agent),
            7: ("ExecutionAgent", self.execution_agent),
            8: ("RiskSentinelAgent", self.risk_agent)
        }

        agent_name, agent = agent_mapping.get(phase_id, ("Unknown", None))

        logger.info("executing_phase",
                   phase_id=phase_id,
                   phase_name=phase_name,
                   agent_name=agent_name)

        # 記錄階段開始
        phase_result = PhaseResult(
            phase_id=phase_id,
            phase_name=phase_name,
            agent_name=agent_name,
            status="running",
            started_at=datetime.now()
        )

        yield RunResponse(
            content=f"🔄 Phase {phase_id}: {phase_name} - 開始執行 (Agent: {agent_name})",
            event=RunEvent.run_started
        )

        try:
            if agent:
                # 使用專門的 Agent 執行階段
                result = self._execute_phase_with_agent(phase_id, agent)
            else:
                # 使用 Team 協調執行階段
                result = self._execute_phase_with_team(phase_id)

            if result:
                phase_result.status = "completed"
                phase_result.completed_at = datetime.now()
                phase_result.duration_seconds = (phase_result.completed_at - phase_result.started_at).total_seconds()
                phase_result.result_data = {"execution_result": result}

                yield RunResponse(
                    content=f"✅ Phase {phase_id}: {phase_name} - 完成 ({phase_result.duration_seconds:.1f}s)",
                    event=RunEvent.run_completed
                )
            else:
                raise Exception("Phase execution returned no result")

        except Exception as e:
            phase_result.status = "failed"
            phase_result.completed_at = datetime.now()
            phase_result.error_message = str(e)

            yield RunResponse(
                content=f"❌ Phase {phase_id}: {phase_name} - 失敗: {str(e)}",
                event=RunEvent.run_completed
            )

        # 保存階段結果
        self.phase_results.append(phase_result)
        self.current_phase = phase_id + 1

        # 更新 session state
        self.session_state["current_phase"] = self.current_phase
        self.session_state["phase_results"] = [p.model_dump() for p in self.phase_results]

    def _execute_phase_with_agent(self, phase_id: int, agent: Agent) -> str:
        """使用專門的 Agent 執行階段"""
        phase_requirements = self._get_phase_requirements(phase_id)

        prompt = f"""
        Execute Phase {phase_id} for DyFlow v3.3 system:

        Phase Requirements:
        {phase_requirements}

        Current System State:
        - Current Phase: {self.current_phase}
        - Previous Phases: {[p.phase_name for p in self.phase_results if p.status == 'completed']}
        - Active Agents: 7 (SupervisorAgent, HealthGuardAgent, MarketIntelAgent, PortfolioManagerAgent, StrategyAgent, ExecutionAgent, RiskSentinelAgent)

        Please execute your phase responsibilities and provide detailed status.
        This replaces NATS message bus communication with direct agent coordination.
        """

        response = agent.run(prompt)
        return response.content if response else None

    def _execute_phase_with_team(self, phase_id: int) -> str:
        """使用 Team 協調執行階段"""
        phase_requirements = self._get_phase_requirements(phase_id)

        prompt = f"""
        Coordinate Phase {phase_id} execution for DyFlow v3.3 system:

        Phase Requirements:
        {phase_requirements}

        Current System State:
        - Current Phase: {self.current_phase}
        - Team Members: 7 specialized agents
        - Architecture: Pure Agno (No NATS message bus)

        Please coordinate the appropriate team members to complete this phase.
        Provide detailed status and results.
        """

        response = self.dyflow_team.run(prompt)
        return response.content if response else None

    def _get_phase_requirements(self, phase_id: int) -> str:
        """獲取階段要求 (根據 PRD v3.3)"""
        requirements = {
            0: "SupervisorAgent: 讀取 YAML/ENV 配置，初始化 Vault 密鑰分發，替代 NATS 初始化",
            1: "HealthGuardAgent: 檢查 RPC/Subgraph/DB/UI 健康狀態，要求 ≥ 90% 健康",
            2: "WebUI 啟動: 啟動 WebUI 和 PrometheusExporter，確保 http://localhost:3000 返回 200 OK",
            3: "錢包測試: 執行 MPC 簽名和 nonce 測試，確保錢包功能正常",
            4: "MarketIntelAgent: 啟動市場情報收集，掃描 BSC 和 Solana 池子，替代 NATS bus.pool",
            5: "PortfolioManagerAgent: 初始化投資組合管理，確保 NAV ≥ 0 且資金鎖可寫入",
            6: "StrategyAgent: 生成 LP 策略計劃，產生 LPPlan.approved，替代 NATS bus.plan",
            7: "ExecutionAgent: 執行交易，確保至少 1 筆交易成功廣播，替代 NATS bus.tx",
            8: "RiskSentinelAgent: 啟動風險監控，確保 IL_net 和 VaR 均在限制範圍內，替代 NATS bus.risk"
        }
        return requirements.get(phase_id, f"Execute phase {phase_id}")

    def _generate_status_report(self) -> str:
        """生成狀態報告"""
        completed_phases = len([p for p in self.phase_results if p.status == "completed"])
        failed_phases = len([p for p in self.phase_results if p.status == "failed"])

        report = f"""
## DyFlow v3.3 完整執行報告

**架構**: Pure Agno Framework (移除 NATS 依賴)
**Agents**: 7 個專門化 Agents
**整體狀態**: {'✅ 成功' if failed_phases == 0 else '❌ 部分失敗'}
**進度**: {completed_phases}/9 階段完成
**當前階段**: Phase {self.current_phase}

### 階段詳情:
"""

        for phase in self.phase_results:
            status_icon = "✅" if phase.status == "completed" else "❌" if phase.status == "failed" else "🔄"
            duration = f" ({phase.duration_seconds:.1f}s)" if phase.duration_seconds else ""
            report += f"- {status_icon} Phase {phase.phase_id}: {phase.phase_name} - {phase.agent_name}{duration}\n"
            if phase.error_message:
                report += f"  錯誤: {phase.error_message}\n"

        report += f"""
### Agent 狀態:
- ✅ SupervisorAgent (Phase 0)
- ✅ HealthGuardAgent (Phase 1)
- ✅ MarketIntelAgent (Phase 4)
- ✅ PortfolioManagerAgent (Phase 5)
- ✅ StrategyAgent (Phase 6)
- ✅ ExecutionAgent (Phase 7)
- ✅ RiskSentinelAgent (Phase 8)

### 架構改進:
- ❌ 移除 NATS 消息總線依賴
- ✅ 使用 Agno Framework 內建通訊
- ✅ 實現 7 個 PRD 定義的 Agents
- ✅ 完整 8-phase 啟動序列
"""

        return report

    def get_current_status(self) -> SystemStatus:
        """獲取當前系統狀態"""
        completed_count = len([p for p in self.phase_results if p.status == "completed"])
        progress = (completed_count / 9) * 100

        overall_status = "running"
        if completed_count == 9:
            overall_status = "completed"
        elif any(p.status == "failed" for p in self.phase_results):
            overall_status = "failed"

        active_agents = [
            "SupervisorAgent", "HealthGuardAgent", "MarketIntelAgent",
            "PortfolioManagerAgent", "StrategyAgent", "ExecutionAgent", "RiskSentinelAgent"
        ]

        return SystemStatus(
            current_phase=self.current_phase,
            total_phases=9,
            overall_status=overall_status,
            progress_percentage=progress,
            active_agents=active_agents,
            phase_results=self.phase_results
        )

    def send_agent_message(self, from_agent: str, to_agent: str, message_type: str, content: Dict[str, Any]):
        """Agent 間通訊 (替代 NATS 消息)"""
        message = AgentCommunication(
            from_agent=from_agent,
            to_agent=to_agent,
            message_type=message_type,
            content=content
        )

        self.agent_communications.append(message)

        # 記錄到 session state
        if "agent_communications" not in self.session_state:
            self.session_state["agent_communications"] = []
        self.session_state["agent_communications"].append(message.model_dump())

        logger.info("agent_message_sent",
                   from_agent=from_agent,
                   to_agent=to_agent,
                   message_type=message_type)

# ========== 使用示例 ==========

if __name__ == "__main__":
    # 創建完整的 DyFlow v3.3 workflow
    workflow = DyFlowCompleteWorkflow()

    print("🚀 DyFlow v3.3 完整 Agno 架構")
    print("✅ 7 個 Agents + 8-phase 啟動序列")
    print("❌ 移除 NATS 依賴")
    print("=" * 50)

    # 執行 workflow
    for response in workflow.run():
        print(response.content)
        print("-" * 50)
