#!/usr/bin/env python3
"""
DyFlow v3.3 完整系統測試
測試 7 個 Agents + 8-phase 啟動序列
驗證移除 NATS 依賴後的 Agno 架構
"""

import asyncio
import requests
import json
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent / "src"))

def print_header(title):
    """打印標題"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """打印章節"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

def test_complete_agno_agents():
    """測試完整的 7 個 Agno Agents"""
    print_section("DyFlow v3.3 完整 Agents 測試 (7 個)")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 根據 PRD v3.3 創建 7 個 DyFlow Agents
        agents = {}
        
        # 1. SupervisorAgent - Phase 0
        agents['supervisor'] = Agent(
            name="SupervisorAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow system supervisor for Phase 0.",
                "Read YAML/ENV configuration, manage Vault key distribution.",
                "Replace NATS initialization with Agno communication.",
                "End with 'Supervisor-Phase-0-Complete'."
            ]
        )
        
        # 2. HealthGuardAgent - Phase 1
        agents['health'] = Agent(
            name="HealthGuardAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow health monitor for Phase 1.",
                "Check RPC/Subgraph/DB/UI health ≥ 90%.",
                "Monitor BSC RPC, Solana RPC, PancakeSwap subgraph, Meteora API.",
                "End with 'Health-Phase-1-Complete'."
            ]
        )
        
        # 3. MarketIntelAgent - Phase 4
        agents['market'] = Agent(
            name="MarketIntelAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow market intelligence agent for Phase 4.",
                "Scan BSC and Solana pools for LP opportunities.",
                "Apply filters: TVL ≥ $10M, Created ≤ 2 days, Fee/TVL ≥ 5%.",
                "Replace NATS bus.pool with direct agent communication.",
                "End with 'Market-Phase-4-Complete'."
            ]
        )
        
        # 4. PortfolioManagerAgent - Phase 5
        agents['portfolio'] = Agent(
            name="PortfolioManagerAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow portfolio manager for Phase 5.",
                "Ensure NAV ≥ 0 and fund locks are writable.",
                "Manage portfolio allocation and rebalancing.",
                "End with 'Portfolio-Phase-5-Complete'."
            ]
        )
        
        # 5. StrategyAgent - Phase 6
        agents['strategy'] = Agent(
            name="StrategyAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow strategy generator for Phase 6.",
                "Generate LPPlan.approved with 4 strategy types:",
                "SPOT_BALANCED, CURVE_BALANCED, BID_ASK_BALANCED, SPOT_IMBALANCED_DAMM.",
                "Replace NATS bus.plan with direct agent communication.",
                "End with 'Strategy-Phase-6-Complete'."
            ]
        )
        
        # 6. ExecutionAgent - Phase 7
        agents['execution'] = Agent(
            name="ExecutionAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow execution agent for Phase 7.",
                "Execute LP transactions on BSC and Solana.",
                "Ensure ≥ 1 transaction successfully broadcast.",
                "Replace NATS bus.tx with direct agent communication.",
                "End with 'Execution-Phase-7-Complete'."
            ]
        )
        
        # 7. RiskSentinelAgent - Phase 8
        agents['risk'] = Agent(
            name="RiskSentinelAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow risk sentinel for Phase 8.",
                "Monitor IL_net < -8% and VaR_95 > 4% thresholds.",
                "Trigger emergency exits when limits exceeded.",
                "Replace NATS bus.risk with direct agent communication.",
                "End with 'Risk-Phase-8-Complete'."
            ]
        )
        
        print(f"✅ 創建了 {len(agents)} 個 DyFlow v3.3 Agents (符合 PRD)")
        
        # 測試每個 Agent
        agent_results = []
        
        for agent_name, agent in agents.items():
            print(f"🔄 測試 {agent_name} Agent...")
            
            try:
                prompt = f"Execute {agent_name} functionality for DyFlow v3.3. This replaces NATS message bus with Agno communication."
                response = agent.run(prompt)
                
                if response and response.content:
                    print(f"✅ {agent_name}: 響應成功")
                    print(f"   📝 {response.content[:80]}...")
                    agent_results.append((agent_name, True, response.content))
                else:
                    print(f"❌ {agent_name}: 無響應")
                    agent_results.append((agent_name, False, "No response"))
                    
            except Exception as e:
                print(f"❌ {agent_name}: 執行失敗 - {str(e)[:50]}...")
                agent_results.append((agent_name, False, str(e)))
        
        return agent_results
        
    except Exception as e:
        print(f"❌ 完整 Agents 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_complete_workflow():
    """測試完整的 8-phase workflow"""
    print_section("DyFlow v3.3 完整 Workflow 測試")
    
    try:
        from workflows.dyflow_agno_complete import DyFlowCompleteWorkflow
        
        # 創建完整 workflow
        workflow = DyFlowCompleteWorkflow(
            session_id=f"test-complete-{int(datetime.now().timestamp())}"
        )
        
        print("✅ 完整 Workflow 創建成功")
        print(f"📋 會話 ID: {workflow.session_id}")
        print(f"🤖 Agents: 7 個專門化 Agents")
        print(f"📊 階段: 8-phase 啟動序列")
        
        # 測試前 3 個階段
        print("\n🔄 測試前 3 個階段...")
        
        responses = []
        for response in workflow.run(start_phase=0):
            responses.append(response.content)
            print(f"📝 {response.content}")
            
            # 限制測試前 3 個階段
            if len(responses) >= 6:  # 每個階段 2 個響應 (開始+完成)
                break
        
        # 獲取狀態
        status = workflow.get_current_status()
        
        print(f"\n📊 Workflow 狀態:")
        print(f"   當前階段: Phase {status.current_phase}")
        print(f"   進度: {status.progress_percentage:.1f}%")
        print(f"   活躍 Agents: {len(status.active_agents)}")
        print(f"   完成階段: {len([p for p in status.phase_results if p.status == 'completed'])}")
        
        return len(responses) > 0
        
    except Exception as e:
        print(f"❌ 完整 Workflow 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nats_removal():
    """測試 NATS 依賴移除"""
    print_section("NATS 依賴移除驗證")
    
    # 檢查是否還有 NATS 相關代碼
    nats_references = []
    
    try:
        # 檢查主要文件中的 NATS 引用
        files_to_check = [
            "src/workflows/dyflow_agno_complete.py",
            "src/core/dyflow_v33_supervisor.py",
            "workflows/dyflow_v33_workflow.yaml"
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'nats' in content.lower() or 'bus.' in content:
                        nats_references.append(file_path)
        
        if nats_references:
            print(f"⚠️  發現 {len(nats_references)} 個文件仍有 NATS 引用:")
            for ref in nats_references:
                print(f"   - {ref}")
            print("建議: 完全移除這些 NATS 相關代碼")
        else:
            print("✅ 未發現 NATS 依賴，移除成功")
        
        # 檢查是否安裝了 nats-py
        try:
            import nats
            print("⚠️  nats-py 仍然安裝，可以考慮卸載: pip uninstall nats-py")
        except ImportError:
            print("✅ nats-py 未安裝，依賴清理完成")
        
        return len(nats_references) == 0
        
    except Exception as e:
        print(f"❌ NATS 移除檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print_header("🚀 DyFlow v3.3 完整系統測試")
    print(f"時間: {datetime.now()}")
    print(f"目標: 7 個 Agents + 8-phase + 移除 NATS")
    
    # 執行所有測試
    all_results = {}
    
    # 1. 完整 Agents 測試
    agent_results = test_complete_agno_agents()
    all_results['complete_agents'] = agent_results
    
    # 2. 完整 Workflow 測試
    workflow_success = test_complete_workflow()
    all_results['complete_workflow'] = workflow_success
    
    # 3. NATS 移除驗證
    nats_removed = test_nats_removal()
    all_results['nats_removal'] = nats_removed
    
    # 總結報告
    print_header("📊 完整測試結果總結")
    
    # Agents 測試結果
    if agent_results:
        agent_passed = sum(1 for _, success, _ in agent_results if success)
        agent_total = len(agent_results)
        status = "✅" if agent_passed == 7 else "⚠️" if agent_passed > 0 else "❌"
        print(f"{status} 完整 Agents (7個): {agent_passed}/{agent_total} 通過")
    
    # Workflow 測試結果
    status = "✅" if workflow_success else "❌"
    print(f"{status} 完整 Workflow: {'通過' if workflow_success else '失敗'}")
    
    # NATS 移除結果
    status = "✅" if nats_removed else "⚠️"
    print(f"{status} NATS 移除: {'完成' if nats_removed else '部分完成'}")
    
    # 總體評估
    total_score = 0
    if agent_results and len(agent_results) == 7:
        total_score += sum(1 for _, success, _ in agent_results if success) / 7 * 40
    if workflow_success:
        total_score += 30
    if nats_removed:
        total_score += 30
    
    print(f"\n📈 總體評分: {total_score:.1f}/100")
    
    if total_score >= 90:
        print("\n🎉 DyFlow v3.3 完整系統優秀！")
        print("✅ 7 個 Agents 按 PRD 實現")
        print("✅ 8-phase 啟動序列完整")
        print("✅ Agno 架構替代 NATS 成功")
    elif total_score >= 70:
        print("\n👍 DyFlow v3.3 系統良好")
        print("⚠️  部分組件需要完善")
    else:
        print("\n⚠️  DyFlow v3.3 系統需要改進")
        print("❌ 多個組件存在問題")
    
    print("\n💡 下一步建議:")
    print("1. 完善所有 7 個 Agents 的實現")
    print("2. 完全移除 NATS 相關代碼")
    print("3. 連接 React UI 到真實 Agno Workflow API")
    print("4. 實現完整的 8-phase 啟動序列")
    print("5. 添加真實的池子掃描和交易執行")
    
    return total_score >= 70

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🚀 DyFlow v3.3 完整系統測試通過！")
        else:
            print("\n❌ DyFlow v3.3 完整系統測試未通過")
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
