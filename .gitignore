# Python
__pycache__/
*.pyc
*.pyo
*.pyd

# Virtual environments
.venv/
env/
venv/

# macOS
.DS_Store

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Data & cache
data/cache/
data/logs/
data/state/
*.db

# Results & temp files
*.json

# VSCode
.vscode/

# System
Thumbs.db

# Ignore test outputs
*.out
*.log

# 清理备份
backup_before_cleanup/

# 临时分析文件
*_report_*.txt
*_portfolio_*.json
enhanced_wallet_portfolio.json
lp_positions_analysis.json
wallet_portfolio_data.json.roo/
.roomodes
.clinerules/
.roo/

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build output
react-ui/dist/
react-ui/build/
react-ui/.vite/
