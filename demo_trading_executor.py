#!/usr/bin/env python3
"""
TradingExecutorAgent 完整功能演示
展示所有支持的交易操作和策略部署功能
"""

import asyncio
import sys
import os
import json

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.agents.trading_executor_agent import TradingExecutorAgent, TradingRequest, TradingAction
from src.utils.strategy_types import DLMMStrategyType, StrategyFactory

class TradingExecutorDemo:
    """TradingExecutorAgent 完整功能演示"""
    
    def __init__(self):
        self.agent = None
        
        # 示例數據
        self.sol_mint = "So********************************111111112"
        self.usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        self.example_pool = "8sLbNZoA1cfnvMJLPfp98ZLAnFSYCFApfJKMbiXNLwxj"
        self.example_wallet = "********************************"
    
    async def initialize_demo(self):
        """初始化演示環境"""
        print("🚀 初始化 TradingExecutorAgent 演示環境...")
        
        # 使用 Mock 模式確保演示可以運行
        config = {
            "name": "DemoTradingExecutor",
            "model_provider": "mock",
            "model_name": "demo-model",
            "enable_reasoning": False,
            "enable_memory": False
        }
        
        self.agent = TradingExecutorAgent(config)
        success = await self.agent.initialize()
        
        if success:
            print("✅ TradingExecutorAgent 初始化成功")
            return True
        else:
            print("❌ TradingExecutorAgent 初始化失敗")
            return False
    
    async def demo_strategy_overview(self):
        """演示策略概覽"""
        print("\n" + "="*60)
        print("📋 支持的 DLMM LP 策略概覽")
        print("="*60)
        
        strategies = await self.agent.get_supported_strategies()
        
        for i, strategy in enumerate(strategies, 1):
            print(f"\n{i}. {strategy['name']} ({strategy['type']})")
            print(f"   描述: {strategy['description']}")
            print(f"   風險等級: {strategy['risk_level']}")
            print(f"   預期 APR: {strategy['expected_apr']}%")
            print(f"   最大無常損失: {strategy['max_impermanent_loss']}%")
            print(f"   重平衡頻率: {strategy['rebalance_frequency']}")
            print(f"   最小資金: ${strategy['min_capital']}")
    
    async def demo_swap_operations(self):
        """演示 Swap 操作"""
        print("\n" + "="*60)
        print("🔄 Swap 操作演示")
        print("="*60)
        
        # 1. 簡單交換
        print("\n1. 簡單 SOL -> USDC 交換")
        swap_request = TradingRequest(
            action=TradingAction.SWAP,
            parameters={
                "input_mint": self.sol_mint,
                "output_mint": self.usdc_mint,
                "amount": "1000000000",  # 1 SOL
                "user_public_key": self.example_wallet,
                "slippage_bps": 50
            },
            priority="NORMAL"
        )
        
        result = await self.agent.execute_trading_request(swap_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
        
        # 2. DCA 交換
        print("\n2. DCA 交換 (5 SOL 分 3 次)")
        dca_request = TradingRequest(
            action=TradingAction.DCA_SWAP,
            parameters={
                "input_mint": self.sol_mint,
                "output_mint": self.usdc_mint,
                "total_amount": 5000000000,  # 5 SOL
                "intervals": 3,
                "interval_seconds": 10,
                "user_public_key": self.example_wallet,
                "slippage_bps": 50
            },
            priority="NORMAL"
        )
        
        result = await self.agent.execute_trading_request(dca_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
        
        # 3. 批量交換
        print("\n3. 批量交換")
        batch_request = TradingRequest(
            action=TradingAction.BATCH_SWAP,
            parameters={
                "swap_requests": [
                    {
                        "input_mint": self.sol_mint,
                        "output_mint": self.usdc_mint,
                        "amount": "500000000",  # 0.5 SOL
                        "user_public_key": self.example_wallet
                    },
                    {
                        "input_mint": self.usdc_mint,
                        "output_mint": self.sol_mint,
                        "amount": "100000000",  # 100 USDC
                        "user_public_key": self.example_wallet
                    }
                ]
            },
            priority="HIGH"
        )
        
        result = await self.agent.execute_trading_request(batch_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
    
    async def demo_lp_strategies(self):
        """演示 LP 策略部署"""
        print("\n" + "="*60)
        print("🎯 LP 策略部署演示")
        print("="*60)
        
        strategies_to_demo = [
            {
                "name": "對稱流動性策略",
                "type": "spot_balanced",
                "config": {"range_width": 20, "distribution_type": "uniform"}
            },
            {
                "name": "曲線分布策略", 
                "type": "curve_balanced",
                "config": {"curve_steepness": 2.5, "concentration_factor": 0.8}
            },
            {
                "name": "買賣價差策略",
                "type": "bid_ask_balanced", 
                "config": {"bid_weight": 0.6, "ask_weight": 0.4}
            },
            {
                "name": "單邊流動性策略",
                "type": "spot_imbalanced",
                "config": {"direction": "long", "concentration_bins": 5}
            }
        ]
        
        for i, strategy_demo in enumerate(strategies_to_demo, 1):
            print(f"\n{i}. {strategy_demo['name']}")
            
            lp_request = TradingRequest(
                action=TradingAction.DEPLOY_LP,
                parameters={
                    "pool_address": self.example_pool,
                    "strategy_type": strategy_demo["type"],
                    "token_amount": 1000.0,
                    "token_mint": self.usdc_mint,
                    "strategy_config": strategy_demo["config"]
                },
                priority="HIGH"
            )
            
            result = await self.agent.execute_trading_request(lp_request)
            print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
            if result.error:
                print(f"   錯誤: {result.error}")
            print(f"   執行時間: {result.execution_time:.3f}s")
    
    async def demo_lp_management(self):
        """演示 LP 管理操作"""
        print("\n" + "="*60)
        print("🔧 LP 管理操作演示")
        print("="*60)
        
        # 1. 收割手續費
        print("\n1. 收割手續費")
        harvest_request = TradingRequest(
            action=TradingAction.HARVEST_FEES,
            parameters={
                "pool_address": self.example_pool,
                "wallet_address": self.example_wallet
            },
            priority="NORMAL"
        )
        
        result = await self.agent.execute_trading_request(harvest_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
        
        # 2. 部分退出 (50%)
        print("\n2. 部分退出持倉 (50%)")
        exit_request = TradingRequest(
            action=TradingAction.EXIT_POSITION,
            parameters={
                "pool_address": self.example_pool,
                "wallet_address": self.example_wallet,
                "percentage": 0.5
            },
            priority="HIGH"
        )
        
        result = await self.agent.execute_trading_request(exit_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
        
        # 3. 完全退出 (100%)
        print("\n3. 完全退出持倉 (100%)")
        exit_all_request = TradingRequest(
            action=TradingAction.EXIT_POSITION,
            parameters={
                "pool_address": self.example_pool,
                "wallet_address": self.example_wallet,
                "percentage": 1.0
            },
            priority="URGENT"
        )
        
        result = await self.agent.execute_trading_request(exit_all_request)
        print(f"   結果: {'✅ 成功' if result.success else '❌ 失敗'}")
        if result.error:
            print(f"   錯誤: {result.error}")
        print(f"   執行時間: {result.execution_time:.3f}s")
    
    async def demo_complete_cycles(self):
        """演示完整循環操作"""
        print("\n" + "="*60)
        print("🔄 完整循環操作演示")
        print("="*60)
        
        # 1. SOL -> DLMM LP 循環
        print("\n1. SOL -> DLMM LP 完整循環")
        print("   (2 SOL -> 交換為 USDC -> 部署曲線分布策略)")
        
        result = await self.agent.sol_to_dlmm_lp_cycle(
            sol_amount=2.0,
            pool_address=self.example_pool,
            strategy_type="curve_balanced",
            range_width=30,
            curve_steepness=2.5,
            concentration_factor=0.8
        )
        
        print(f"   結果: {'✅ 成功' if result.get('success') else '❌ 失敗'}")
        if not result.get('success'):
            print(f"   錯誤: {result.get('error')}")
        
        # 2. DLMM LP -> SOL 循環
        print("\n2. DLMM LP -> SOL 完整循環")
        print("   (收割費用 -> 退出持倉 -> 轉換回 SOL)")
        
        result = await self.agent.dlmm_lp_to_sol_cycle(
            pool_address=self.example_pool,
            wallet_address=self.example_wallet,
            exit_percentage=1.0
        )
        
        print(f"   結果: {'✅ 成功' if result.get('success') else '❌ 失敗'}")
        if not result.get('success'):
            print(f"   錯誤: {result.get('error')}")
    
    async def demo_execution_stats(self):
        """演示執行統計"""
        print("\n" + "="*60)
        print("📊 執行統計")
        print("="*60)
        
        stats = self.agent.get_execution_stats()
        
        print(f"總交易數: {stats['total_trades']}")
        print(f"成功交易: {stats['successful_trades']}")
        print(f"失敗交易: {stats['failed_trades']}")
        print(f"成功率: {(stats['successful_trades'] / max(stats['total_trades'], 1) * 100):.1f}%")
        print(f"總交易量: ${stats['total_volume']:.2f}")
        print(f"策略部署數: {stats['strategies_deployed']}")
        print(f"收割費用: ${stats['fees_harvested']:.2f}")
    
    async def run_complete_demo(self):
        """運行完整演示"""
        print("🎭 TradingExecutorAgent 完整功能演示")
        print("="*80)
        
        # 初始化
        if not await self.initialize_demo():
            print("❌ 演示初始化失敗")
            return
        
        try:
            # 運行各個演示模塊
            await self.demo_strategy_overview()
            await self.demo_swap_operations()
            await self.demo_lp_strategies()
            await self.demo_lp_management()
            await self.demo_complete_cycles()
            await self.demo_execution_stats()
            
            print("\n" + "="*80)
            print("🎉 演示完成！")
            print("\n主要功能:")
            print("✅ 四種 DLMM LP 策略部署")
            print("✅ 完整的 Swap 功能 (簡單、DCA、批量)")
            print("✅ LP 生命周期管理 (開倉、收割、平倉)")
            print("✅ SOL ↔ DLMM LP 完整循環")
            print("✅ 詳細的執行統計和錯誤處理")
            
            print("\n注意:")
            print("⚠️ 這是演示模式，使用模擬數據")
            print("⚠️ 實際使用需要配置真實的錢包和 RPC 端點")
            print("⚠️ 建議先在測試網測試再部署到主網")
            
        finally:
            # 清理
            await self.agent.cleanup()

async def main():
    """主函數"""
    demo = TradingExecutorDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
