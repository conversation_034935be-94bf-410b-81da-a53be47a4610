"""
TradingExecutorAgent 使用示例
展示如何使用 TradingExecutorAgent 進行完整的交易操作
"""

import asyncio
import json
from typing import Dict, Any
import structlog

# DyFlow imports
from src.agents.trading_executor_agent import TradingExecutorAgent, TradingRequest, TradingAction
from src.utils.strategy_types import DLMMStrategyType

# 設置日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

class TradingExecutorExample:
    """TradingExecutorAgent 使用示例"""
    
    def __init__(self):
        self.agent: TradingExecutorAgent = None
        
        # 示例配置
        self.example_pool_address = "8sLbNZoA1cfnvMJLPfp98ZLAnFSYCFApfJKMbiXNLwxj"  # 示例 DLMM 池地址
        self.example_wallet = "********************************"  # 示例錢包地址
        self.sol_mint = "So********************************111111112"
        self.usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    
    async def initialize_agent(self):
        """初始化 TradingExecutorAgent"""
        try:
            logger.info("initializing_trading_executor_agent")
            
            # 創建 Agent 配置
            config = {
                "name": "TradingExecutorExample",
                "model_provider": "ollama",
                "model_name": "qwen2.5:3b",
                "enable_reasoning": True,
                "enable_memory": False,  # 示例中關閉記憶功能
                "max_retries": 3
            }
            
            # 創建並初始化 Agent
            self.agent = TradingExecutorAgent(config)
            success = await self.agent.initialize()
            
            if success:
                logger.info("trading_executor_agent_initialized_successfully")
                
                # 設置錢包 (僅用於示例，實際使用時需要安全管理私鑰)
                # self.agent.set_wallet("your_private_key_here")
                
                return True
            else:
                logger.error("trading_executor_agent_initialization_failed")
                return False
                
        except Exception as e:
            logger.error("initialize_agent_failed", error=str(e))
            return False
    
    async def example_simple_swap(self):
        """示例：簡單代幣交換"""
        try:
            logger.info("=== 示例：簡單代幣交換 ===")
            
            # 創建交換請求
            swap_request = TradingRequest(
                action=TradingAction.SWAP,
                parameters={
                    "input_mint": self.sol_mint,
                    "output_mint": self.usdc_mint,
                    "amount": "1000000000",  # 1 SOL (in lamports)
                    "slippage_bps": 50,  # 0.5%
                    "user_public_key": self.example_wallet,
                    "priority_fee": 5000
                },
                priority="NORMAL",
                user_id="example_user"
            )
            
            # 執行交換
            result = await self.agent.execute_trading_request(swap_request)
            
            logger.info("swap_result", 
                       success=result.success,
                       data=result.data,
                       error=result.error,
                       execution_time=result.execution_time)
            
            return result
            
        except Exception as e:
            logger.error("example_simple_swap_failed", error=str(e))
            return None
    
    async def example_deploy_lp_strategy(self):
        """示例：部署 LP 策略"""
        try:
            logger.info("=== 示例：部署 LP 策略 ===")
            
            # 創建 LP 策略部署請求
            lp_request = TradingRequest(
                action=TradingAction.DEPLOY_LP,
                parameters={
                    "pool_address": self.example_pool_address,
                    "strategy_type": "spot_balanced",
                    "token_amount": 1000.0,  # 1000 USDC
                    "token_mint": self.usdc_mint,
                    "strategy_config": {
                        "range_width": 20,
                        "distribution_type": "uniform"
                    }
                },
                priority="HIGH",
                user_id="example_user"
            )
            
            # 執行策略部署
            result = await self.agent.execute_trading_request(lp_request)
            
            logger.info("lp_deployment_result",
                       success=result.success,
                       data=result.data,
                       error=result.error,
                       execution_time=result.execution_time)
            
            return result
            
        except Exception as e:
            logger.error("example_deploy_lp_strategy_failed", error=str(e))
            return None
    
    async def example_dca_swap(self):
        """示例：DCA 交換"""
        try:
            logger.info("=== 示例：DCA 交換 ===")
            
            # 創建 DCA 交換請求
            dca_request = TradingRequest(
                action=TradingAction.DCA_SWAP,
                parameters={
                    "input_mint": self.sol_mint,
                    "output_mint": self.usdc_mint,
                    "total_amount": 5000000000,  # 5 SOL
                    "intervals": 5,  # 分 5 次執行
                    "interval_seconds": 30,  # 每次間隔 30 秒
                    "slippage_bps": 50,
                    "user_public_key": self.example_wallet,
                    "priority_fee": 5000
                },
                priority="NORMAL",
                user_id="example_user"
            )
            
            # 執行 DCA 交換
            result = await self.agent.execute_trading_request(dca_request)
            
            logger.info("dca_swap_result",
                       success=result.success,
                       data=result.data,
                       error=result.error,
                       execution_time=result.execution_time)
            
            return result
            
        except Exception as e:
            logger.error("example_dca_swap_failed", error=str(e))
            return None
    
    async def example_sol_to_dlmm_cycle(self):
        """示例：SOL -> DLMM LP 完整循環"""
        try:
            logger.info("=== 示例：SOL -> DLMM LP 完整循環 ===")
            
            # 執行完整循環
            result = await self.agent.sol_to_dlmm_lp_cycle(
                sol_amount=2.0,  # 2 SOL
                pool_address=self.example_pool_address,
                strategy_type="curve_balanced",
                range_width=30,
                curve_steepness=2.5,
                concentration_factor=0.8
            )
            
            logger.info("sol_to_dlmm_cycle_result",
                       success=result.get("success"),
                       data=result)
            
            return result
            
        except Exception as e:
            logger.error("example_sol_to_dlmm_cycle_failed", error=str(e))
            return None
    
    async def example_harvest_and_exit(self):
        """示例：收割手續費和退出持倉"""
        try:
            logger.info("=== 示例：收割手續費和退出持倉 ===")
            
            # 1. 收割手續費
            harvest_request = TradingRequest(
                action=TradingAction.HARVEST_FEES,
                parameters={
                    "pool_address": self.example_pool_address,
                    "wallet_address": self.example_wallet
                },
                priority="NORMAL",
                user_id="example_user"
            )
            
            harvest_result = await self.agent.execute_trading_request(harvest_request)
            logger.info("harvest_result", result=harvest_result.data)
            
            # 2. 部分退出持倉 (50%)
            exit_request = TradingRequest(
                action=TradingAction.EXIT_POSITION,
                parameters={
                    "pool_address": self.example_pool_address,
                    "wallet_address": self.example_wallet,
                    "percentage": 0.5  # 退出 50%
                },
                priority="HIGH",
                user_id="example_user"
            )
            
            exit_result = await self.agent.execute_trading_request(exit_request)
            logger.info("exit_result", result=exit_result.data)
            
            return {
                "harvest": harvest_result,
                "exit": exit_result
            }
            
        except Exception as e:
            logger.error("example_harvest_and_exit_failed", error=str(e))
            return None
    
    async def example_get_supported_strategies(self):
        """示例：獲取支持的策略"""
        try:
            logger.info("=== 示例：獲取支持的策略 ===")
            
            strategies = await self.agent.get_supported_strategies()
            
            logger.info("supported_strategies", count=len(strategies))
            for strategy in strategies:
                logger.info("strategy_info",
                           type=strategy["type"],
                           name=strategy["name"],
                           risk_level=strategy["risk_level"],
                           expected_apr=strategy["expected_apr"])
            
            return strategies
            
        except Exception as e:
            logger.error("example_get_supported_strategies_failed", error=str(e))
            return None
    
    async def run_all_examples(self):
        """運行所有示例"""
        try:
            logger.info("=== 開始運行 TradingExecutorAgent 示例 ===")
            
            # 初始化 Agent
            if not await self.initialize_agent():
                logger.error("Agent 初始化失敗，退出示例")
                return
            
            # 運行各種示例
            examples = [
                ("獲取支持的策略", self.example_get_supported_strategies),
                ("簡單代幣交換", self.example_simple_swap),
                ("部署 LP 策略", self.example_deploy_lp_strategy),
                ("DCA 交換", self.example_dca_swap),
                ("SOL -> DLMM LP 循環", self.example_sol_to_dlmm_cycle),
                ("收割和退出", self.example_harvest_and_exit),
            ]
            
            results = {}
            
            for name, example_func in examples:
                try:
                    logger.info(f"運行示例: {name}")
                    result = await example_func()
                    results[name] = result
                    
                    # 等待一段時間再執行下一個示例
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.error(f"示例 {name} 執行失敗", error=str(e))
                    results[name] = {"error": str(e)}
            
            # 顯示執行統計
            stats = self.agent.get_execution_stats()
            logger.info("execution_statistics", stats=stats)
            
            # 清理資源
            await self.agent.cleanup()
            
            logger.info("=== 所有示例執行完成 ===")
            return results
            
        except Exception as e:
            logger.error("run_all_examples_failed", error=str(e))
            return None

async def main():
    """主函數"""
    example = TradingExecutorExample()
    results = await example.run_all_examples()
    
    if results:
        print("\n=== 示例執行結果摘要 ===")
        for name, result in results.items():
            if isinstance(result, dict) and "error" in result:
                print(f"❌ {name}: {result['error']}")
            elif result:
                print(f"✅ {name}: 執行成功")
            else:
                print(f"⚠️ {name}: 無結果")

if __name__ == "__main__":
    asyncio.run(main())
