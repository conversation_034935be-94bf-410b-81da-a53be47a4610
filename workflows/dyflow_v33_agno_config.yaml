# DyFlow v3.3 Agno Framework Configuration
# 替代 NATS 消息總線，使用 Agno Framework 內建通訊

# 全局配置
globals:
  trading_mode: active  # active | exit_only | paused
  architecture: agno_framework  # 使用 Agno Framework 替代 NATS
  portfolio:
    lock_scope: pool
    max_positions: 10
    position_size_usd: 5000
  guard_policies:
    default_timeout: 4
    default_retries: 2
    max_execution_time: 300
  risk_limits:
    il_net_threshold: -8.0  # -8%
    var_95_threshold: 4.0   # 4%
    volatility_threshold: 5.0  # 5%
    health_score_threshold: 0.8  # 80%

# Agno Framework 配置
agno_config:
  model:
    provider: ollama
    model_id: qwen2.5:3b
    host: http://localhost:11434
  
  storage:
    type: sqlite
    db_file: data/agno_workflows.db
    table_name: dyflow_workflows
  
  team_mode: coordinate  # 協調模式
  show_members_responses: true
  debug_mode: true

# DyFlow v3.3 Agents (7 個，根據 PRD)
agents:
  # SupervisorAgent - Phase 0
  - id: supervisor
    name: SupervisorAgent
    phase: 0
    role: "System initialization and lifecycle management"
    priority: 10
    instructions:
      - "You are the DyFlow system supervisor responsible for Phase 0 initialization."
      - "Read configuration files, manage system startup sequence."
      - "Coordinate the 8-phase startup sequence using Agno Framework."
      - "Replace NATS message bus with Agno agent communication."
      - "End responses with 'Supervisor-Phase-0-Complete'."
    config:
      startup_phases: 8
      rollback_on_failure: true
    
  # HealthGuardAgent - Phase 1
  - id: health
    name: HealthGuardAgent
    phase: 1
    role: "System health monitoring and validation"
    priority: 9
    instructions:
      - "You are responsible for Phase 1 health checks."
      - "Monitor RPC/Subgraph/DB/UI health, ensure ≥ 90% health threshold."
      - "Check BSC RPC, Solana RPC, PancakeSwap subgraph, Meteora API, database."
      - "Use Agno communication instead of NATS bus.health."
      - "End responses with 'Health-Phase-1-Complete'."
    config:
      check_interval: 60
      components:
        - bsc_rpc
        - solana_rpc
        - pancake_subgraph
        - meteora_api
        - database
    
  # MarketIntelAgent - Phase 4
  - id: intel
    name: MarketIntelAgent
    phase: 4
    role: "Market data collection and pool scanning"
    priority: 6
    instructions:
      - "You are responsible for Phase 4 market intelligence."
      - "Scan BSC (PancakeSwap v3) and Solana (Meteora DLMM v2) pools."
      - "Apply filters: TVL ≥ $10M, Created ≤ 2 days, Fee/TVL ≥ 5%, 24h Fees > $5."
      - "Use Agno communication instead of NATS bus.pool."
      - "End responses with 'Market-Phase-4-Complete'."
    tools:
      - damm_scanner
      - chain_scanner
    config:
      scan_interval: 30
      max_pools: 100
      filters:
        min_tvl: 10000000
        max_created_days: 2
        min_fee_tvl_ratio: 0.05
        min_fees_24h: 5
    
  # PortfolioManagerAgent - Phase 5
  - id: portfolio
    name: PortfolioManagerAgent
    phase: 5
    role: "Portfolio management and NAV calculation"
    priority: 7
    instructions:
      - "You are responsible for Phase 5 portfolio management."
      - "Ensure NAV ≥ 0 and fund locks are writable."
      - "Manage portfolio allocation and rebalancing."
      - "Use Agno communication instead of NATS bus.plan.approved."
      - "End responses with 'Portfolio-Phase-5-Complete'."
    config:
      nav_update_interval: 300
      rebalance_threshold: 0.02
      max_allocation_per_pool: 0.15
    
  # StrategyAgent - Phase 6
  - id: strategy
    name: StrategyAgent
    phase: 6
    role: "LP strategy generation and optimization"
    priority: 8
    instructions:
      - "You are responsible for Phase 6 strategy generation."
      - "Generate LPPlan.approved with 4 strategy types:"
      - "- SPOT_BALANCED (symmetric liquidity)"
      - "- CURVE_BALANCED (curve distribution)"
      - "- BID_ASK_BALANCED (bid-ask spread)"
      - "- SPOT_IMBALANCED_DAMM (single-sided liquidity)"
      - "Use Agno communication instead of NATS bus.plan."
      - "End responses with 'Strategy-Phase-6-Complete'."
    tools:
      - vol_oracle
      - tx_sim
    guards:
      expects:
        sigma_p90:
          lt: 0.60
        il_sim:
          lt: 0.05
      timeout: 120
      retries: 2
    config:
      strategy_types:
        - SPOT_BALANCED
        - CURVE_BALANCED
        - BID_ASK_BALANCED
        - SPOT_IMBALANCED_DAMM
      risk_assessment: true
    
  # ExecutionAgent - Phase 7
  - id: exec
    name: ExecutionAgent
    phase: 7
    role: "Transaction execution and monitoring"
    priority: 9
    instructions:
      - "You are responsible for Phase 7 transaction execution."
      - "Execute LP transactions on BSC and Solana."
      - "Ensure ≥ 1 transaction successfully broadcast."
      - "Use Agno communication instead of NATS bus.tx."
      - "End responses with 'Execution-Phase-7-Complete'."
    tools:
      - wallet_signer
      - dex_router
    config:
      max_concurrent_tx: 3
      tx_timeout: 180
      gas_price_strategy: "medium"
      slippage_tolerance: 0.005
    
  # RiskSentinelAgent - Phase 8
  - id: risk
    name: RiskSentinelAgent
    phase: 8
    role: "Risk monitoring and portfolio protection"
    priority: 10
    instructions:
      - "You are responsible for Phase 8 risk monitoring."
      - "Monitor IL_net (Impermanent Loss) and VaR (Value at Risk) metrics."
      - "Ensure IL_net < -8% and VaR_95 > 4% thresholds."
      - "Trigger emergency exits when risk limits exceeded."
      - "Use Agno communication instead of NATS bus.risk."
      - "End responses with 'Risk-Phase-8-Complete'."
    config:
      monitoring_interval: 60
      il_check_interval: 300
      var_calculation_method: "historical"
      emergency_exit_enabled: true

# Tool 配置 (保持不變，但移除 NATS 依賴)
tools:
  # Meteora DLMM/DAMM v2 池掃描
  - id: damm_scanner
    class: DammScannerTool
    config:
      meteora_api_url: "https://dlmm-api.meteora.ag"
      timeout: 30
      max_pools: 100
      filters:
        min_tvl: 10000000
        max_created_days: 2
        min_fee_tvl_ratio: 0.05
        min_fees_24h: 5
  
  # BSC 和 Solana 鏈掃描
  - id: chain_scanner
    class: ChainScannerTool
    config:
      bsc_rpc_url: "https://bsc-dataseed.binance.org/"
      solana_rpc_url: "https://api.mainnet-beta.solana.com"
      pancake_subgraph_url: "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
      timeout: 30
  
  # 波動率預言機
  - id: vol_oracle
    class: VolOracleTool
    config:
      update_interval: 60
      lookback_periods:
        - 1h
        - 4h
        - 24h
      volatility_models:
        - parkinson
        - atr
        - realized
  
  # 錢包簽名工具
  - id: wallet_signer
    class: WalletSignerTool
    config:
      mpc_threshold: "2/3"
      signature_timeout: 30
      backup_wallets: true
      transaction_limits:
        daily_limit_usd: 100000
        single_tx_limit_usd: 10000
  
  # DEX 路由工具
  - id: dex_router
    class: DexRouterTool
    config:
      jupiter_api_url: "https://quote-api.jup.ag/v6"
      pancake_router_address: "******************************************"
      slippage_tolerance: 0.005
      max_hops: 3
  
  # 交易模擬工具
  - id: tx_sim
    class: TxSimTool
    config:
      tenderly_api_key: "${TENDERLY_API_KEY}"
      solsim_api_key: "${SOLSIM_API_KEY}"
      simulation_timeout: 15
  
  # 連接探測工具
  - id: conn_probe
    class: ConnProbeTool
    config:
      probe_interval: 30
      timeout: 10
      endpoints:
        - bsc_rpc
        - solana_rpc
        - pancake_subgraph
        - meteora_api
        - database
  
  # 手續費收割工具
  - id: fee_collector
    class: FeeCollectorTool
    schedule: "0 2 * * *"  # 每日 UTC 02:00
    config:
      collection_timeout: 300
      min_fee_threshold: 1.0
      auto_compound: true

# 策略映射矩陣 (v3.3) - 保持不變
strategy_mapping:
  SPOT_BALANCED:
    conditions:
      volatility_max: 0.01  # σ ≤ 1%
      fee_tvl_ratio_max: 0.002  # Fee/TVL < 0.2%
    k_calculation: "1 * ATR"
    description: "雙穩、主流代幣對"
    risk_level: "low"
    
  CURVE_BALANCED:
    conditions:
      volatility_min: 0.01  # 1-3%
      volatility_max: 0.03
      fee_activation: false
    k_calculation: "1.5 * ATR"
    description: "隨趨勢滑動"
    risk_level: "medium"
    
  BID_ASK_BALANCED:
    conditions:
      spread_max: 0.0005  # Spread < 5 bps
      depth_volume_ratio: "high"  # Depth ≫ Vol
    k_calculation: "2 * ATR"
    description: "吃點差策略"
    risk_level: "medium"
    
  SPOT_IMBALANCED_DAMM:
    conditions:
      volatility_min: 0.03  # σ > 3%
      fee_tvl_ratio_min: 0.004  # Fee/TVL ≥ 0.4%
      fdv_max: 1000000  # FDV < 1M
    k_calculation: "max(2 * ATR, 1.2 * σ_p90)"
    description: "單邊流動性，費率可抵 IL"
    risk_level: "high"

# 風控配置 - 保持不變
risk_management:
  il_net_formula: "IL_raw - FeeAccrued"
  thresholds:
    il_net_exit: -8.0  # IL_net < -8% 強平
    var_95_warning: 4.0  # VaR_95 > 4% 降倉 25%
    volatility_exit: 5.0  # σ_1m > 5% 持續 3 分鐘切至 exit_only
    health_score_pause: 0.8  # Health score < 0.8 全局 paused
  
  monitoring:
    il_check_interval: 300  # 5 分鐘檢查一次
    var_calculation_window: 24h
    volatility_window: 1m
    health_check_interval: 60

# 部署配置 (移除 NATS)
deployment:
  docker_compose: true
  architecture: agno_framework  # 使用 Agno Framework
  services:
    - supervisor_agent
    - health_guard_agent
    - market_intel_agent
    - portfolio_manager_agent
    - strategy_agent
    - execution_agent
    - risk_sentinel_agent
    - agno_workflow_api  # 新的 Agno API 服務
  
  monitoring:
    prometheus_port: 9090
    grafana_port: 3001
    agno_api_port: 8001  # 新的 Agno API 端口
    metrics_path: "/:chain/:pool_id/metrics"
  
  secrets:
    vault_enabled: true
    dynamic_lease: true
    auto_revoke: true
