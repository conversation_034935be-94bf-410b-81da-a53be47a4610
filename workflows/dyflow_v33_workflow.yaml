# DyFlow v3.3 Agno Workflow Configuration
# 八階段啟動護欄 + 事件驅動架構

name: "DyFlow v3.3 LP Strategy Workflow"
version: "3.3.0"
description: "24/7 自動化多 Agent LP 策略系統"

# 全局配置
config:
  max_concurrent_agents: 7
  timeout_seconds: 300
  retry_attempts: 3
  enable_logging: true
  log_level: "INFO"
  
  # 事件總線配置
  event_bus:
    type: "memory"  # 或 "redis", "nats"
    channels:
      - "bus.pool"
      - "bus.lpplan" 
      - "bus.tx"
      - "ExitRequest"
      - "globalsDiff"
      - "task_state_changed"
      - "lp_update"
      - "risk_update"
      - "health_update"

# 八階段啟動序列
phases:
  - phase: 0
    name: "Supervisor"
    agent: "SupervisorAgent"
    description: "讀取 YAML 配置和 Vault 設定"
    success_criteria:
      - "config_loaded"
      - "vault_accessible"
    enable_next: [1]
    
  - phase: 1
    name: "HealthGuard"
    agent: "HealthGuardAgent"
    description: "檢查 RPC、Subgraph、DB 連接"
    success_criteria:
      - "rpc_health >= 0.9"
      - "subgraph_health >= 0.9"
      - "db_health >= 0.9"
    enable_next: [2]
    
  - phase: 2
    name: "WebUI + Prometheus"
    agent: "WebUIAgent"
    description: "啟動 Web 界面和監控"
    success_criteria:
      - "webui_health_200"
      - "prometheus_metrics_available"
    enable_next: [3]
    
  - phase: 3
    name: "WalletProbe"
    agent: "WalletProbeAgent"
    description: "檢查錢包簽名能力"
    success_criteria:
      - "wallet_count >= 1"
      - "signing_capability_verified"
    enable_next: [4]
    
  - phase: 4
    name: "MarketIntel"
    agent: "MarketIntelAgent"
    description: "開始池子掃描和事件推送"
    success_criteria:
      - "bus_pool_events_flowing"
      - "pool_scan_active"
    enable_next: [5]
    
  - phase: 5
    name: "PortfolioManager"
    agent: "PortfolioManagerAgent"
    description: "NAV 計算和資金鎖定"
    success_criteria:
      - "nav_calculated"
      - "fund_allocation_ready"
    enable_next: [6]
    
  - phase: 6
    name: "Strategy"
    agent: "StrategyAgent"
    description: "生成 LPPlan 並標記為 approved"
    success_criteria:
      - "lpplan_approved_generated"
      - "risk_profile_attached"
    enable_next: [7]
    
  - phase: 7
    name: "Execution"
    agent: "ExecutionAgent"
    description: "執行交易並推送 bus.tx 事件"
    success_criteria:
      - "tx_success_count >= 1"
      - "bus_tx_events_flowing"
    enable_next: [8]
    
  - phase: 8
    name: "RiskSentinel + FeeCollector"
    agent: "RiskSentinelAgent"
    description: "風險監控和費用收集"
    success_criteria:
      - "il_monitoring_active"
      - "var_monitoring_active"
      - "fee_collection_scheduled"
    enable_next: ["business_loop"]

# Agent 配置
agents:
  SupervisorAgent:
    class: "src.agents.supervisor_agent.SupervisorAgent"
    config:
      vault_path: "config/vault.yaml"
      workflow_config: "workflows/dyflow_v33_workflow.yaml"
      
  HealthGuardAgent:
    class: "src.agents.health_guard_agent.HealthGuardAgent"
    config:
      check_interval: 30
      health_threshold: 0.9
      endpoints:
        bsc_rpc: "https://bsc-dataseed1.binance.org/"
        solana_rpc: "https://api.mainnet-beta.solana.com"
        pancake_subgraph: "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
        meteora_api: "https://dammv2-api.meteora.ag"
        supabase_db: "postgresql://..."
        
  MarketIntelAgent:
    class: "src.agents.market_intel_agent.MarketIntelAgent"
    config:
      scan_interval: 15
      pool_filters:
        min_tvl: 10000000  # $10M
        max_age_days: 2
        min_fee_tvl_ratio: 0.05  # 5%
        min_24h_fees: 5000  # $5K
      chains: ["bsc", "solana"]
      
  PortfolioManagerAgent:
    class: "src.agents.portfolio_agent.PortfolioManagerAgent"
    config:
      max_positions: 10
      max_allocation_per_position: 0.2  # 20%
      rebalance_threshold: 0.1
      
  StrategyAgent:
    class: "src.agents.strategy_agent.StrategyAgent"
    config:
      strategies:
        - "SPOT_BALANCED"
        - "CURVE_BALANCED" 
        - "BID_ASK_BALANCED"
        - "SPOT_IMBALANCED_DAMM"
      default_risk_profile:
        il_cut: -0.08  # -8%
        var_cut: 0.05   # 5%
        sigma_cut: 0.05 # 5%
        holding_window: 3600  # 1 hour
        exit_asset: "SOL"  # or "BNB"
        
  ExecutionAgent:
    class: "src.agents.execution_agent.ExecutionAgent"
    config:
      max_slippage: 0.01
      gas_multiplier: 1.2
      confirmation_blocks: 3
      jupiter_api: "https://quote-api.jup.ag/v6"
      pancake_router: "0x13f4EA83D0bd40E75C8222255bc855a974568Dd4"
      
  RiskSentinelAgent:
    class: "src.agents.risk_sentinel_agno.RiskSentinelAgnoAgent"
    config:
      monitor_interval: 10  # 10 seconds
      il_fuse: -0.08  # -8%
      var_threshold: 0.04  # 4%
      enable_agno: true
      model_name: "qwen2.5:3b"

# 事件路由規則
event_routing:
  "bus.pool":
    source: "MarketIntelAgent"
    targets: ["StrategyAgent"]
    
  "bus.lpplan":
    source: "StrategyAgent" 
    targets: ["ExecutionAgent"]
    condition: "status == 'approved'"
    
  "bus.tx":
    source: "ExecutionAgent"
    targets: ["RiskSentinelAgent", "FeeCollectorTool"]
    
  "ExitRequest":
    source: "RiskSentinelAgent"
    targets: ["ExecutionAgent"]
    priority: "high"
    
  "globalsDiff":
    source: "WebUIAgent"
    targets: ["all"]
    
  "task_state_changed":
    source: "any"
    targets: ["WebUIAgent"]

# 業務循環配置
business_loop:
  enabled: true
  tasks:
    - name: "pool_scanning"
      agent: "MarketIntelAgent"
      interval: 15  # seconds
      
    - name: "risk_monitoring"
      agent: "RiskSentinelAgent" 
      interval: 10  # seconds
      
    - name: "fee_collection"
      agent: "FeeCollectorTool"
      schedule: "0 2 * * *"  # Daily at 02:00 UTC
      
    - name: "portfolio_rebalance"
      agent: "PortfolioManagerAgent"
      interval: 300  # 5 minutes

# WebSocket 配置
websocket:
  enabled: true
  port: 8001
  path: "/ws/dyflow"
  cors_origins: ["http://localhost:3001"]
  
# 監控和日誌
monitoring:
  prometheus:
    enabled: true
    port: 9090
    metrics_path: "/metrics"
    
  logging:
    level: "INFO"
    format: "json"
    file: "logs/dyflow_v33.log"
    max_size: "100MB"
    backup_count: 5
