# DyFlow Low-Float Meme Coin LP Strategy Workflow
# 基於PRD v3.0的完整工作流程配置
# Framework: Agno | Chains: BSC (PancakeSwap v3) & Solana (Meteora DLMM v2)

version: "1"
name: "low-float-meme-lp-strategy"
description: "24/7自動化單邊LP策略工作流程"

# 全局配置
config:
  chains: ["bsc", "solana"]
  max_concurrent_steps: 3
  error_retry_attempts: 3
  error_retry_delay: 30s
  execution_timeout: 300s
  
  # 策略參數
  strategy_params:
    min_tvl_usd: 20000          # 最小TVL 20k USD
    max_fdv_usd: 1000000        # 最大FDV 1M USD
    max_circulating_ratio: 0.05  # 流通/FDV ≤ 5%
    tick_range_percent: 12.5     # ±12.5% tick範圍
    
    # 風險管理
    il_fuse_threshold: -0.08     # IL ≤ -8% 熔斷
    var_fuse_threshold: 0.04     # VaR ≥ 4% 熔斷
    
    # DCA參數
    harvest_interval_hours: 4    # 每4小時harvest
    dca_trigger_percent: 0.20    # 20%上漲觸發DCA
    dca_exit_percent: 0.10       # 撤出10%流動性

# 工作流程步驟
steps:
  # ========== 數據收集階段 ==========
  
  # 1. Solana池子掃描
  - id: scanSol
    name: "掃描Solana Meteora DLMM池子"
    uses: MeteoraDLMMTool.scanPools
    schedule: "*/45 * * * * *"  # 每45秒
    config:
      api_endpoint: "https://dlmm-api.meteora.ag/pair/all"
      min_tvl: 20000
      max_pools: 100
    outputs:
      - solana_pools
    on_error: continue
    
  # 2. BSC池子掃描  
  - id: scanBsc
    name: "掃描BSC PancakeSwap V3池子"
    uses: PancakeSubgraphTool.scanPools
    schedule: "*/30 * * * * *"  # 每30秒
    config:
      subgraph_url: "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
      api_key: "9731921233db132a98c2325878e6c153"
      min_tvl: 20000
      max_pools: 100
    outputs:
      - bsc_pools
    on_error: continue

  # 3. 價格數據獲取
  - id: fetchPrices
    name: "獲取代幣價格數據"
    uses: CoinGeckoTool.fetchPrices
    schedule: "*/60 * * * * *"  # 每60秒
    config:
      tokens: ["solana", "binancecoin", "ethereum", "bitcoin"]
      vs_currency: "usd"
    outputs:
      - token_prices
    on_error: continue

  # ========== 分析和決策階段 ==========
  
  # 4. 池子篩選和評分
  - id: pick
    name: "池子發現和篩選"
    agent: PoolPicker
    needs: [scanSol, scanBsc, fetchPrices]
    schedule: "*/5 * * * *"  # 每5分鐘
    config:
      scoring_factors:
        - tvl_score: 0.25
        - volume_score: 0.20
        - apr_score: 0.20
        - risk_score: 0.15
        - liquidity_score: 0.10
        - trend_score: 0.10
      min_total_score: 60
    outputs:
      - top_pools
      - pool_scores
    timeout: 120s

  # 5. LP範圍管理和執行
  - id: rebalance
    name: "LP範圍重新平衡"
    agent: RangeRebalancer
    needs: [pick]
    schedule: "*/15 * * * *"  # 每15分鐘
    config:
      tick_range_percent: 12.5
      rebalance_threshold: 0.05  # 5%偏移觸發重新平衡
      max_slippage: 0.02         # 2%最大滑點
    outputs:
      - rebalance_actions
      - position_updates
    timeout: 300s

  # 6. DCA和對沖執行
  - id: hedge
    name: "DCA出貨和對沖執行"
    agent: HedgeExecutor
    needs: [rebalance]
    schedule: "*/10 * * * *"  # 每10分鐘
    config:
      dca_trigger_percent: 0.20
      dca_exit_percent: 0.10
      hedge_ratio: 0.5           # 50%對沖比例
      min_profit_threshold: 0.05  # 5%最小利潤閾值
    outputs:
      - hedge_actions
      - dca_receipts
    timeout: 180s

  # ========== 風險管理階段 ==========
  
  # 7. 風險監控
  - id: risk
    name: "風險監控和熔斷"
    agent: RiskSentinel
    schedule: "*/15 * * * * *"  # 每15秒
    config:
      il_fuse_threshold: -0.08
      var_fuse_threshold: 0.04
      monitoring_window_hours: 24
      alert_channels: ["supabase", "log"]
    outputs:
      - risk_alerts
      - fuse_triggers
    timeout: 60s
    priority: high

  # 8. 緊急退出處理
  - id: exit
    name: "緊急退出處理"
    agent: ExitHandler
    needs: [risk]
    condition: "risk.fuse_triggered == true"
    config:
      exit_strategy: "immediate"  # immediate, gradual
      max_slippage: 0.05         # 緊急情況允許5%滑點
      parallel_execution: true
    outputs:
      - exit_receipts
      - recovery_actions
    timeout: 120s
    priority: critical

  # ========== 組合管理階段 ==========
  
  # 9. 組合管理和報告
  - id: portfolio
    name: "組合管理和NAV計算"
    agent: PortfolioManager
    needs: [hedge, exit]
    schedule: "*/15 * * * *"  # 每15分鐘
    config:
      nav_calculation_method: "mark_to_market"
      performance_benchmark: "hodl"
      reporting_frequency: "hourly"
    outputs:
      - portfolio_nav
      - performance_metrics
      - position_summary
    timeout: 90s

  # 10. 數據持久化
  - id: persist
    name: "數據同步到Supabase"
    uses: SupabaseDbTool.syncData
    needs: [portfolio]
    schedule: "*/5 * * * *"  # 每5分鐘
    config:
      tables:
        - positions
        - hedges
        - fees
        - alerts
        - portfolio_nav
      batch_size: 100
      upsert_strategy: "merge"
    outputs:
      - sync_status
    timeout: 60s

  # ========== 監控和告警階段 ==========
  
  # 11. 系統健康檢查
  - id: healthCheck
    name: "系統健康檢查"
    uses: SystemHealthTool.check
    schedule: "*/2 * * * *"  # 每2分鐘
    config:
      checks:
        - rpc_connectivity
        - api_latency
        - memory_usage
        - error_rates
      alert_thresholds:
        rpc_latency_ms: 5000
        error_rate_percent: 10
        memory_usage_percent: 80
    outputs:
      - health_status
      - system_metrics
    timeout: 30s

  # 12. 性能指標收集
  - id: metrics
    name: "Prometheus指標收集"
    uses: PrometheusMetrics.collect
    schedule: "*/30 * * * * *"  # 每30秒
    config:
      metrics:
        - dyflow_il_pct
        - dyflow_var_24h
        - lp_fee_daily
        - rpc_latency_ms
        - swap_error_ratio
        - portfolio_nav_usd
        - active_positions_count
    outputs:
      - prometheus_metrics
    timeout: 15s

# ========== 錯誤處理和恢復 ==========

error_handling:
  # 全局錯誤處理
  global:
    max_retries: 3
    retry_delay: 30s
    fallback_strategy: "continue"
    
  # 特定步驟錯誤處理
  step_specific:
    scanSol:
      on_timeout: "use_cached_data"
      on_api_error: "switch_to_backup_endpoint"
      
    scanBsc:
      on_timeout: "use_cached_data"
      on_rate_limit: "exponential_backoff"
      
    risk:
      on_error: "trigger_safe_mode"
      safe_mode_actions:
        - "pause_new_positions"
        - "increase_monitoring_frequency"
        - "send_critical_alert"

# ========== 條件執行規則 ==========

conditions:
  # 市場時間條件
  market_hours:
    description: "主要在市場活躍時間執行"
    condition: "hour >= 6 && hour <= 22"  # UTC 6-22點
    applies_to: ["pick", "rebalance", "hedge"]
    
  # 高波動期間
  high_volatility:
    description: "高波動期間增加監控頻率"
    condition: "market_volatility > 0.05"
    actions:
      - increase_frequency: ["risk", "healthCheck"]
      - decrease_frequency: ["rebalance"]
      
  # 低流動性期間
  low_liquidity:
    description: "低流動性期間暫停新建倉"
    condition: "market_liquidity < 0.3"
    actions:
      - pause_steps: ["rebalance"]
      - enable_steps: ["exit"]

# ========== 通知和告警 ==========

notifications:
  channels:
    supabase:
      table: "alerts"
      severity_levels: ["high", "critical"]
      
    prometheus:
      metrics_endpoint: "http://localhost:9090/metrics"
      alert_manager: "http://localhost:9093"
      
    log:
      level: "info"
      format: "structured"
      
  alert_rules:
    il_fuse_triggered:
      severity: "critical"
      message: "IL熔斷觸發，所有位置已緊急退出"
      channels: ["supabase", "log"]
      
    var_fuse_triggered:
      severity: "critical"
      message: "VaR熔斷觸發，風險過高"
      channels: ["supabase", "log"]
      
    high_error_rate:
      severity: "high"
      condition: "error_rate > 0.1"
      message: "系統錯誤率過高"
      channels: ["log"]
      
    rpc_latency_high:
      severity: "medium"
      condition: "rpc_latency > 5000"
      message: "RPC延遲過高"
      channels: ["log"]

# ========== 性能優化 ==========

optimization:
  caching:
    enabled: true
    ttl_seconds: 300
    cache_keys:
      - "pool_data"
      - "token_prices"
      - "risk_metrics"
      
  parallel_execution:
    enabled: true
    max_workers: 5
    parallel_steps:
      - ["scanSol", "scanBsc", "fetchPrices"]
      - ["rebalance", "hedge"]
      
  resource_limits:
    memory_mb: 1024
    cpu_percent: 70
    network_timeout_seconds: 30
