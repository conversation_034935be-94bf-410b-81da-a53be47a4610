"""
Low-Float Meme Coin LP Strategy Workflow - DyFlow v3 + Agno Framework
低流通 Meme 幣 LP 策略主工作流
基於 Agno Framework 的事件驅動、24x7 自動化單邊 LP 策略
"""

import asyncio
import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog
import signal

# 添加項目根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.agno_scheduler import AGNOScheduler, ExecutionMode
from src.agents.planner_agno import PlannerAgnoAgent
from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
from src.agents.range_rebalancer_agent import RangeRebalancerAgent
from src.agents.hedge_agent import HedgeAgent
from src.agents.portfolio_agent import PortfolioAgent
from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
from src.utils.config_loader import load_config
from src.utils.database_manager import DatabaseManager
from src.utils.helpers import get_utc_timestamp

# 配置日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# ========== Workflow Configuration ==========

WORKFLOW_CONFIG = {
    "name": "low_float_meme_coin_lp_strategy",
    "version": "3.1",
    "description": "24x7 automated single-sided LP strategy for low-float meme coins",
    
    # 執行配置
    "execution": {
        "mode": "continuous",  # continuous, scheduled, event_driven
        "interval_minutes": 15,  # 主循環間隔
        "max_concurrent_agents": 5,
        "enable_failover": True,
        "enable_monitoring": True
    },
    
    # Agent 配置
    "agents": {
        "scanner": {
            "enabled": True,
            "critical": False,
            "max_retries": 3,
            "timeout_seconds": 120
        },
        "scorer": {
            "enabled": True,
            "critical": False,
            "max_retries": 2,
            "timeout_seconds": 180,
            "dependencies": ["scanner"]
        },
        "planner": {
            "enabled": True,
            "critical": True,
            "max_retries": 3,
            "timeout_seconds": 300,
            "dependencies": ["scorer"]
        },
        "rebalancer": {
            "enabled": True,
            "critical": False,
            "max_retries": 2,
            "timeout_seconds": 600,
            "dependencies": ["planner"]
        },
        "hedge": {
            "enabled": True,
            "critical": False,
            "max_retries": 2,
            "timeout_seconds": 300,
            "dependencies": ["rebalancer"]
        },
        "portfolio": {
            "enabled": True,
            "critical": False,
            "max_retries": 2,
            "timeout_seconds": 180,
            "dependencies": ["hedge"]
        },
        "risk_sentinel": {
            "enabled": True,
            "critical": True,
            "max_retries": 1,
            "timeout_seconds": 120,
            "dependencies": []  # 獨立運行
        }
    },
    
    # 風險管理
    "risk_management": {
        "il_fuse_threshold": -8.0,  # -8% IL 熔斷
        "var_fuse_threshold": 4.0,   # 4% VaR 熔斷
        "max_position_size": 0.15,   # 最大單一持倉 15%
        "emergency_exit_enabled": True
    }
}

# ========== Low Float Workflow ==========

class LowFloatWorkflow:
    """低流通 Meme 幣 LP 策略工作流"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = load_config(config_path)
        self.database = DatabaseManager(self.config)
        self.scheduler = AGNOScheduler(self.config, self.database)
        
        # 工作流狀態
        self.is_running = False
        self.shutdown_requested = False
        self.current_cycle = 0
        
        # 性能指標
        self.metrics = {
            "total_cycles": 0,
            "successful_cycles": 0,
            "failed_cycles": 0,
            "last_execution_time": None,
            "average_cycle_duration": 0.0
        }
        
        logger.info("low_float_workflow_initialized", 
                   config=WORKFLOW_CONFIG["name"],
                   version=WORKFLOW_CONFIG["version"])
    
    async def initialize(self) -> bool:
        """初始化工作流"""
        try:
            logger.info("workflow_initialization_started")
            
            # 初始化數據庫
            await self.database.initialize()
            
            # 初始化調度器
            await self.scheduler.initialize()
            
            # 註冊 Agents
            await self._register_agents()
            
            # 設置事件處理器
            self._setup_event_handlers()
            
            # 設置信號處理器
            self._setup_signal_handlers()
            
            logger.info("workflow_initialization_completed")
            return True
            
        except Exception as e:
            logger.error("workflow_initialization_failed", error=str(e))
            return False
    
    async def _register_agents(self) -> None:
        """註冊所有 Agents"""
        try:
            # Scanner Agent (池子掃描)
            scanner_agent = ScorerV2AgnoAgent("scanner", self.config, self.database)
            self.scheduler.register_agent(
                "scanner", 
                scanner_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["scanner"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["scanner"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["scanner"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["scanner"]["critical"]
            )
            
            # Scorer Agent (池子評分)
            scorer_agent = ScorerV2AgnoAgent("scorer", self.config, self.database)
            self.scheduler.register_agent(
                "scorer",
                scorer_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["scorer"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["scorer"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["scorer"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["scorer"]["critical"]
            )
            
            # Planner Agent (策略規劃)
            planner_agent = PlannerAgnoAgent("planner", self.config, self.database)
            self.scheduler.register_agent(
                "planner",
                planner_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["planner"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["planner"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["planner"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["planner"]["critical"]
            )
            
            # Range Rebalancer Agent (範圍重平衡)
            rebalancer_agent = RangeRebalancerAgent("rebalancer", self.config, self.database)
            self.scheduler.register_agent(
                "rebalancer",
                rebalancer_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["rebalancer"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["rebalancer"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["rebalancer"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["rebalancer"]["critical"]
            )
            
            # Hedge Agent (對沖和 DCA)
            hedge_agent = HedgeAgent("hedge", self.config, self.database)
            self.scheduler.register_agent(
                "hedge",
                hedge_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["hedge"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["hedge"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["hedge"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["hedge"]["critical"]
            )
            
            # Portfolio Agent (投資組合管理)
            portfolio_agent = PortfolioAgent("portfolio", self.config, self.database)
            self.scheduler.register_agent(
                "portfolio",
                portfolio_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["portfolio"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["portfolio"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["portfolio"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["portfolio"]["critical"]
            )
            
            # Risk Sentinel Agent (風險監控)
            risk_agent = RiskSentinelAgnoAgent("risk_sentinel", self.config, self.database)
            self.scheduler.register_agent(
                "risk_sentinel",
                risk_agent,
                dependencies=WORKFLOW_CONFIG["agents"]["risk_sentinel"].get("dependencies", []),
                max_retries=WORKFLOW_CONFIG["agents"]["risk_sentinel"]["max_retries"],
                timeout_seconds=WORKFLOW_CONFIG["agents"]["risk_sentinel"]["timeout_seconds"],
                critical=WORKFLOW_CONFIG["agents"]["risk_sentinel"]["critical"]
            )
            
            logger.info("agents_registered", count=len(WORKFLOW_CONFIG["agents"]))
            
        except Exception as e:
            logger.error("agent_registration_failed", error=str(e))
            raise
    
    def _setup_event_handlers(self) -> None:
        """設置事件處理器"""
        try:
            # 工作流事件
            self.scheduler.add_event_handler("workflow_started", self._on_workflow_started)
            self.scheduler.add_event_handler("workflow_completed", self._on_workflow_completed)
            
            # Agent 事件
            self.scheduler.add_event_handler("agent_started", self._on_agent_started)
            self.scheduler.add_event_handler("agent_completed", self._on_agent_completed)
            self.scheduler.add_event_handler("agent_failed", self._on_agent_failed)
            
            # 風險事件
            self.scheduler.add_event_handler("risk_alert", self._on_risk_alert)
            self.scheduler.add_event_handler("emergency_exit", self._on_emergency_exit)
            
            logger.info("event_handlers_setup_completed")
            
        except Exception as e:
            logger.error("event_handlers_setup_failed", error=str(e))
    
    def _setup_signal_handlers(self) -> None:
        """設置信號處理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            logger.info("signal_handlers_setup_completed")
        except Exception as e:
            logger.error("signal_handlers_setup_failed", error=str(e))
    
    def _signal_handler(self, signum, frame):
        """信號處理器"""
        logger.info("shutdown_signal_received", signal=signum)
        self.shutdown_requested = True
    
    # ========== Event Handlers ==========
    
    async def _on_workflow_started(self, data: Dict[str, Any]) -> None:
        """工作流開始事件處理"""
        logger.info("workflow_started_event", data=data)
        self.metrics["last_execution_time"] = datetime.now()
    
    async def _on_workflow_completed(self, data: Dict[str, Any]) -> None:
        """工作流完成事件處理"""
        status = data.get("status", "unknown")
        self.metrics["total_cycles"] += 1
        
        if status == "completed":
            self.metrics["successful_cycles"] += 1
        else:
            self.metrics["failed_cycles"] += 1
        
        logger.info("workflow_completed_event", 
                   status=status,
                   metrics=self.metrics)
    
    async def _on_agent_started(self, data: Dict[str, Any]) -> None:
        """Agent 開始事件處理"""
        logger.debug("agent_started_event", data=data)
    
    async def _on_agent_completed(self, data: Dict[str, Any]) -> None:
        """Agent 完成事件處理"""
        logger.debug("agent_completed_event", data=data)
    
    async def _on_agent_failed(self, data: Dict[str, Any]) -> None:
        """Agent 失敗事件處理"""
        logger.warning("agent_failed_event", data=data)
    
    async def _on_risk_alert(self, data: Dict[str, Any]) -> None:
        """風險告警事件處理"""
        logger.warning("risk_alert_event", data=data)
        
        # 根據風險級別採取行動
        risk_level = data.get("level", "medium")
        if risk_level in ["high", "critical"]:
            # 觸發緊急處理
            await self._handle_high_risk_alert(data)
    
    async def _on_emergency_exit(self, data: Dict[str, Any]) -> None:
        """緊急退出事件處理"""
        logger.critical("emergency_exit_event", data=data)
        # 實施緊急退出程序
        await self._execute_emergency_exit()
    
    async def _handle_high_risk_alert(self, alert_data: Dict[str, Any]) -> None:
        """處理高風險告警"""
        try:
            logger.warning("handling_high_risk_alert", alert=alert_data)
            
            # 暫停非關鍵 Agent
            non_critical_agents = ["scanner", "scorer", "rebalancer", "hedge"]
            for agent_name in non_critical_agents:
                self.scheduler.disable_agent(agent_name)
            
            # 執行風險評估
            risk_result = await self.scheduler.execute_workflow(["risk_sentinel"])
            
            # 根據結果決定是否恢復
            if risk_result.status == "completed":
                # 重新啟用 Agent
                for agent_name in non_critical_agents:
                    self.scheduler.enable_agent(agent_name)
            
        except Exception as e:
            logger.error("high_risk_alert_handling_failed", error=str(e))
    
    async def _execute_emergency_exit(self) -> None:
        """執行緊急退出"""
        try:
            logger.critical("executing_emergency_exit")
            
            # 停止所有非關鍵操作
            # 執行緊急平倉
            # 保存狀態
            # 發送告警通知
            
            self.shutdown_requested = True
            
        except Exception as e:
            logger.error("emergency_exit_failed", error=str(e))

    # ========== Main Execution Loop ==========

    async def run(self) -> None:
        """運行主工作流"""
        try:
            if not await self.initialize():
                logger.error("workflow_initialization_failed")
                return

            self.is_running = True
            logger.info("workflow_started", config=WORKFLOW_CONFIG)

            # 主執行循環
            while not self.shutdown_requested:
                try:
                    cycle_start = datetime.now()
                    self.current_cycle += 1

                    logger.info("workflow_cycle_started", cycle=self.current_cycle)

                    # 執行主工作流
                    agent_sequence = [
                        "risk_sentinel",  # 首先檢查風險
                        "scanner",        # 掃描新池子
                        "scorer",         # 評分池子
                        "planner",        # 制定策略
                        "rebalancer",     # 重平衡範圍
                        "hedge",          # 對沖和 DCA
                        "portfolio"       # 投資組合管理
                    ]

                    # 過濾啟用的 Agent
                    enabled_agents = [
                        agent for agent in agent_sequence
                        if WORKFLOW_CONFIG["agents"].get(agent, {}).get("enabled", True)
                    ]

                    # 執行工作流
                    workflow_result = await self.scheduler.execute_workflow(
                        enabled_agents,
                        workflow_id=f"cycle_{self.current_cycle}"
                    )

                    # 計算執行時間
                    cycle_duration = (datetime.now() - cycle_start).total_seconds()
                    self._update_metrics(cycle_duration, workflow_result.status)

                    logger.info("workflow_cycle_completed",
                               cycle=self.current_cycle,
                               status=workflow_result.status,
                               duration_seconds=cycle_duration,
                               completed_agents=len(workflow_result.completed_agents),
                               failed_agents=len(workflow_result.failed_agents))

                    # 檢查是否需要緊急停止
                    if workflow_result.status == "failed":
                        failed_critical_agents = [
                            agent for agent in workflow_result.failed_agents
                            if WORKFLOW_CONFIG["agents"].get(agent, {}).get("critical", False)
                        ]

                        if failed_critical_agents:
                            logger.critical("critical_agents_failed",
                                           agents=failed_critical_agents)
                            await self._execute_emergency_exit()
                            break

                    # 等待下一個循環
                    if not self.shutdown_requested:
                        interval = WORKFLOW_CONFIG["execution"]["interval_minutes"] * 60
                        await asyncio.sleep(interval)

                except Exception as e:
                    logger.error("workflow_cycle_failed",
                               cycle=self.current_cycle,
                               error=str(e))

                    # 短暫等待後重試
                    await asyncio.sleep(30)

            logger.info("workflow_shutdown_completed",
                       total_cycles=self.current_cycle,
                       metrics=self.metrics)

        except Exception as e:
            logger.error("workflow_execution_failed", error=str(e))
        finally:
            await self.cleanup()

    def _update_metrics(self, cycle_duration: float, status: str) -> None:
        """更新性能指標"""
        try:
            # 更新平均執行時間
            total_duration = (self.metrics["average_cycle_duration"] *
                            (self.metrics["total_cycles"] - 1) + cycle_duration)
            self.metrics["average_cycle_duration"] = total_duration / self.metrics["total_cycles"]

            # 更新最後執行時間
            self.metrics["last_execution_time"] = datetime.now()

        except Exception as e:
            logger.error("metrics_update_failed", error=str(e))

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("workflow_cleanup_started")

            # 清理調度器
            await self.scheduler.cleanup()

            # 清理數據庫
            await self.database.cleanup()

            self.is_running = False

            logger.info("workflow_cleanup_completed")

        except Exception as e:
            logger.error("workflow_cleanup_failed", error=str(e))

    def get_status(self) -> Dict[str, Any]:
        """獲取工作流狀態"""
        return {
            "is_running": self.is_running,
            "current_cycle": self.current_cycle,
            "shutdown_requested": self.shutdown_requested,
            "metrics": self.metrics,
            "scheduler_metrics": self.scheduler.get_agent_metrics(),
            "workflow_config": WORKFLOW_CONFIG
        }

# ========== Main Entry Point ==========

async def main():
    """主入口點"""
    try:
        logger.info("dyflow_low_float_strategy_starting")

        # 創建並運行工作流
        workflow = LowFloatWorkflow()
        await workflow.run()

    except KeyboardInterrupt:
        logger.info("workflow_interrupted_by_user")
    except Exception as e:
        logger.error("workflow_main_failed", error=str(e))
    finally:
        logger.info("dyflow_low_float_strategy_shutdown")

if __name__ == "__main__":
    # 運行主工作流
    asyncio.run(main())
