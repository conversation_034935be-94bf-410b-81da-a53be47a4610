# Dy-Flow AI Agent API 文档

## 健康检查端点

### GET /health

检查系统健康状态

**响应示例：**
```json
{
  "status": "healthy",
  "timestamp": **********.789,
  "components": {
    "scheduler": true,
    "data_aggregator": true,
    "agents": 7,
    "data_providers": 2
  }
}
```

### GET /status

获取详细系统状态

**响应示例：**
```json
{
  "system": {
    "running": true,
    "uptime": 3600
  },
  "agents": {
    "pool_hunter": "active",
    "risk_sentinel": "active",
    "scorer": "active",
    "planner": "active",
    "executor": "active",
    "earnings_auditor": "active",
    "cli_reporter": "active"
  },
  "data_providers": {
    "bsc": "connected",
    "solana": "connected"
  },
  "scheduler": {
    "active": true,
    "jobs": 5
  }
}
```

### GET /metrics

获取 Prometheus 格式的监控指标

**响应示例：**
```
dyflow_system_running 1
dyflow_agents_count 7
dyflow_data_providers_count 2
dyflow_agent_active{agent="pool_hunter"} 1
dyflow_agent_active{agent="risk_sentinel"} 1
dyflow_agent_active{agent="scorer"} 1
dyflow_agent_active{agent="planner"} 1
dyflow_agent_active{agent="executor"} 1
dyflow_agent_active{agent="earnings_auditor"} 1
dyflow_agent_active{agent="cli_reporter"} 1
```

## Agent 工作流程

### AGNO DAG 执行流程

1. **池子猎手 (Pool Hunter)**
   - 发现新的流动性池
   - 筛选符合条件的池子
   - 收集基础数据

2. **风险哨兵 (Risk Sentinel)**
   - 评估池子风险
   - 监控价格波动
   - 检查安全指标

3. **评分器 (Scorer)**
   - 综合评分算法
   - APR/TVL/风险加权
   - 排序推荐池子

4. **规划器 (Planner)**
   - 制定交易策略
   - 资金分配计划
   - 风险管理规则

5. **执行器 (Executor)**
   - 执行交易操作
   - 提供/撤出流动性
   - 监控交易状态

6. **收益审计员 (Earnings Auditor)**
   - 计算实际收益
   - 对比预期表现
   - 生成收益报告

7. **CLI 报告器 (CLI Reporter)**
   - 实时状态显示
   - 交互式界面
   - 操作结果反馈

## 配置 API

### 动态配置

系统支持通过配置文件动态调整参数：

- `config/agno_dag.yaml` - DAG 调度配置
- `config/default.yaml` - 基础配置
- `config/networks.yaml` - 网络配置
- `config/strategies.yaml` - 策略配置
- `config/pools.yaml` - 池子配置

### 环境变量

关键环境变量：

```env
BSC_PRIVATE_KEY          # BSC 私钥
SOLANA_PRIVATE_KEY       # Solana 私钥
BSC_RPC_URL             # BSC RPC 端点
SOLANA_RPC_URL          # Solana RPC 端点
LOG_LEVEL               # 日志级别
CHECK_INTERVAL          # 检查间隔(秒)
HEALTH_CHECK_PORT       # 健康检查端口
```

## 错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 500 | 内部服务器错误 |
| 503 | 服务不可用 |

## 监控集成

### Prometheus 指标

系统提供以下 Prometheus 指标：

- `dyflow_system_running`: 系统运行状态 (0/1)
- `dyflow_agents_count`: 活跃 Agent 数量
- `dyflow_data_providers_count`: 数据提供者数量
- `dyflow_agent_active{agent="name"}`: 单个 Agent 状态

### 日志格式

系统使用结构化 JSON 日志：

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "logger": "dyflow.core.scheduler",
  "event": "agent_executed",
  "agent": "pool_hunter",
  "duration": 1.23,
  "status": "success"
}
```

## 使用示例

### 健康检查脚本

```bash
#!/bin/bash
response=$(curl -s http://localhost:8080/health)
status=$(echo $response | jq -r '.status')

if [ "$status" = "healthy" ]; then
    echo "✅ 系统运行正常"
    exit 0
else
    echo "❌ 系统异常: $response"
    exit 1
fi
```

### 监控脚本

```python
import requests
import time

def monitor_system():
    while True:
        try:
            response = requests.get('http://localhost:8080/status')
            data = response.json()
            
            print(f"系统状态: {data['system']['running']}")
            print(f"运行时间: {data['system']['uptime']}秒")
            print(f"活跃 Agent: {len(data['agents'])}")
            
        except Exception as e:
            print(f"监控异常: {e}")
        
        time.sleep(30)

if __name__ == "__main__":
    monitor_system()