# Dy-Flow AI Agent 部署指南

## 快速开始

### 本地部署（推荐新手）

1. **克隆项目**
```bash
git clone <repository-url>
cd dyflow
```

2. **一键安装**
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

3. **配置环境**
```bash
# 编辑环境变量
cp .env.example .env
# 填入您的 BSC 和 Solana 私钥
```

4. **启动系统**
```bash
# 激活虚拟环境
source venv/bin/activate

# 启动 AI Agent
python main.py
```

### Docker 部署（推荐生产环境）

1. **准备环境**
```bash
# 安装 Docker 和 Docker Compose
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose

# macOS
brew install docker docker-compose

# 或访问 https://docs.docker.com/get-docker/
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，至少填入以下必要配置：
# BSC_PRIVATE_KEY=your_bsc_private_key
# SOLANA_PRIVATE_KEY=your_solana_private_key
# POSTGRES_PASSWORD=your_secure_password
```

3. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f dyflow
```

4. **验证部署**
```bash
# 检查健康状态
curl http://localhost:8080/health

# 查看系统状态
curl http://localhost:8080/status
```

## 系统架构

```
dyflow/
├── main.py                 # 主程序入口
├── requirements.txt        # Python 依赖
├── docker-compose.yml      # Docker 配置
├── Dockerfile             # 容器镜像
├── .env.example           # 环境变量模板
│
├── src/
│   ├── agents/            # 7个核心 Agent
│   │   ├── pool_hunter.py    # 池子猎手
│   │   ├── risk_sentinel.py  # 风险哨兵
│   │   ├── scorer.py         # 评分器
│   │   ├── planner.py        # 规划器
│   │   ├── executor.py       # 执行器
│   │   ├── earnings_auditor.py # 收益审计员
│   │   └── cli_reporter.py   # CLI 报告器
│   ├── core/              # 核心调度器
│   │   └── agno_scheduler.py # AGNO DAG 调度器
│   ├── llm/               # LLM 集成
│   ├── algorithms/        # 核心算法
│   ├── integrations/      # 数据源集成
│   └── ui/                # CLI 界面
│
├── config/
│   ├── agno_dag.yaml      # DAG 配置
│   ├── default.yaml       # 基础配置
│   ├── networks.yaml      # 网络配置
│   ├── strategies.yaml    # 策略配置
│   └── pools.yaml         # 池子配置
│
├── docs/
│   └── DEPLOYMENT.md      # 本文档
│
└── scripts/
    └── setup.sh           # 一键安装脚本
```

## 服务说明

### 核心服务

| 服务 | 端口 | 说明 |
|------|------|------|
| dyflow | 8080 | 主应用服务 |
| postgres | 5432 | 数据库服务（内部） |
| redis | 6379 | 缓存服务（内部） |
| ollama | 11434 | LLM 推理服务 |

### 访问地址

- **健康检查**: http://localhost:8080/health
- **系统状态**: http://localhost:8080/status
- **Prometheus 指标**: http://localhost:8080/metrics

## 配置说明

### 环境变量

核心配置项：

```env
# 必须配置
BSC_PRIVATE_KEY=your_bsc_private_key_here
SOLANA_PRIVATE_KEY=your_solana_private_key_here

# 推荐配置
BSC_RPC_URL=https://bsc-dataseed1.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
LOG_LEVEL=INFO

# 数据库配置（Docker 自动配置）
POSTGRES_PASSWORD=your_secure_password
```

### 策略配置

编辑 `config/strategies.yaml`：

```yaml
risk_management:
  max_slippage: 0.01  # 1% 最大滑点
  emergency_exit_threshold: 0.10  # 10% 紧急退出
  min_apr_threshold: 0.05  # 5% 最小APR

strategy:
  min_tvl: 100000  # $100k 最小TVL
  preferred_tokens:
    - "BTC"
    - "ETH"
    - "BNB"
    - "SOL"
    - "USDT"
    - "USDC"
```

## 运维管理

### 日常操作

```bash
# 查看运行状态
docker-compose ps

# 查看实时日志
docker-compose logs -f dyflow

# 重启服务
docker-compose restart dyflow

# 更新配置后重启
docker-compose down
docker-compose up -d

# 备份数据
docker-compose exec postgres pg_dump -U dyflow dyflow > backup.sql
```

### 监控指标

系统提供 Prometheus 格式的监控指标：

```bash
curl http://localhost:8080/metrics
```

关键指标：
- `dyflow_system_running`: 系统运行状态
- `dyflow_agents_count`: Agent 数量
- `dyflow_data_providers_count`: 数据提供者数量

### 故障排查

1. **系统无法启动**
```bash
# 检查日志
docker-compose logs dyflow

# 检查配置
docker-compose config

# 重建镜像
docker-compose build --no-cache dyflow
```

2. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U dyflow

# 重启数据库
docker-compose restart postgres
```

3. **Agent 执行异常**
```bash
# 查看详细日志
docker-compose logs -f dyflow | grep ERROR

# 检查健康状态
curl http://localhost:8080/health
```

## 安全建议

1. **私钥安全**
   - 使用环境变量存储私钥
   - 不要将私钥提交到代码仓库
   - 定期轮换私钥

2. **网络安全**
   - 使用防火墙限制访问
   - 考虑使用 VPN
   - 定期更新依赖

3. **监控告警**
   - 监控系统健康状态
   - 设置异常告警
   - 定期检查日志

## 性能优化

1. **资源分配**
```yaml
# docker-compose.yml 中添加资源限制
services:
  dyflow:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

2. **数据库优化**
   - 定期清理历史数据
   - 优化查询索引
   - 监控连接池

3. **缓存策略**
   - 使用 Redis 缓存热点数据
   - 调整缓存过期时间
   - 监控缓存命中率

## 升级指南

1. **备份数据**
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U dyflow dyflow > backup_$(date +%Y%m%d).sql

# 备份配置文件
cp -r config config_backup_$(date +%Y%m%d)
```

2. **更新代码**
```bash
git pull origin main
```

3. **重新部署**
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 常见问题

### Q: 如何增加新的监控池子？
A: 编辑 `config/pools.yaml`，添加池子地址和配置，重启系统生效。

### Q: 如何调整检查频率？
A: 修改 `config/agno_dag.yaml` 中的 `schedule_interval` 参数。

### Q: 如何启用通知功能？
A: 在 `.env` 中配置 Telegram Bot Token 和 Chat ID。

### Q: 系统支持哪些网络？
A: 目前支持 BSC 和 Solana，可在 `config/networks.yaml` 中查看详细配置。

---

如有问题，请查看项目 README.md 或提交 Issue。