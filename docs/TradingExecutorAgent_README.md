# TradingExecutorAgent - DyFlow 交易執行系統

## 概述

TradingExecutorAgent 是 DyFlow v3 系統的核心交易執行組件，基於 Agno Framework 構建，支持完整的 Solana 鏈上交易操作和 DLMM LP 策略部署。

## 主要功能

### 🔄 Swap 功能 (使用 Jupiter SDK)
- **簡單交換**: SOL ↔ 任意 SPL 代幣
- **批量交換**: 一次執行多個交換操作
- **DCA 交換**: 分批執行降低價格影響
- **最優路由**: 自動選擇最佳交換路徑
- **價格影響分析**: 評估不同金額的價格影響

### 🎯 DLMM LP 策略部署
支持四種主要的流動性提供策略：

#### 1. SPOT_BALANCED (對稱流動性)
- **描述**: 在當前價格周圍均匀分布流動性
- **適用場景**: 穩定市場，低波動環境
- **風險等級**: LOW
- **預期 APR**: 15%

#### 2. CURVE_BALANCED (曲線分布)
- **描述**: 按曲線分布流動性，中心集中
- **適用場景**: 波動市場，需要靈活調整
- **風險等級**: MEDIUM
- **預期 APR**: 25%

#### 3. BID_ASK_BALANCED (買賣價差)
- **描述**: 分別在買賣兩側提供流動性
- **適用場景**: 做市策略，賺取價差
- **風險等級**: MEDIUM
- **預期 APR**: 30%

#### 4. SPOT_IMBALANCED (單邊流動性)
- **描述**: 主要在一個方向提供流動性
- **適用場景**: 趨勢市場，方向性投注
- **風險等級**: HIGH
- **預期 APR**: 40%

### 🔄 完整循環管理
- **SOL → DLMM LP**: 從 SOL 開始部署 LP 策略
- **DLMM LP → SOL**: 退出 LP 持倉並轉換回 SOL
- **自動收割**: 定期收割手續費收入
- **一鍵平倉**: 緊急情況下快速退出

## 快速開始

### 1. 安裝依賴

```bash
# 安裝 Python 依賴
pip install -r requirements.txt

# 安裝 Solana CLI (可選)
sh -c "$(curl -sSfL https://release.solana.com/v1.16.0/install)"

# 安裝 Ollama (用於本地 AI 模型)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull qwen2.5:3b
```

### 2. 基本使用

```python
import asyncio
from src.agents.trading_executor_agent import TradingExecutorAgent, TradingRequest, TradingAction

async def main():
    # 創建 Agent
    agent = TradingExecutorAgent({
        "name": "MyTradingAgent",
        "model_provider": "ollama",
        "model_name": "qwen2.5:3b"
    })
    
    # 初始化
    await agent.initialize()
    
    # 設置錢包 (請安全管理私鑰)
    agent.set_wallet("your_private_key_here")
    
    # 執行簡單交換
    swap_request = TradingRequest(
        action=TradingAction.SWAP,
        parameters={
            "input_mint": "So11111111111111111111111111111111111111112",  # SOL
            "output_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "amount": "**********",  # 1 SOL
            "user_public_key": "your_wallet_address"
        }
    )
    
    result = await agent.execute_trading_request(swap_request)
    print(f"交換結果: {result.success}")
    
    # 清理
    await agent.cleanup()

asyncio.run(main())
```

### 3. 部署 LP 策略

```python
# 部署對稱流動性策略
lp_request = TradingRequest(
    action=TradingAction.DEPLOY_LP,
    parameters={
        "pool_address": "your_pool_address",
        "strategy_type": "spot_balanced",
        "token_amount": 1000.0,
        "token_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "strategy_config": {
            "range_width": 20,
            "distribution_type": "uniform"
        }
    }
)

result = await agent.execute_trading_request(lp_request)
```

### 4. 完整循環操作

```python
# SOL → DLMM LP 循環
result = await agent.sol_to_dlmm_lp_cycle(
    sol_amount=2.0,
    pool_address="your_pool_address",
    strategy_type="curve_balanced",
    range_width=30,
    curve_steepness=2.5
)

# DLMM LP → SOL 循環
result = await agent.dlmm_lp_to_sol_cycle(
    pool_address="your_pool_address",
    wallet_address="your_wallet_address",
    exit_percentage=1.0  # 100% 退出
)
```

## 配置選項

### Agent 配置

```python
config = {
    "name": "TradingExecutor",
    "model_provider": "ollama",  # 或 "openai"
    "model_name": "qwen2.5:3b",
    "enable_reasoning": True,
    "enable_memory": True,
    "max_retries": 3
}
```

### 策略配置

每種策略都有特定的配置參數：

```python
# 對稱流動性策略
spot_balanced_config = {
    "range_width": 20,
    "distribution_type": "uniform"
}

# 曲線分布策略
curve_balanced_config = {
    "curve_steepness": 2.5,
    "range_width": 50,
    "concentration_factor": 0.8
}

# 買賣價差策略
bid_ask_config = {
    "bid_range": 15,
    "ask_range": 15,
    "bid_weight": 0.6,
    "ask_weight": 0.4
}

# 單邊流動性策略
spot_imbalanced_config = {
    "direction": "long",  # 或 "short"
    "range_width": 30,
    "concentration_bins": 5,
    "tail_distribution": 0.3
}
```

## 安全注意事項

### 🔐 私鑰管理
- **永遠不要**在代碼中硬編碼私鑰
- 使用環境變量或安全的密鑰管理系統
- 考慮使用硬件錢包進行生產環境部署

### 💰 資金安全
- 在主網使用前，先在測試網充分測試
- 設置合理的滑點和優先費用
- 監控交易狀態和確認

### 🛡️ 風險管理
- 設置止損和止盈條件
- 分散投資，不要將所有資金投入單一策略
- 定期監控持倉表現

## 測試

```bash
# 運行基本功能測試
python test_trading_executor.py

# 運行完整示例
python examples/trading_executor_example.py
```

## 故障排除

### 常見問題

1. **Ollama 不可用**
   ```bash
   # 安裝並啟動 Ollama
   ollama serve
   ollama pull qwen2.5:3b
   ```

2. **Solana RPC 連接失敗**
   - 檢查網絡連接
   - 嘗試使用不同的 RPC 端點
   - 確認 Solana 網絡狀態

3. **交易失敗**
   - 檢查錢包餘額
   - 確認滑點設置
   - 檢查代幣地址是否正確

### 日誌調試

```python
import structlog

# 啟用詳細日誌
structlog.configure(
    processors=[
        structlog.stdlib.add_log_level,
        structlog.processors.JSONRenderer()
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)
```

## 貢獻

歡迎提交 Issue 和 Pull Request 來改進這個系統！

## 許可證

本項目採用 MIT 許可證。
