<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow 階段數據測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .phase-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .phase-completed { background: #d4edda; border-color: #c3e6cb; }
        .phase-running { background: #d1ecf1; border-color: #bee5eb; }
        .phase-pending { background: #f8f9fa; border-color: #dee2e6; }
        .phase-failed { background: #f8d7da; border-color: #f5c6cb; }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-completed { background: #28a745; color: white; }
        .status-running { background: #007bff; color: white; }
        .status-pending { background: #6c757d; color: white; }
        .status-failed { background: #dc3545; color: white; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 DyFlow v3.3 階段監控測試</h1>
        
        <div>
            <button class="refresh-btn" onclick="fetchPhaseData()">🔄 刷新階段數據</button>
            <button class="refresh-btn" onclick="testWebSocket()">🔌 測試 WebSocket</button>
            <button class="refresh-btn" onclick="clearLogs()">🧹 清除日誌</button>
        </div>

        <div id="status"></div>
        
        <div id="overview">
            <h2>📊 總體進度</h2>
            <div id="progress-info">載入中...</div>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <div id="phases">
            <h2>📋 階段詳情</h2>
            <div id="phase-list">載入中...</div>
        </div>

        <div id="logs">
            <h2>📝 測試日誌</h2>
            <div id="log-content" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>

    <script>
        let logContent = document.getElementById('log-content');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `[${timestamp}] ${message}\n`;
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function clearLogs() {
            logContent.innerHTML = '';
        }
        
        function showError(message) {
            document.getElementById('status').innerHTML = `<div class="error">❌ ${message}</div>`;
            log(`ERROR: ${message}`);
        }
        
        function showSuccess(message) {
            document.getElementById('status').innerHTML = `<div class="success">✅ ${message}</div>`;
            log(`SUCCESS: ${message}`);
        }
        
        async function fetchPhaseData() {
            try {
                log('開始獲取階段數據...');
                const response = await fetch('http://localhost:8001/api/phases/status');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`成功獲取階段數據: 當前階段 ${data.current_phase}, 進度 ${data.progress_percentage.toFixed(1)}%`);
                
                renderPhaseData(data);
                showSuccess(`階段數據已更新 - 當前 Phase ${data.current_phase}`);
                
            } catch (error) {
                showError(`獲取階段數據失敗: ${error.message}`);
            }
        }
        
        function renderPhaseData(data) {
            // 更新總體進度
            const progressInfo = document.getElementById('progress-info');
            const progressFill = document.getElementById('progress-fill');
            
            progressInfo.innerHTML = `
                當前階段: <strong>Phase ${data.current_phase}</strong><br>
                已完成: <strong>${data.completed_phases}/${data.total_phases}</strong> 階段<br>
                進度: <strong>${data.progress_percentage.toFixed(1)}%</strong>
            `;
            progressFill.style.width = `${data.progress_percentage}%`;
            
            // 更新階段列表
            const phaseList = document.getElementById('phase-list');
            const phaseNames = [
                '系統初始化', '健康檢查', 'UI 啟動', '錢包測試', '市場情報',
                '投資組合', '策略生成', '交易執行', '風控監控'
            ];
            
            let html = '';
            for (let i = 0; i < 9; i++) {
                const phaseStatus = data.phase_status[i.toString()] || { status: 'pending' };
                const phaseName = phaseNames[i] || `Phase ${i}`;
                
                const statusClass = `phase-${phaseStatus.status}`;
                const badgeClass = `status-${phaseStatus.status}`;
                
                const statusText = {
                    'completed': '已完成',
                    'running': '運行中',
                    'failed': '失敗',
                    'pending': '等待中'
                }[phaseStatus.status] || '未知';
                
                html += `
                    <div class="phase-card ${statusClass}">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <strong>Phase ${i}: ${phaseName}</strong>
                            <span class="status-badge ${badgeClass}">${statusText}</span>
                        </div>
                        ${phaseStatus.started_at ? `<div style="font-size: 12px; color: #666; margin-top: 5px;">開始: ${new Date(phaseStatus.started_at).toLocaleString()}</div>` : ''}
                        ${phaseStatus.completed_at ? `<div style="font-size: 12px; color: #666;">完成: ${new Date(phaseStatus.completed_at).toLocaleString()}</div>` : ''}
                        ${phaseStatus.error ? `<div style="font-size: 12px; color: #dc3545; margin-top: 5px;">錯誤: ${phaseStatus.error}</div>` : ''}
                    </div>
                `;
            }
            
            phaseList.innerHTML = html;
        }
        
        function testWebSocket() {
            log('測試 WebSocket 連接...');
            
            try {
                const ws = new WebSocket('ws://localhost:8001/ws');
                
                ws.onopen = function() {
                    log('WebSocket 連接成功');
                    showSuccess('WebSocket 連接正常');
                    
                    // 發送測試消息
                    ws.send(JSON.stringify({ type: 'test', message: 'Hello from test page' }));
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`收到 WebSocket 消息: ${data.type}`);
                    } catch (e) {
                        log(`收到 WebSocket 消息: ${event.data}`);
                    }
                };
                
                ws.onerror = function(error) {
                    log(`WebSocket 錯誤: ${error}`);
                    showError('WebSocket 連接失敗');
                };
                
                ws.onclose = function() {
                    log('WebSocket 連接關閉');
                };
                
                // 5秒後關閉連接
                setTimeout(() => {
                    ws.close();
                }, 5000);
                
            } catch (error) {
                showError(`WebSocket 測試失敗: ${error.message}`);
            }
        }
        
        // 頁面載入時自動獲取數據
        window.onload = function() {
            log('頁面載入完成，開始測試...');
            fetchPhaseData();
            
            // 設置自動刷新
            setInterval(fetchPhaseData, 10000); // 每10秒刷新一次
        };
    </script>
</body>
</html>
