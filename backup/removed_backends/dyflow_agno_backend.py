"""
DyFlow Agno Backend - 基于Agno Framework的后端服务
整合所有API逻辑到Agno架构中，提供统一的数据接口
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

# FastAPI imports
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
import uvicorn

# Agno Framework imports (可选)
try:
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

# DyFlow imports
from src.supervisor import DyFlowSupervisor, SupervisorConfig
from src.agents.data_provider_agent import DataProviderAgent
from src.utils.config import Config
from src.utils.helpers import get_utc_timestamp

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# FastAPI应用
app = FastAPI(
    title="DyFlow Agno Backend",
    description="基于Agno Framework的DyFlow后端服务",
    version="1.0.0"
)

# 全局变量
supervisor: Optional[DyFlowSupervisor] = None
data_provider_agent: Optional[DataProviderAgent] = None
websocket_connections: List[WebSocket] = []

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("websocket_connected", total_connections=len(self.active_connections))
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info("websocket_disconnected", total_connections=len(self.active_connections))
    
    async def broadcast(self, message: dict):
        """广播消息到所有连接的客户端"""
        if not self.active_connections:
            return
            
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.warning("websocket_send_failed", error=str(e))
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)

# WebSocket管理器实例
websocket_manager = WebSocketManager()

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global supervisor, data_provider_agent
    
    try:
        logger.info("dyflow_agno_backend_starting")
        
        # 初始化配置
        config = Config()
        
        # 初始化数据提供代理
        if AGNO_AVAILABLE:
            data_provider_agent = DataProviderAgent()
            logger.info("data_provider_agent_initialized")
        else:
            logger.warning("agno_framework_not_available")
        
        # 初始化Supervisor (可选)
        supervisor_config = SupervisorConfig(
            enable_agno=AGNO_AVAILABLE,
            enable_scheduling=True,
            enable_monitoring=True
        )
        
        supervisor = DyFlowSupervisor(config, supervisor_config)
        await supervisor.initialize()
        
        # 启动数据更新任务
        asyncio.create_task(data_update_loop())
        
        logger.info("dyflow_agno_backend_started")
        
    except Exception as e:
        logger.error("startup_failed", error=str(e))
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    global supervisor
    
    try:
        logger.info("dyflow_agno_backend_shutting_down")
        
        if supervisor:
            await supervisor.stop()
        
        logger.info("dyflow_agno_backend_shutdown_complete")
        
    except Exception as e:
        logger.error("shutdown_failed", error=str(e))

async def data_update_loop():
    """数据更新循环 - 定期获取最新数据并广播"""
    while True:
        try:
            if data_provider_agent:
                # 获取实时数据
                data = await data_provider_agent.get_real_time_pools(['bsc', 'solana'])
                
                # 广播到所有WebSocket连接
                await websocket_manager.broadcast({
                    'type': 'pool_data_update',
                    'data': data,
                    'timestamp': datetime.utcnow().isoformat()
                })
                
                logger.debug("data_broadcasted", 
                           bsc_pools=len(data.get('bsc_pools', [])),
                           solana_pools=len(data.get('solana_pools', [])))
            
            # 等待5秒后下次更新
            await asyncio.sleep(5)
            
        except Exception as e:
            logger.error("data_update_loop_error", error=str(e))
            await asyncio.sleep(30)  # 错误时等待更长时间

@app.get("/")
async def read_root():
    """返回主页面"""
    try:
        with open("dyflow_enhanced_dashboard.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>DyFlow Agno Backend</title></head>
            <body>
                <h1>DyFlow Agno Backend</h1>
                <p>基于Agno Framework的DyFlow后端服务正在运行</p>
                <p>API文档: <a href="/docs">/docs</a></p>
            </body>
        </html>
        """)

@app.get("/api/real-data")
async def get_real_data(chains: str = "bsc,solana"):
    """获取实时池子数据 - REST API接口"""
    try:
        if not data_provider_agent:
            raise HTTPException(status_code=503, detail="数据提供服务不可用")
        
        # 解析链参数
        chain_list = [chain.strip() for chain in chains.split(',')]
        
        # 获取数据
        data = await data_provider_agent.get_real_time_pools(chain_list)
        
        logger.info("api_real_data_requested", 
                   chains=chain_list,
                   bsc_pools=len(data.get('bsc_pools', [])),
                   solana_pools=len(data.get('solana_pools', [])))
        
        return data
        
    except Exception as e:
        logger.error("api_real_data_failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"数据获取失败: {str(e)}")

@app.get("/api/system-status")
async def get_system_status():
    """获取系统状态"""
    try:
        status = {
            'timestamp': get_utc_timestamp(),
            'agno_available': AGNO_AVAILABLE,
            'data_provider_active': data_provider_agent is not None,
            'supervisor_active': supervisor is not None and supervisor.is_running,
            'websocket_connections': len(websocket_manager.active_connections)
        }
        
        if supervisor:
            supervisor_status = supervisor.get_system_status()
            status.update(supervisor_status)
        
        return status
        
    except Exception as e:
        logger.error("system_status_failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"系统状态获取失败: {str(e)}")

@app.post("/api/agent/execute/{agent_name}")
async def execute_agent(agent_name: str):
    """手动执行指定Agent"""
    try:
        if not supervisor:
            raise HTTPException(status_code=503, detail="Supervisor不可用")
        
        result = await supervisor.execute_agent_manually(agent_name)
        
        logger.info("agent_manually_executed", 
                   agent_name=agent_name,
                   status=result.status)
        
        return {
            'agent_name': result.agent_name,
            'status': result.status,
            'execution_time': result.execution_time,
            'timestamp': result.timestamp.isoformat(),
            'error_message': result.error_message,
            'metadata': result.metadata
        }
        
    except Exception as e:
        logger.error("agent_execution_failed", agent_name=agent_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"Agent执行失败: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 实时数据推送"""
    await websocket_manager.connect(websocket)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理客户端请求
            if message.get('type') == 'get_data':
                if data_provider_agent:
                    chains = message.get('chains', ['bsc', 'solana'])
                    pool_data = await data_provider_agent.get_real_time_pools(chains)
                    
                    await websocket.send_text(json.dumps({
                        'type': 'pool_data',
                        'data': pool_data,
                        'timestamp': datetime.utcnow().isoformat()
                    }))
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error("websocket_error", error=str(e))
        websocket_manager.disconnect(websocket)

if __name__ == "__main__":
    # 运行服务器
    uvicorn.run(
        "dyflow_agno_backend:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
