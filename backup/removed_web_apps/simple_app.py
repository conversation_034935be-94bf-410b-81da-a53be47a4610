#!/usr/bin/env python3
"""
DyFlow Simple Web UI Dashboard
簡化版本，避免複雜的Agent導入問題
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn

# FastAPI應用
app = FastAPI(title="DyFlow Dashboard", version="3.0")

# 靜態文件和模板
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass  # 如果static目錄不存在就跳過

templates = Jinja2Templates(directory="templates")

# WebSocket連接管理
websocket_connections: List[WebSocket] = []

class DashboardData:
    """Dashboard數據管理"""
    
    def __init__(self):
        self.system_status = {
            "supervisor": {
                "is_running": True,
                "agno_enabled": False,
                "uptime_seconds": 3600
            },
            "agents": {
                "pool_picker": {
                    "execution_count": 15,
                    "error_count": 0,
                    "last_execution": datetime.now().isoformat(),
                    "status": "active"
                },
                "risk_sentinel": {
                    "execution_count": 120,
                    "error_count": 2,
                    "last_execution": datetime.now().isoformat(),
                    "status": "active"
                },
                "range_rebalancer": {
                    "execution_count": 8,
                    "error_count": 0,
                    "last_execution": datetime.now().isoformat(),
                    "status": "active"
                }
            },
            "health": {
                "overall_status": "healthy",
                "total_checks": 6,
                "healthy_checks": 5,
                "warning_checks": 1,
                "critical_checks": 0
            }
        }
        
        self.pool_data = [
            {
                "id": "sol_usdc_meteora",
                "chain": "solana",
                "protocol": "meteora_dlmm",
                "pair": "SOL/USDC",
                "tvl_usd": 2500000,
                "volume_24h": 850000,
                "apr": 15.2,
                "risk_level": "medium",
                "score": 78
            },
            {
                "id": "bnb_usdt_pancake",
                "chain": "bsc", 
                "protocol": "pancakeswap_v3",
                "pair": "BNB/USDT",
                "tvl_usd": 5200000,
                "volume_24h": 1200000,
                "apr": 12.8,
                "risk_level": "low",
                "score": 85
            },
            {
                "id": "pepe_sol_meteora",
                "chain": "solana",
                "protocol": "meteora_dlmm", 
                "pair": "PEPE/SOL",
                "tvl_usd": 180000,
                "volume_24h": 95000,
                "apr": 45.6,
                "risk_level": "high",
                "score": 65
            },
            {
                "id": "wbnb_cake_pancake",
                "chain": "bsc",
                "protocol": "pancakeswap_v3",
                "pair": "WBNB/CAKE",
                "tvl_usd": 890000,
                "volume_24h": 320000,
                "apr": 22.4,
                "risk_level": "medium",
                "score": 72
            }
        ]
        
        self.positions = [
            {
                "id": "pos_001",
                "pool": "SOL/USDC",
                "chain": "solana",
                "liquidity_usd": 5000,
                "pnl_pct": 8.5,
                "il_pct": -2.1,
                "status": "active",
                "range": "±12.5%",
                "apr": 15.2
            },
            {
                "id": "pos_002", 
                "pool": "BNB/USDT",
                "chain": "bsc",
                "liquidity_usd": 8000,
                "pnl_pct": 12.3,
                "il_pct": -1.8,
                "status": "active",
                "range": "±12.5%",
                "apr": 12.8
            },
            {
                "id": "pos_003",
                "pool": "PEPE/SOL", 
                "chain": "solana",
                "liquidity_usd": 2500,
                "pnl_pct": -5.2,
                "il_pct": -6.8,
                "status": "monitoring",
                "range": "±12.5%",
                "apr": 45.6
            }
        ]
        
        self.risk_alerts = [
            {
                "id": "alert_001",
                "level": "warning",
                "message": "PEPE/SOL池子IL接近-7%，接近熔斷閾值",
                "timestamp": datetime.now() - timedelta(minutes=15),
                "resolved": False
            },
            {
                "id": "alert_002",
                "level": "info", 
                "message": "BNB/USDT池子重新平衡完成",
                "timestamp": datetime.now() - timedelta(hours=2),
                "resolved": True
            }
        ]
        
        self.agent_logs = [
            {
                "timestamp": datetime.now() - timedelta(minutes=1),
                "agent": "PoolPicker",
                "level": "info",
                "message": "發現4個新的高分池子，BSC鏈50個池子掃描完成"
            },
            {
                "timestamp": datetime.now() - timedelta(minutes=3),
                "agent": "RiskSentinel", 
                "level": "warning",
                "message": "檢測到PEPE/SOL池子IL增加至-6.8%"
            },
            {
                "timestamp": datetime.now() - timedelta(minutes=5),
                "agent": "RangeRebalancer",
                "level": "info", 
                "message": "BNB/USDT池子重新平衡完成，調整tick範圍"
            },
            {
                "timestamp": datetime.now() - timedelta(minutes=8),
                "agent": "HedgeAgent",
                "level": "info",
                "message": "SOL/USDC池子DCA觸發，執行10%出貨"
            }
        ]
        
        self.last_update = datetime.now()
    
    async def update_data(self):
        """更新數據 (模擬實時變化)"""
        import random
        
        # 模擬價格變化
        for pool in self.pool_data:
            # 隨機調整APR (±2%)
            pool['apr'] += random.uniform(-2, 2)
            pool['apr'] = max(0, pool['apr'])
            
            # 隨機調整TVL (±5%)
            change = random.uniform(-0.05, 0.05)
            pool['tvl_usd'] *= (1 + change)
            pool['volume_24h'] *= (1 + change * 0.5)
        
        # 模擬持倉變化
        for position in self.positions:
            # 隨機調整PnL (±1%)
            change = random.uniform(-1, 1)
            position['pnl_pct'] += change
            position['il_pct'] += random.uniform(-0.5, 0.2)
        
        # 添加新的Agent日誌
        if random.random() < 0.3:  # 30%機率添加新日誌
            agents = ["PoolPicker", "RiskSentinel", "RangeRebalancer", "HedgeAgent"]
            messages = [
                "池子掃描完成",
                "風險檢查正常",
                "重新平衡執行",
                "DCA策略觸發"
            ]
            
            new_log = {
                "timestamp": datetime.now(),
                "agent": random.choice(agents),
                "level": "info",
                "message": random.choice(messages)
            }
            
            self.agent_logs.insert(0, new_log)
            self.agent_logs = self.agent_logs[:20]  # 只保留最近20條
        
        self.last_update = datetime.now()

# 全局Dashboard數據
dashboard_data = DashboardData()

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    print("🚀 DyFlow Simple Web UI 啟動中...")
    
    # 啟動數據更新任務
    asyncio.create_task(update_dashboard_data())
    
    print("✅ DyFlow Simple Web UI 已啟動")
    print("📱 訪問 http://localhost:8080 查看Dashboard")

async def update_dashboard_data():
    """定期更新Dashboard數據"""
    while True:
        try:
            await dashboard_data.update_data()
            
            # 廣播更新到所有WebSocket連接
            if websocket_connections:
                data = {
                    "type": "dashboard_update",
                    "data": {
                        "system_status": dashboard_data.system_status,
                        "pool_data": dashboard_data.pool_data,
                        "positions": dashboard_data.positions,
                        "risk_alerts": dashboard_data.risk_alerts,
                        "agent_logs": dashboard_data.agent_logs[:10],  # 最近10條
                        "last_update": dashboard_data.last_update.isoformat()
                    }
                }
                
                # 發送到所有連接的客戶端
                disconnected = []
                for websocket in websocket_connections:
                    try:
                        await websocket.send_text(json.dumps(data, default=str))
                    except:
                        disconnected.append(websocket)
                
                # 移除斷開的連接
                for ws in disconnected:
                    websocket_connections.remove(ws)
            
            await asyncio.sleep(5)  # 每5秒更新一次
            
        except Exception as e:
            print(f"數據更新失敗: {e}")
            await asyncio.sleep(10)

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主Dashboard頁面"""
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "DyFlow Dashboard"
    })

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端點"""
    await websocket.accept()
    websocket_connections.append(websocket)
    
    try:
        # 發送初始數據
        initial_data = {
            "type": "initial_data",
            "data": {
                "system_status": dashboard_data.system_status,
                "pool_data": dashboard_data.pool_data,
                "positions": dashboard_data.positions,
                "risk_alerts": dashboard_data.risk_alerts,
                "agent_logs": dashboard_data.agent_logs[:10],
                "last_update": dashboard_data.last_update.isoformat()
            }
        }
        await websocket.send_text(json.dumps(initial_data, default=str))
        
        # 保持連接
        while True:
            data = await websocket.receive_text()
            # 處理客戶端消息
            message = json.loads(data)
            
            if message.get("type") == "agent_command":
                # 處理Agent命令
                await handle_agent_command(websocket, message)
                
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)

async def handle_agent_command(websocket: WebSocket, message: Dict):
    """處理Agent命令"""
    try:
        command = message.get("command")
        
        if command == "execute_agent":
            agent_name = message.get("agent_name")
            # 模擬Agent執行
            response = {
                "type": "command_response",
                "success": True,
                "message": f"Agent {agent_name} 執行完成"
            }
        elif command == "emergency_exit":
            # 緊急退出命令
            response = {
                "type": "command_response", 
                "success": True,
                "message": "緊急退出指令已發送，正在關閉所有LP持倉"
            }
        else:
            response = {
                "type": "command_response",
                "success": False,
                "message": f"未知命令: {command}"
            }
        
        await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        error_response = {
            "type": "command_response",
            "success": False,
            "message": f"命令執行失敗: {str(e)}"
        }
        await websocket.send_text(json.dumps(error_response))

@app.get("/api/status")
async def get_system_status():
    """獲取系統狀態API"""
    return {
        "status": "running",
        "last_update": dashboard_data.last_update.isoformat(),
        "system_status": dashboard_data.system_status
    }

@app.get("/api/pools")
async def get_pools():
    """獲取池子數據API"""
    return {
        "pools": dashboard_data.pool_data,
        "count": len(dashboard_data.pool_data),
        "last_update": dashboard_data.last_update.isoformat()
    }

@app.get("/api/positions")
async def get_positions():
    """獲取持倉數據API"""
    return {
        "positions": dashboard_data.positions,
        "count": len(dashboard_data.positions),
        "total_value": sum(pos["liquidity_usd"] for pos in dashboard_data.positions),
        "last_update": dashboard_data.last_update.isoformat()
    }

if __name__ == "__main__":
    uvicorn.run(
        "simple_app:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
