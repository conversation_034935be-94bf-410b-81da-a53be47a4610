#!/usr/bin/env python3
"""
DyFlow React UI FastAPI Backend
完全基於React的現代化WebUI後端
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 導入項目配置和數據提供者
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.trading_config import (
    is_target_pair,
    get_agent_filter_config,
    API_CONFIG
)

# 導入現有的數據提供者
from modern_app import ModernDataProvider, ModernDashboardData

# FastAPI應用
app = FastAPI(
    title="DyFlow React UI Backend", 
    version="4.0",
    description="24/7 自動化流動性挖礦策略系統 - React UI後端"
)

# CORS設置 - 允許React開發服務器訪問
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket連接管理
websocket_connections: List[WebSocket] = []

# 全局Dashboard數據 - 重用現有的數據提供者
dashboard_data = ModernDashboardData()

# React應用路徑
REACT_BUILD_PATH = Path(__file__).parent.parent / "react-ui" / "dist"
REACT_DEV_PATH = Path(__file__).parent.parent / "react-ui"

def setup_static_files():
    """設置靜態文件服務"""
    try:
        if REACT_BUILD_PATH.exists() and (REACT_BUILD_PATH / "index.html").exists():
            # 生產環境：使用構建後的React應用
            print(f"🏗️ 使用生產構建: {REACT_BUILD_PATH}")
            # 掛載整個dist目錄，而不只是assets
            app.mount("/assets", StaticFiles(directory=str(REACT_BUILD_PATH / "assets")), name="assets")
            app.mount("/static", StaticFiles(directory=str(REACT_BUILD_PATH)), name="static")
            return "production"
        else:
            # 開發環境：使用開發版本
            print(f"🛠️ 使用開發版本: {REACT_DEV_PATH}")
            if (REACT_DEV_PATH / "test-static.html").exists():
                app.mount("/static", StaticFiles(directory=str(REACT_DEV_PATH)), name="static")
                return "development"
            else:
                print("❌ 未找到React應用文件")
                return "none"
    except Exception as e:
        print(f"❌ 設置靜態文件失敗: {e}")
        return "error"

# 定義路由函數
async def serve_react_app():
    """服務React應用主頁"""
    try:
        if react_mode == "production":
            return FileResponse(str(REACT_BUILD_PATH / "index.html"))
        elif react_mode == "development":
            return FileResponse(str(REACT_DEV_PATH / "test-static.html"))
        else:
            return HTMLResponse("""
            <html>
                <head><title>DyFlow React UI</title></head>
                <body>
                    <h1>DyFlow React UI</h1>
                    <p>React應用未找到。請先構建React應用或確保開發文件存在。</p>
                    <h2>構建指令：</h2>
                    <pre>
cd react-ui
npm run build
                    </pre>
                </body>
            </html>
            """)
    except Exception as e:
        return HTMLResponse(f"<h1>錯誤</h1><p>{str(e)}</p>")

async def serve_react_routes(path: str):
    """處理React路由 - SPA支持"""
    # 如果是API請求，不處理
    if path.startswith("api/") or path.startswith("ws"):
        return {"error": "Not found"}

    # 對於所有其他路由，返回React應用
    return await serve_react_app()

# 設置靜態文件並註冊路由
react_mode = setup_static_files()

# 註冊路由
app.add_api_route("/", serve_react_app, methods=["GET"], response_class=HTMLResponse)
app.add_api_route("/{path:path}", serve_react_routes, methods=["GET"])

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端點 - 與React UI通信"""
    await websocket.accept()
    websocket_connections.append(websocket)
    
    print(f"🔌 新的WebSocket連接，總連接數: {len(websocket_connections)}")
    
    try:
        # 發送初始數據
        initial_data = {
            "type": "initial_data",
            "data": {
                "system_status": dashboard_data.system_status,
                "pool_data": dashboard_data.pool_data,
                "positions": dashboard_data.positions,
                "risk_alerts": dashboard_data.risk_alerts,
                "agent_logs": dashboard_data.agent_logs[:10],
                "token_prices": dashboard_data.token_prices,
                "last_update": dashboard_data.last_update.isoformat()
            }
        }
        await websocket.send_text(json.dumps(initial_data, default=str))
        
        # 保持連接並處理消息
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            print(f"📨 收到WebSocket消息: {message.get('type')}")
            
            if message.get("type") == "force_update":
                # 強制更新數據
                await dashboard_data.update_real_data()
                print("🔄 強制數據更新完成")
                
            elif message.get("type") == "agent_command":
                # 處理Agent命令
                await handle_agent_command(websocket, message)
                
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)
        print(f"🔌 WebSocket連接斷開，剩餘連接數: {len(websocket_connections)}")

async def handle_agent_command(websocket: WebSocket, message: Dict):
    """處理Agent命令"""
    try:
        command = message.get("command")
        
        if command == "evaluate_pool":
            pool_data = message.get("pool_data", {})
            pool_pair = pool_data.get("pair", "Unknown")
            pool_chain = pool_data.get("chain", "unknown")
            
            # 模擬Agent評估過程
            evaluation_result = await evaluate_pool_with_agent(pool_data)
            
            response = {
                "type": "command_response",
                "success": True,
                "message": f"Agent已完成 {pool_pair} ({pool_chain.upper()}) 池子評估",
                "evaluation": evaluation_result
            }
            
            # 添加評估日誌
            dashboard_data.agent_logs.insert(0, {
                "timestamp": datetime.now(),
                "agent": "PoolEvaluator",
                "level": "success" if evaluation_result["recommended"] else "warning",
                "message": f"池子 {pool_pair} 評估完成 - {'推薦投資' if evaluation_result['recommended'] else '不推薦投資'}"
            })
            
        elif command == "emergency_exit":
            response = {
                "type": "command_response",
                "success": True,
                "message": "緊急退出指令已發送，正在關閉所有LP持倉"
            }
            
            # 添加緊急退出日誌
            dashboard_data.agent_logs.insert(0, {
                "timestamp": datetime.now(),
                "agent": "EmergencyController",
                "level": "error",
                "message": "用戶觸發緊急退出，正在關閉所有LP持倉"
            })
            
        else:
            response = {
                "type": "command_response",
                "success": False,
                "message": f"未知命令: {command}"
            }
        
        await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        error_response = {
            "type": "command_response",
            "success": False,
            "message": f"命令執行失敗: {str(e)}"
        }
        await websocket.send_text(json.dumps(error_response))

async def evaluate_pool_with_agent(pool_data: Dict) -> Dict:
    """使用Agent評估池子"""
    try:
        # 模擬Agent評估邏輯
        tvl = pool_data.get("tvl_usd", 0)
        apr = pool_data.get("apr", 0)
        risk_level = pool_data.get("risk_level", "high")
        
        # 簡單的評估邏輯
        score = 0
        reasons = []
        
        if tvl >= 100000:
            score += 30
            reasons.append("TVL充足")
        elif tvl >= 10000:
            score += 20
            reasons.append("TVL適中")
        else:
            reasons.append("TVL較低")
        
        if 5 <= apr <= 50:
            score += 30
            reasons.append("APR合理")
        elif apr > 50:
            score += 10
            reasons.append("APR過高，風險較大")
        else:
            reasons.append("APR較低")
        
        if risk_level == "low":
            score += 20
            reasons.append("風險等級低")
        elif risk_level == "medium":
            score += 10
            reasons.append("風險等級中等")
        else:
            reasons.append("風險等級高")
        
        recommended = score >= 50
        
        return {
            "score": score,
            "recommended": recommended,
            "reasons": reasons,
            "risk_assessment": risk_level,
            "recommendation": "推薦投資" if recommended else "不推薦投資"
        }
        
    except Exception as e:
        return {
            "score": 0,
            "recommended": False,
            "reasons": [f"評估失敗: {str(e)}"],
            "risk_assessment": "unknown",
            "recommendation": "評估失敗"
        }

async def update_dashboard_data():
    """定期更新Dashboard數據並廣播到所有WebSocket連接"""
    while True:
        try:
            await dashboard_data.update_real_data()
            
            # 廣播更新到所有WebSocket連接
            if websocket_connections:
                data = {
                    "type": "dashboard_update",
                    "data": {
                        "system_status": dashboard_data.system_status,
                        "pool_data": dashboard_data.pool_data,
                        "positions": dashboard_data.positions,
                        "risk_alerts": dashboard_data.risk_alerts,
                        "agent_logs": dashboard_data.agent_logs[:10],
                        "token_prices": dashboard_data.token_prices,
                        "last_update": dashboard_data.last_update.isoformat()
                    }
                }
                
                # 發送到所有連接的客戶端
                disconnected = []
                for websocket in websocket_connections:
                    try:
                        await websocket.send_text(json.dumps(data, default=str))
                    except:
                        disconnected.append(websocket)
                
                # 移除斷開的連接
                for ws in disconnected:
                    websocket_connections.remove(ws)
            
            await asyncio.sleep(15)  # 每15秒更新一次
            
        except Exception as e:
            print(f"❌ 數據更新失敗: {e}")
            await asyncio.sleep(30)

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    print("🚀 DyFlow React UI Backend 啟動中...")
    
    # 初始化數據提供者
    await dashboard_data.data_provider.initialize()
    
    # 立即獲取一次數據
    await dashboard_data.update_real_data()
    
    # 啟動數據更新任務
    asyncio.create_task(update_dashboard_data())
    
    print("✅ DyFlow React UI Backend 已啟動")
    print(f"📱 React模式: {react_mode}")
    print("🌐 訪問 http://localhost:8082 查看React UI")

@app.on_event("shutdown")
async def shutdown_event():
    """應用關閉事件"""
    await dashboard_data.cleanup()

if __name__ == "__main__":
    uvicorn.run(
        "react_app:app",
        host="0.0.0.0",
        port=8082,
        reload=True,
        log_level="info"
    )
