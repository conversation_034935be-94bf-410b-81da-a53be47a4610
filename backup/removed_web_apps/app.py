#!/usr/bin/env python3
"""
DyFlow Web UI Dashboard
基於FastAPI的實時監控界面
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加src到路径
sys.path.insert(0, '../src')
sys.path.insert(0, '../')

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn
import structlog

# DyFlow imports
from src.supervisor import DyFlowSupervisor, SupervisorConfig
from src.tools.system_health_tool import SystemHealthTool
from src.tools.prometheus_metrics_tool import PrometheusMetricsTool
from src.utils.config import Config

logger = structlog.get_logger(__name__)

# FastAPI應用
app = FastAPI(title="DyFlow Dashboard", version="3.0")

# 靜態文件和模板
app.mount("/static", StaticFiles(directory="web_ui/static"), name="static")
templates = Jinja2Templates(directory="web_ui/templates")

# 全局變量
supervisor: Optional[DyFlowSupervisor] = None
health_tool: Optional[SystemHealthTool] = None
metrics_tool: Optional[PrometheusMetricsTool] = None
websocket_connections: List[WebSocket] = []

class DashboardData:
    """Dashboard數據管理"""
    
    def __init__(self):
        self.system_status = {}
        self.pool_data = []
        self.positions = []
        self.risk_alerts = []
        self.performance_metrics = {}
        self.agent_logs = []
        self.last_update = datetime.now()
    
    async def update_all_data(self):
        """更新所有數據"""
        try:
            # 1. 系統狀態
            if supervisor:
                self.system_status = supervisor.get_system_status()
            
            # 2. 健康檢查
            if health_tool:
                health_result = await health_tool.run("full")
                self.system_status['health'] = health_result
            
            # 3. 指標數據
            if metrics_tool:
                metrics_result = await metrics_tool.run("collect")
                self.performance_metrics = metrics_result
            
            # 4. 模擬池子數據 (實際應從PoolPicker獲取)
            self.pool_data = await self._get_pool_data()
            
            # 5. 模擬持倉數據
            self.positions = await self._get_positions_data()
            
            # 6. 風險告警
            self.risk_alerts = await self._get_risk_alerts()
            
            # 7. Agent日誌
            self.agent_logs = await self._get_agent_logs()
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error("dashboard_data_update_failed", error=str(e))
    
    async def _get_pool_data(self) -> List[Dict]:
        """獲取池子數據"""
        # 模擬數據 - 實際應從PoolPicker Agent獲取
        return [
            {
                "id": "sol_usdc_meteora",
                "chain": "solana",
                "protocol": "meteora_dlmm",
                "pair": "SOL/USDC",
                "tvl_usd": 2500000,
                "volume_24h": 850000,
                "apr": 15.2,
                "risk_level": "medium",
                "score": 78
            },
            {
                "id": "bnb_usdt_pancake",
                "chain": "bsc", 
                "protocol": "pancakeswap_v3",
                "pair": "BNB/USDT",
                "tvl_usd": 5200000,
                "volume_24h": 1200000,
                "apr": 12.8,
                "risk_level": "low",
                "score": 85
            },
            {
                "id": "pepe_sol_meteora",
                "chain": "solana",
                "protocol": "meteora_dlmm", 
                "pair": "PEPE/SOL",
                "tvl_usd": 180000,
                "volume_24h": 95000,
                "apr": 45.6,
                "risk_level": "high",
                "score": 65
            }
        ]
    
    async def _get_positions_data(self) -> List[Dict]:
        """獲取持倉數據"""
        return [
            {
                "id": "pos_001",
                "pool": "SOL/USDC",
                "chain": "solana",
                "liquidity_usd": 5000,
                "pnl_pct": 8.5,
                "il_pct": -2.1,
                "status": "active",
                "range": "±12.5%",
                "apr": 15.2
            },
            {
                "id": "pos_002", 
                "pool": "BNB/USDT",
                "chain": "bsc",
                "liquidity_usd": 8000,
                "pnl_pct": 12.3,
                "il_pct": -1.8,
                "status": "active",
                "range": "±12.5%",
                "apr": 12.8
            }
        ]
    
    async def _get_risk_alerts(self) -> List[Dict]:
        """獲取風險告警"""
        return [
            {
                "id": "alert_001",
                "level": "warning",
                "message": "SOL/USDC池子IL接近-5%",
                "timestamp": datetime.now() - timedelta(minutes=15),
                "resolved": False
            },
            {
                "id": "alert_002",
                "level": "info", 
                "message": "BNB/USDT池子重新平衡完成",
                "timestamp": datetime.now() - timedelta(hours=2),
                "resolved": True
            }
        ]
    
    async def _get_agent_logs(self) -> List[Dict]:
        """獲取Agent日誌"""
        return [
            {
                "timestamp": datetime.now() - timedelta(minutes=1),
                "agent": "PoolPicker",
                "level": "info",
                "message": "發現3個新的高分池子"
            },
            {
                "timestamp": datetime.now() - timedelta(minutes=3),
                "agent": "RiskSentinel", 
                "level": "warning",
                "message": "檢測到SOL/USDC池子IL增加"
            },
            {
                "timestamp": datetime.now() - timedelta(minutes=5),
                "agent": "RangeRebalancer",
                "level": "info", 
                "message": "BNB/USDT池子重新平衡完成"
            }
        ]

# 全局Dashboard數據
dashboard_data = DashboardData()

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    global supervisor, health_tool, metrics_tool
    
    try:
        logger.info("dyflow_web_ui_starting")
        
        # 初始化工具
        health_tool = SystemHealthTool()
        metrics_tool = PrometheusMetricsTool({'metrics_port': 8001})
        await metrics_tool.initialize()
        
        # 啟動數據更新任務
        asyncio.create_task(update_dashboard_data())
        
        logger.info("dyflow_web_ui_started")
        
    except Exception as e:
        logger.error("web_ui_startup_failed", error=str(e))

async def update_dashboard_data():
    """定期更新Dashboard數據"""
    while True:
        try:
            await dashboard_data.update_all_data()
            
            # 廣播更新到所有WebSocket連接
            if websocket_connections:
                data = {
                    "type": "dashboard_update",
                    "data": {
                        "system_status": dashboard_data.system_status,
                        "pool_data": dashboard_data.pool_data,
                        "positions": dashboard_data.positions,
                        "risk_alerts": dashboard_data.risk_alerts,
                        "performance_metrics": dashboard_data.performance_metrics,
                        "agent_logs": dashboard_data.agent_logs[-10:],  # 最近10條
                        "last_update": dashboard_data.last_update.isoformat()
                    }
                }
                
                # 發送到所有連接的客戶端
                disconnected = []
                for websocket in websocket_connections:
                    try:
                        await websocket.send_text(json.dumps(data, default=str))
                    except:
                        disconnected.append(websocket)
                
                # 移除斷開的連接
                for ws in disconnected:
                    websocket_connections.remove(ws)
            
            await asyncio.sleep(5)  # 每5秒更新一次
            
        except Exception as e:
            logger.error("dashboard_data_update_failed", error=str(e))
            await asyncio.sleep(10)

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主Dashboard頁面"""
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "DyFlow Dashboard"
    })

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端點"""
    await websocket.accept()
    websocket_connections.append(websocket)
    
    try:
        # 發送初始數據
        initial_data = {
            "type": "initial_data",
            "data": {
                "system_status": dashboard_data.system_status,
                "pool_data": dashboard_data.pool_data,
                "positions": dashboard_data.positions,
                "risk_alerts": dashboard_data.risk_alerts,
                "performance_metrics": dashboard_data.performance_metrics,
                "agent_logs": dashboard_data.agent_logs[-10:],
                "last_update": dashboard_data.last_update.isoformat()
            }
        }
        await websocket.send_text(json.dumps(initial_data, default=str))
        
        # 保持連接
        while True:
            data = await websocket.receive_text()
            # 處理客戶端消息
            message = json.loads(data)
            
            if message.get("type") == "agent_command":
                # 處理Agent命令
                await handle_agent_command(websocket, message)
                
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)

async def handle_agent_command(websocket: WebSocket, message: Dict):
    """處理Agent命令"""
    try:
        command = message.get("command")
        
        if command == "execute_agent":
            agent_name = message.get("agent_name")
            # 這裡可以調用supervisor執行特定Agent
            response = {
                "type": "command_response",
                "success": True,
                "message": f"Agent {agent_name} 執行完成"
            }
        elif command == "emergency_exit":
            # 緊急退出命令
            response = {
                "type": "command_response", 
                "success": True,
                "message": "緊急退出指令已發送"
            }
        else:
            response = {
                "type": "command_response",
                "success": False,
                "message": f"未知命令: {command}"
            }
        
        await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        error_response = {
            "type": "command_response",
            "success": False,
            "message": f"命令執行失敗: {str(e)}"
        }
        await websocket.send_text(json.dumps(error_response))

@app.get("/api/status")
async def get_system_status():
    """獲取系統狀態API"""
    return {
        "status": "running",
        "last_update": dashboard_data.last_update.isoformat(),
        "system_status": dashboard_data.system_status
    }

@app.get("/api/pools")
async def get_pools():
    """獲取池子數據API"""
    return {
        "pools": dashboard_data.pool_data,
        "count": len(dashboard_data.pool_data),
        "last_update": dashboard_data.last_update.isoformat()
    }

@app.get("/api/positions")
async def get_positions():
    """獲取持倉數據API"""
    return {
        "positions": dashboard_data.positions,
        "count": len(dashboard_data.positions),
        "total_value": sum(pos["liquidity_usd"] for pos in dashboard_data.positions),
        "last_update": dashboard_data.last_update.isoformat()
    }

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
