#!/usr/bin/env python3
"""
DyFlow React UI 啟動腳本
一鍵啟動現代化React WebUI
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def main():
    print("🚀 DyFlow React UI 啟動器")
    print("=" * 50)
    
    # 檢查項目根目錄
    project_root = Path(__file__).parent
    react_ui_path = project_root / "react-ui"
    web_ui_path = project_root / "web_ui"
    
    print(f"📁 項目根目錄: {project_root}")
    print(f"📁 React UI 目錄: {react_ui_path}")
    print(f"📁 Web UI 目錄: {web_ui_path}")
    
    # 檢查React構建文件
    react_build_path = react_ui_path / "dist"
    if react_build_path.exists():
        print("✅ 找到React生產構建文件")
        mode = "production"
    elif (react_ui_path / "test-static.html").exists():
        print("✅ 找到React開發文件")
        mode = "development"
    else:
        print("❌ 未找到React UI文件")
        print("\n請先構建React應用:")
        print("cd react-ui && npm run build")
        return 1
    
    # 檢查FastAPI後端
    react_app_path = web_ui_path / "react_app.py"
    if not react_app_path.exists():
        print("❌ 未找到React UI後端文件")
        return 1
    
    print("✅ 找到React UI後端文件")
    
    # 啟動FastAPI後端
    print(f"\n🌐 啟動DyFlow React UI ({mode}模式)...")
    print("📱 訪問地址: http://localhost:8082")
    print("🔄 實時數據更新: 每15秒")
    print("🔌 WebSocket支持: 已啟用")
    print("\n按 Ctrl+C 停止服務器")
    print("=" * 50)
    
    try:
        # 切換到項目根目錄
        os.chdir(project_root)
        
        # 啟動FastAPI應用
        subprocess.run([
            sys.executable, 
            str(react_app_path)
        ], check=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 用戶中斷，正在停止服務器...")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 啟動失敗: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ 未知錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
