#!/usr/bin/env python3
"""
DyFlow Web UI 啟動腳本
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """安裝Web UI依賴"""
    print("🔧 安裝Web UI依賴...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", "web_ui/requirements.txt"
        ])
        print("✅ 依賴安裝完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依賴安裝失敗: {e}")
        return False

def start_web_ui():
    """啟動Web UI"""
    print("🚀 啟動DyFlow Web UI...")
    print("📱 Dashboard將在 http://localhost:8080 運行")
    print("🔄 實時數據更新頻率: 5秒")
    print("💬 支援WebSocket實時通信")
    print("-" * 50)
    
    try:
        # 切換到web_ui目錄
        os.chdir("web_ui")
        
        # 啟動FastAPI應用
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app:app", 
            "--host", "0.0.0.0",
            "--port", "8080",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Web UI已停止")
    except Exception as e:
        print(f"❌ Web UI啟動失敗: {e}")

def main():
    """主函數"""
    print("=" * 60)
    print("🎯 DyFlow Web UI Dashboard")
    print("=" * 60)
    
    # 檢查目錄結構
    if not Path("web_ui").exists():
        print("❌ web_ui目錄不存在")
        return
    
    if not Path("web_ui/app.py").exists():
        print("❌ web_ui/app.py不存在")
        return
    
    if not Path("web_ui/templates/dashboard.html").exists():
        print("❌ dashboard.html模板不存在")
        return
    
    # 安裝依賴
    if not install_dependencies():
        return
    
    # 啟動Web UI
    start_web_ui()

if __name__ == "__main__":
    main()
