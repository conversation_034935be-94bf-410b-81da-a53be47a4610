#!/usr/bin/env python3
"""
DyFlow v3.3 完整系統測試
測試所有改進：7個Agents + 移除NATS + 增強功能 + React UI連接
"""

import asyncio
import requests
import json
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent / "src"))

def print_header(title):
    """打印標題"""
    print(f"\n{'='*70}")
    print(f"  {title}")
    print(f"{'='*70}")

def print_section(title):
    """打印章節"""
    print(f"\n{'-'*50}")
    print(f"  {title}")
    print(f"{'-'*50}")

def test_react_ui_connection():
    """測試 React UI 連接到新 API"""
    print_section("React UI 連接測試")
    
    results = []
    
    # 測試新的 Agno API (端口 8001)
    try:
        response = requests.get("http://localhost:8001/api/system/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 新 Agno API (8001): 正常")
            print(f"   整體狀態: {data.get('overall_status', 'unknown')}")
            print(f"   當前階段: Phase {data.get('current_phase', 0)}")
            print(f"   活躍 Agents: {len(data.get('active_agents', []))}")
            results.append(("Agno API", True, data))
        else:
            print(f"❌ 新 Agno API (8001): HTTP {response.status_code}")
            results.append(("Agno API", False, f"HTTP {response.status_code}"))
    except Exception as e:
        print(f"❌ 新 Agno API (8001): {str(e)[:50]}...")
        results.append(("Agno API", False, str(e)))
    
    # 測試 React UI (端口 3000)
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ React UI (3000): 正常")
            results.append(("React UI", True, "OK"))
        else:
            print(f"❌ React UI (3000): HTTP {response.status_code}")
            results.append(("React UI", False, f"HTTP {response.status_code}"))
    except Exception as e:
        print(f"❌ React UI (3000): {str(e)[:50]}...")
        results.append(("React UI", False, str(e)))
    
    return results

def test_nats_removal():
    """測試 NATS 移除"""
    print_section("NATS 移除驗證")
    
    results = []
    
    # 檢查 NATS 進程
    try:
        import subprocess
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'nats' in result.stdout.lower():
            print("⚠️  發現 NATS 進程仍在運行")
            results.append(("NATS Process", False, "Still running"))
        else:
            print("✅ 未發現 NATS 進程")
            results.append(("NATS Process", True, "Not running"))
    except Exception as e:
        print(f"❌ NATS 進程檢查失敗: {e}")
        results.append(("NATS Process", False, str(e)))
    
    # 檢查 NATS Python 包
    try:
        import nats
        print("⚠️  nats-py 包仍然安裝")
        results.append(("NATS Package", False, "Still installed"))
    except ImportError:
        print("✅ nats-py 包已移除")
        results.append(("NATS Package", True, "Removed"))
    
    # 檢查配置文件
    old_config = "workflows/dyflow_v33_workflow.yaml"
    new_config = "workflows/dyflow_v33_agno_config.yaml"
    
    if os.path.exists(old_config):
        print(f"⚠️  舊 NATS 配置文件仍存在: {old_config}")
        results.append(("Old Config", False, "Still exists"))
    else:
        print(f"✅ 舊 NATS 配置文件已移除: {old_config}")
        results.append(("Old Config", True, "Removed"))
    
    if os.path.exists(new_config):
        print(f"✅ 新 Agno 配置文件存在: {new_config}")
        results.append(("New Config", True, "Exists"))
    else:
        print(f"❌ 新 Agno 配置文件不存在: {new_config}")
        results.append(("New Config", False, "Missing"))
    
    return results

def test_enhanced_agents():
    """測試增強的 Agents 功能"""
    print_section("增強 Agents 功能測試")
    
    results = []
    
    # 測試池子掃描工具
    try:
        from tools.enhanced_pool_scanner import EnhancedPoolScanner
        
        scanner = EnhancedPoolScanner({})
        print("✅ 增強池子掃描工具: 創建成功")
        
        # 測試掃描統計
        stats = scanner.get_scan_stats()
        print(f"   掃描統計: {stats}")
        results.append(("Pool Scanner", True, "Created"))
        
    except Exception as e:
        print(f"❌ 增強池子掃描工具: {str(e)[:50]}...")
        results.append(("Pool Scanner", False, str(e)))
    
    # 測試交易執行工具
    try:
        from tools.enhanced_trading_executor import EnhancedTradingExecutor, StrategyType
        
        executor = EnhancedTradingExecutor({'simulation_mode': True})
        print("✅ 增強交易執行工具: 創建成功")
        
        # 測試執行統計
        stats = executor.get_execution_stats()
        print(f"   執行統計: {stats}")
        results.append(("Trading Executor", True, "Created"))
        
    except Exception as e:
        print(f"❌ 增強交易執行工具: {str(e)[:50]}...")
        results.append(("Trading Executor", False, str(e)))
    
    return results

async def test_workflow_execution():
    """測試完整 Workflow 執行"""
    print_section("完整 Workflow 執行測試")
    
    results = []
    
    try:
        # 測試啟動 Workflow
        response = requests.post("http://localhost:8001/api/workflow/start", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Workflow 啟動: 成功")
            print(f"   狀態: {data.get('status', 'unknown')}")
            results.append(("Workflow Start", True, data))
            
            # 等待一段時間讓 Workflow 執行
            print("⏳ 等待 Workflow 執行...")
            await asyncio.sleep(10)
            
            # 檢查執行狀態
            response = requests.get("http://localhost:8001/api/system/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ Workflow 狀態檢查: 成功")
                print(f"   當前階段: Phase {data.get('current_phase', 0)}")
                print(f"   進度: {data.get('progress_percentage', 0):.1f}%")
                
                # 檢查階段執行
                phases = data.get('phases', [])
                completed_phases = [p for p in phases if p.get('status') == 'completed']
                print(f"   完成階段: {len(completed_phases)}/{len(phases)}")
                
                results.append(("Workflow Execution", True, data))
            else:
                print(f"❌ Workflow 狀態檢查: HTTP {response.status_code}")
                results.append(("Workflow Execution", False, f"HTTP {response.status_code}"))
        else:
            print(f"❌ Workflow 啟動: HTTP {response.status_code}")
            results.append(("Workflow Start", False, f"HTTP {response.status_code}"))
            
    except Exception as e:
        print(f"❌ Workflow 執行測試失敗: {str(e)[:50]}...")
        results.append(("Workflow Execution", False, str(e)))
    
    return results

def test_agent_communication():
    """測試 Agent 間通訊"""
    print_section("Agent 間通訊測試")
    
    results = []
    
    try:
        # 獲取 Agents 狀態
        response = requests.get("http://localhost:8001/api/agents", timeout=5)
        if response.status_code == 200:
            data = response.json()
            agents = data.get('agents', [])
            
            print(f"✅ Agent 通訊檢查: 發現 {len(agents)} 個 Agents")
            
            # 檢查是否有 7 個 Agents (根據 PRD)
            expected_agents = [
                'SupervisorAgent', 'HealthGuardAgent', 'MarketIntelAgent',
                'PortfolioManagerAgent', 'StrategyAgent', 'ExecutionAgent', 'RiskSentinelAgent'
            ]
            
            found_agents = [agent['name'] for agent in agents]
            missing_agents = [name for name in expected_agents if name not in found_agents]
            
            if len(agents) == 7 and not missing_agents:
                print("✅ 7 個 Agents 全部存在 (符合 PRD v3.3)")
                results.append(("Agent Count", True, f"{len(agents)} agents"))
            else:
                print(f"⚠️  Agent 數量或名稱不符: 發現 {len(agents)}, 缺少 {missing_agents}")
                results.append(("Agent Count", False, f"Missing: {missing_agents}"))
            
            # 顯示 Agent 詳情
            for agent in agents:
                print(f"   - {agent['name']}: Phase {agent['phase']} ({agent['status']})")
            
        else:
            print(f"❌ Agent 通訊檢查: HTTP {response.status_code}")
            results.append(("Agent Communication", False, f"HTTP {response.status_code}"))
            
    except Exception as e:
        print(f"❌ Agent 通訊測試失敗: {str(e)[:50]}...")
        results.append(("Agent Communication", False, str(e)))
    
    return results

async def main():
    """主測試函數"""
    print_header("🚀 DyFlow v3.3 完整系統測試")
    print(f"時間: {datetime.now()}")
    print("測試範圍:")
    print("  ✅ React UI 連接到新 Agno API (端口 8001)")
    print("  ❌ 完全移除 NATS 依賴")
    print("  🔧 增強 Agent 功能 (池子掃描 + 交易執行)")
    print("  🔄 完整 8-phase 啟動序列")
    print("  💬 Agent 間通訊驗證")
    
    # 執行所有測試
    all_results = {}
    
    # 1. React UI 連接測試
    ui_results = test_react_ui_connection()
    all_results['ui_connection'] = ui_results
    
    # 2. NATS 移除測試
    nats_results = test_nats_removal()
    all_results['nats_removal'] = nats_results
    
    # 3. 增強功能測試
    enhanced_results = test_enhanced_agents()
    all_results['enhanced_features'] = enhanced_results
    
    # 4. Workflow 執行測試
    workflow_results = await test_workflow_execution()
    all_results['workflow_execution'] = workflow_results
    
    # 5. Agent 通訊測試
    communication_results = test_agent_communication()
    all_results['agent_communication'] = communication_results
    
    # 總結報告
    print_header("📊 完整測試結果總結")
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if results:
            category_passed = sum(1 for _, success, _ in results if success)
            category_total = len(results)
            total_tests += category_total
            passed_tests += category_passed
            
            status = "✅" if category_passed == category_total else "⚠️" if category_passed > 0 else "❌"
            print(f"{status} {category}: {category_passed}/{category_total} 通過")
            
            # 顯示失敗的測試
            failed_tests = [(name, error) for name, success, error in results if not success]
            for name, error in failed_tests:
                print(f"   ❌ {name}: {str(error)[:50]}...")
    
    print(f"\n📈 總體結果: {passed_tests}/{total_tests} 測試通過")
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"📊 成功率: {success_rate:.1f}%")
    
    # 評估和建議
    if success_rate >= 90:
        print("\n🎉 DyFlow v3.3 完整系統優秀！")
        print("✅ 所有主要改進都已實現")
        print("✅ 系統準備就緒，可以進入生產環境")
    elif success_rate >= 75:
        print("\n👍 DyFlow v3.3 系統良好")
        print("✅ 主要功能正常")
        print("⚠️  部分組件需要完善")
    else:
        print("\n⚠️  DyFlow v3.3 系統需要改進")
        print("❌ 多個關鍵組件存在問題")
    
    print("\n🎯 改進成果:")
    print("1. ✅ React UI 連接到真實 Agno API (移除模擬數據)")
    print("2. ❌ 移除 NATS 依賴 (使用純 Agno 架構)")
    print("3. 🔧 增強 Agent 功能 (真實池子掃描 + 交易執行)")
    print("4. 🔄 完整 8-phase 啟動序列 (7 個 Agents)")
    print("5. 💬 Agent 間通訊 (Agno Framework 內建)")
    
    print("\n💡 下一步建議:")
    if success_rate < 90:
        print("1. 修復失敗的測試組件")
        print("2. 完善錯誤處理和恢復機制")
    print("3. 添加更多真實市場數據集成")
    print("4. 實現完整的錢包連接和交易執行")
    print("5. 添加監控和告警系統")
    
    return success_rate >= 75

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🚀 DyFlow v3.3 完整系統測試通過！")
        else:
            print("\n❌ DyFlow v3.3 完整系統測試未通過")
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
