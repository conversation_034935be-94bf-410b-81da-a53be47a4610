#!/usr/bin/env python3
"""
DyFlow 系統完整測試
測試 Agno Agents 和 UI 運行狀況
"""

import asyncio
import requests
import json
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent / "src"))

def print_header(title):
    """打印標題"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """打印章節"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

def test_ui_services():
    """測試 UI 服務"""
    print_section("UI 服務狀態檢查")
    
    services = [
        ("React UI", "http://localhost:3000", "前端界面"),
        ("後端 API", "http://localhost:8000", "API 服務器"),
        ("系統狀態 API", "http://localhost:8000/api/system/status", "系統狀態"),
        ("健康檢查 API", "http://localhost:8000/api/health", "健康檢查"),
        ("Ollama 服務", "http://localhost:11434/api/version", "AI 模型服務")
    ]
    
    results = []
    
    for name, url, desc in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 運行正常 ({desc})")
                results.append((name, True, None))
            else:
                print(f"❌ {name}: 響應異常 {response.status_code}")
                results.append((name, False, f"HTTP {response.status_code}"))
        except Exception as e:
            print(f"❌ {name}: 連接失敗 - {str(e)[:50]}...")
            results.append((name, False, str(e)))
    
    return results

def test_agno_agents():
    """測試 Agno Agents"""
    print_section("Agno Agents 測試")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 創建 DyFlow 核心 Agents
        agents = {}
        
        # SupervisorAgent
        agents['supervisor'] = Agent(
            name="SupervisorAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow system supervisor.",
                "Coordinate system initialization and phase management.",
                "Provide brief status reports ending with 'Supervisor-Ready'."
            ]
        )
        
        # HealthGuardAgent
        agents['health'] = Agent(
            name="HealthGuardAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow health monitor.",
                "Check system health and component status.",
                "Report health status ending with 'Health-Check-Complete'."
            ]
        )
        
        # MarketIntelAgent
        agents['market'] = Agent(
            name="MarketIntelAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow market intelligence agent.",
                "Analyze DeFi market conditions and pool opportunities.",
                "Provide market insights ending with 'Market-Intel-Ready'."
            ]
        )
        
        # StrategyAgent
        agents['strategy'] = Agent(
            name="StrategyAgent",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow strategy generator.",
                "Create optimal LP strategies based on market conditions.",
                "Generate strategy plans ending with 'Strategy-Generated'."
            ]
        )
        
        print(f"✅ 創建了 {len(agents)} 個 DyFlow Agents")
        
        # 測試每個 Agent
        agent_results = []
        
        for agent_name, agent in agents.items():
            print(f"🔄 測試 {agent_name} Agent...")
            
            try:
                prompt = f"Execute {agent_name} functionality for DyFlow system. Provide brief status."
                response = agent.run(prompt)
                
                if response and response.content:
                    print(f"✅ {agent_name}: 響應成功")
                    print(f"   📝 {response.content[:80]}...")
                    agent_results.append((agent_name, True, response.content))
                else:
                    print(f"❌ {agent_name}: 無響應")
                    agent_results.append((agent_name, False, "No response"))
                    
            except Exception as e:
                print(f"❌ {agent_name}: 執行失敗 - {str(e)[:50]}...")
                agent_results.append((agent_name, False, str(e)))
        
        return agent_results
        
    except Exception as e:
        print(f"❌ Agno Agents 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_workflow_simulation():
    """測試工作流程模擬"""
    print_section("DyFlow Workflow 模擬")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 創建 Workflow 協調器
        coordinator = Agent(
            name="WorkflowCoordinator",
            model=Ollama(id="qwen2.5:3b", host="http://localhost:11434"),
            instructions=[
                "You are the DyFlow workflow coordinator.",
                "Manage the 8-phase startup sequence.",
                "Coordinate between different system components.",
                "Provide phase execution status."
            ]
        )
        
        # 模擬 8-phase 啟動序列
        phases = [
            "Phase 0: System Initialization",
            "Phase 1: Health Check", 
            "Phase 2: UI Startup",
            "Phase 3: Wallet Test",
            "Phase 4: Market Intelligence",
            "Phase 5: Portfolio Management",
            "Phase 6: Strategy Generation",
            "Phase 7: Execution",
            "Phase 8: Risk Monitoring"
        ]
        
        workflow_results = []
        
        for i, phase in enumerate(phases[:5]):  # 測試前5個階段
            print(f"🔄 執行 {phase}...")
            
            try:
                prompt = f"""
                Execute {phase} for DyFlow system:
                
                Current phase: {i}
                Phase description: {phase}
                
                Provide brief execution status and next steps.
                """
                
                response = coordinator.run(prompt)
                
                if response and response.content:
                    print(f"✅ {phase}: 完成")
                    workflow_results.append((phase, True, response.content))
                else:
                    print(f"❌ {phase}: 失敗")
                    workflow_results.append((phase, False, "No response"))
                    
            except Exception as e:
                print(f"❌ {phase}: 異常 - {str(e)[:50]}...")
                workflow_results.append((phase, False, str(e)))
        
        return workflow_results
        
    except Exception as e:
        print(f"❌ Workflow 模擬失敗: {e}")
        return []

def test_api_integration():
    """測試 API 集成"""
    print_section("API 集成測試")
    
    api_tests = []
    
    # 測試系統狀態 API
    try:
        response = requests.get("http://localhost:8000/api/system/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 系統狀態 API: 正常")
            print(f"   📊 狀態: {data.get('status', 'unknown')}")
            api_tests.append(("系統狀態", True, data))
        else:
            print(f"❌ 系統狀態 API: HTTP {response.status_code}")
            api_tests.append(("系統狀態", False, f"HTTP {response.status_code}"))
    except Exception as e:
        print(f"❌ 系統狀態 API: {e}")
        api_tests.append(("系統狀態", False, str(e)))
    
    # 測試健康檢查 API
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康檢查 API: 正常")
            print(f"   🏥 健康狀態: {data.get('status', 'unknown')}")
            api_tests.append(("健康檢查", True, data))
        else:
            print(f"❌ 健康檢查 API: HTTP {response.status_code}")
            api_tests.append(("健康檢查", False, f"HTTP {response.status_code}"))
    except Exception as e:
        print(f"❌ 健康檢查 API: {e}")
        api_tests.append(("健康檢查", False, str(e)))
    
    return api_tests

def main():
    """主測試函數"""
    print_header("🚀 DyFlow 系統完整測試")
    print(f"時間: {datetime.now()}")
    print(f"測試目標: Agno Agents + UI 運行狀況")
    
    # 執行所有測試
    all_results = {}
    
    # 1. UI 服務測試
    ui_results = test_ui_services()
    all_results['ui_services'] = ui_results
    
    # 2. Agno Agents 測試
    agent_results = test_agno_agents()
    all_results['agno_agents'] = agent_results
    
    # 3. Workflow 模擬測試
    workflow_results = test_workflow_simulation()
    all_results['workflow'] = workflow_results
    
    # 4. API 集成測試
    api_results = test_api_integration()
    all_results['api_integration'] = api_results
    
    # 總結報告
    print_header("📊 測試結果總結")
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if results:
            category_passed = sum(1 for _, success, _ in results if success)
            category_total = len(results)
            total_tests += category_total
            passed_tests += category_passed
            
            status = "✅" if category_passed == category_total else "⚠️" if category_passed > 0 else "❌"
            print(f"{status} {category}: {category_passed}/{category_total} 通過")
    
    print(f"\n📈 總體結果: {passed_tests}/{total_tests} 測試通過")
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"📊 成功率: {success_rate:.1f}%")
    
    # 建議
    if success_rate >= 90:
        print("\n🎉 系統運行狀況優秀！")
        print("✅ DyFlow Agno 架構工作正常")
        print("✅ UI 和 API 服務穩定")
        print("✅ Agent 通訊功能正常")
    elif success_rate >= 70:
        print("\n👍 系統運行狀況良好")
        print("⚠️  部分組件需要注意")
    else:
        print("\n⚠️  系統需要修復")
        print("❌ 多個組件存在問題")
    
    print("\n💡 下一步建議:")
    print("1. 檢查失敗的組件並修復")
    print("2. 完善 Agent 間協調機制")
    print("3. 優化 UI 和 API 集成")
    print("4. 實現完整的 8-phase 啟動序列")
    
    return success_rate >= 70

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🚀 DyFlow 系統測試通過！可以繼續開發")
        else:
            print("\n❌ DyFlow 系統測試未通過，需要修復問題")
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
