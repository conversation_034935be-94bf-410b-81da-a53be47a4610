# DyFlow v3.1 项目结构文档

## 项目概述

DyFlow 是一个基于 Agno Framework 的 24/7 自动化单边 LP 策略系统，支持 BSC (PancakeSwap v3) 和 Solana (Meteora DLMM v2) 两条链。

## 统一启动方式

### 主启动器
```bash
# 运行核心系统
python dyflow.py core

# 运行Web UI
python dyflow.py ui

# 运行后端服务
python dyflow.py backend

# 运行测试
python dyflow.py test --type complete

# 查看系统状态
python dyflow.py status

# 查看帮助
python dyflow.py help
```

## 项目结构

```
dyflow_new/
├── dyflow.py                           # 🚀 统一启动器
├── dyflow_main.py                      # 核心系统主程序
├── requirements.txt                    # Python依赖
├── README.md                          # 项目说明
├── PROJECT_STRUCTURE.md              # 本文档
├── TRADING_EXECUTOR_SUMMARY.md       # 交易执行器总结
├── REAL_API_INTEGRATION_SUMMARY.md   # API集成总结
│
├── src/                               # 📦 核心代码
│   ├── agents/                        # 🤖 智能代理
│   │   ├── __init__.py
│   │   ├── base_agent.py              # 基础Agent类
│   │   ├── trading_executor_agent.py  # 交易执行Agent
│   │   ├── planner_agno.py           # 规划Agent
│   │   ├── risk_sentinel_agno.py     # 风险哨兵Agent
│   │   ├── scorer_v2_agno.py         # 评分Agent
│   │   ├── portfolio_agent.py        # 投资组合Agent
│   │   ├── hedge_agent.py            # 对冲Agent
│   │   ├── range_rebalancer_agent.py # 范围重平衡Agent
│   │   ├── data_provider_agent.py    # 数据提供Agent
│   │   ├── pool_picker_agent.py      # 池子选择Agent
│   │   └── exit_handler_agent.py     # 退出处理Agent
│   │
│   ├── tools/                         # 🔧 工具集
│   │   ├── __init__.py
│   │   ├── jupiter_swap_tool.py       # Jupiter交换工具
│   │   ├── meteora_dlmm_tool.py      # Meteora DLMM工具
│   │   ├── pancake_subgraph_tool.py  # PancakeSwap子图工具
│   │   ├── pool_scanner_tool.py      # 池子扫描工具
│   │   ├── pool_scoring_tool.py      # 池子评分工具
│   │   ├── binance_hedge_tool.py     # Binance对冲工具
│   │   ├── supabase_db_tool.py       # Supabase数据库工具
│   │   ├── real_data_provider_tool.py # 真实数据提供工具
│   │   ├── system_health_tool.py     # 系统健康工具
│   │   └── prometheus_metrics_tool.py # Prometheus指标工具
│   │
│   ├── utils/                         # 🛠️ 工具函数
│   │   ├── __init__.py
│   │   ├── config.py                 # 配置管理
│   │   ├── helpers.py                # 辅助函数
│   │   ├── exceptions.py             # 异常定义
│   │   ├── models.py                 # 数据模型
│   │   ├── models_v3.py              # v3数据模型
│   │   ├── strategy_types.py         # 策略类型定义
│   │   └── database.py               # 数据库操作
│   │
│   ├── core/                          # 🏗️ 核心组件
│   │   ├── __init__.py
│   │   ├── agno_scheduler.py         # Agno调度器
│   │   ├── dyflow_agno_scheduler.py  # DyFlow Agno调度器
│   │   └── wallet_manager.py         # 钱包管理器
│   │
│   ├── data_providers/                # 📊 数据提供者
│   │   ├── __init__.py
│   │   ├── base_provider.py          # 基础提供者
│   │   ├── bsc_provider.py           # BSC数据提供者
│   │   └── solana_provider.py        # Solana数据提供者
│   │
│   ├── integrations/                  # 🔗 集成组件
│   │   ├── __init__.py
│   │   ├── meteora.py                # Meteora集成
│   │   └── pancakeswap.py            # PancakeSwap集成
│   │
│   ├── strategies/                    # 📈 策略组件
│   │   ├── __init__.py
│   │   └── focused_pairs_strategy.py # 专注对策略
│   │
│   ├── teams/                         # 👥 团队组件
│   │   ├── __init__.py
│   │   ├── base_team.py              # 基础团队
│   │   ├── lp_monitoring_team.py     # LP监控团队
│   │   ├── lp_adjustment_team.py     # LP调整团队
│   │   └── risk_management_team.py   # 风险管理团队
│   │
│   ├── workflows/                     # 🔄 工作流
│   │   ├── __init__.py
│   │   └── lp_monitoring_workflow.py # LP监控工作流
│   │
│   ├── algorithms/                    # 🧮 算法组件
│   │   ├── __init__.py
│   │   ├── normalization.py          # 标准化算法
│   │   ├── risk_calc.py              # 风险计算
│   │   └── scoring.py                # 评分算法
│   │
│   └── supervisor.py                  # 🎯 主调度器
│
├── config/                            # ⚙️ 配置文件
│   ├── config.yaml                   # 主配置文件
│   ├── default.yaml                  # 默认配置
│   ├── networks.yaml                 # 网络配置
│   ├── pools.yaml                    # 池子配置
│   ├── strategies.yaml               # 策略配置
│   ├── trading_config.py             # 交易配置
│   └── unified_config.py             # 🆕 统一配置管理
│
├── web_ui/                            # 🌐 Web界面
│   ├── unified_app.py                # 🆕 统一Web应用
│   ├── templates/                    # 模板文件
│   └── requirements.txt              # Web UI依赖
│
├── react-ui/                          # ⚛️ React界面
│   ├── src/                          # React源码
│   ├── dist/                         # 构建输出
│   ├── package.json                  # Node.js依赖
│   └── vite.config.js                # Vite配置
│
├── tests/                             # 🧪 测试文件
│   ├── unit/                         # 单元测试
│   ├── integration/                  # 集成测试
│   ├── system/                       # 系统测试
│   ├── test_config.yaml             # 测试配置
│   └── integration_test_framework.py # 测试框架
│
├── examples/                          # 📚 示例代码
│   ├── trading_executor_example.py   # 交易执行器示例
│   └── demo_trading_executor.py      # 交易执行器演示
│
├── scripts/                           # 📜 脚本工具
│   └── cleanup_project.py            # 🆕 项目清理脚本
│
├── docs/                              # 📖 文档
│   ├── API.md                        # API文档
│   ├── DEPLOYMENT.md                 # 部署文档
│   └── TradingExecutorAgent_README.md # 交易执行器文档
│
├── workflows/                         # 🔄 工作流定义
│   ├── low-float.yaml               # 低流通工作流
│   └── low_float_workflow.py        # 低流通工作流实现
│
├── data/                              # 📁 数据目录
│   ├── cache/                        # 缓存数据
│   └── exports/                      # 导出数据
│
└── logs/                              # 📝 日志目录
    └── conport.log                   # 连接端口日志
```

## 核心组件说明

### 1. 统一启动器 (dyflow.py)
- 🎯 **功能**: 统一的项目入口点
- 🚀 **支持模式**: core, ui, backend, test, status
- 📋 **命令**: `python dyflow.py [command] [options]`

### 2. 交易执行系统
- 🤖 **TradingExecutorAgent**: 主要交易执行Agent
- 🔄 **Swap功能**: 支持Jupiter SDK的完整交换功能
- 🎯 **LP策略**: 四种DLMM LP策略部署
- 🔄 **完整循环**: SOL ↔ DLMM LP头寸管理

### 3. Web界面系统
- 🌐 **统一Web应用**: `web_ui/unified_app.py`
- ⚛️ **React界面**: 现代化前端界面
- 📊 **实时数据**: WebSocket实时更新
- 📱 **响应式设计**: 支持移动端

### 4. 配置管理
- ⚙️ **统一配置**: `config/unified_config.py`
- 🔧 **环境变量**: 支持环境变量覆盖
- 📁 **多环境**: 支持开发/测试/生产环境

## 使用指南

### 快速开始
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
export SUPABASE_URL="your_supabase_url"
export SUPABASE_ANON_KEY="your_supabase_key"

# 3. 运行系统
python dyflow.py core
```

### 开发模式
```bash
# 运行测试
python dyflow.py test --type basic

# 启动Web UI
python dyflow.py ui --mode react

# 查看系统状态
python dyflow.py status
```

### 生产部署
```bash
# 使用生产配置
python dyflow.py core --config config/production.yaml

# 后台运行
nohup python dyflow.py core > logs/dyflow.log 2>&1 &
```

## 主要改进

### ✅ 统一入口点
- 替代多个分散的启动脚本
- 统一的命令行界面
- 清晰的模式选择

### ✅ 代码整合
- 清理重复的Web应用
- 整合配置管理
- 统一测试框架

### ✅ 项目结构优化
- 标准化目录结构
- 清理缓存文件
- 备份重要文件

### ✅ 文档完善
- 详细的项目结构说明
- 清晰的使用指南
- 完整的功能文档

## 下一步计划

1. **完善测试覆盖**: 增加更多单元测试和集成测试
2. **性能优化**: 优化数据获取和处理性能
3. **监控增强**: 添加更多系统监控指标
4. **文档完善**: 补充API文档和部署指南
5. **CI/CD集成**: 添加自动化构建和部署流程

## 技术栈

- **后端**: Python 3.8+, FastAPI, AsyncIO
- **前端**: React, TypeScript, Vite
- **数据库**: Supabase (PostgreSQL)
- **区块链**: Solana, BSC
- **AI框架**: Agno Framework, Ollama
- **监控**: Prometheus, Grafana (可选)
- **部署**: Docker, Docker Compose
